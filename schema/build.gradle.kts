import com.google.protobuf.gradle.id
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    kotlin("jvm")

    id("maven-publish")
    id("com.google.protobuf")
}

java.sourceCompatibility = JavaVersion.VERSION_11

dependencies {
    api("com.google.protobuf:protobuf-kotlin")
    api("io.grpc:grpc-kotlin-stub")
    implementation("io.grpc:grpc-protobuf")
}

val protobufVersion: String by extra
val grpcVersion: String by extra
val grpcKotlinVersion: String by extra
protobuf {
    generatedFilesBaseDir = "$projectDir/build/generated"
    tasks.getByName<Delete>("clean") {
        delete(generatedFilesBaseDir)
    }
    protoc {
        artifact = "com.google.protobuf:protoc:$protobufVersion"
    }
    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:$grpcVersion"
        }
        id("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:$grpcKotlinVersion:jdk8@jar"
        }
    }
    generateProtoTasks {
        all().forEach {
            it.plugins {
                id("grpc")
                id("grpckt")
            }
            it.builtins {
                id("kotlin")
            }
        }
    }
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict", "-opt-in=kotlin.RequiresOptIn")
        jvmTarget = "11"
    }
}

publishing {
    publications {
        val versionName: String by rootProject.extra
        create<MavenPublication>("maven") {
            groupId = "${rootProject.group}"
            artifactId = "${rootProject.name}-${project.name}"
            version = versionName

            from(components["java"])
        }
    }
}
