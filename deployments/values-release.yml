environments:
  APM_SERVER_URL: http://fleet.elk.usemultiplier.local:8200
  GRPC_CLIENT_CORE-SERVICE_ADDRESS: dns:///core-service.release.local.usemultiplier.com:9090
  GRPC_CLIENT_CORE-SERVICE_NEGOTIATIONTYPE: TLS
  JAVA_HEAP_MAX_MEM: -Xmx1g
  SENTRY_DSN: https://<EMAIL>/4503968957333504
  SENTRY_ENVIRONMENT: release
  SENTRY_TRACES_SAMPLE_RATE: '1.0'
  SPRING_DATASOURCE_USERNAME: integrationdb
  SPRING_PROFILES_ACTIVE: release
  awslogs-group: /ecs/rel-app-tech-customerIntegrationService-taskDef
  awslogs-stream-prefix: ecs
kind: v2
name: customerIntegrationService
resources:
  cpu: 512
  memory: 2048
taskRoleArn: arn:aws:iam::778085304246:role/rel-app-tech-customerIntegrationService-role
executionRoleArn: arn:aws:iam::778085304246:role/rel-app-tech-customerIntegrationService-role
secrets:
  APM_TOKEN: /monitoring/elasticsearch/apmToken/staging-release
  AWS_REGION: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/aws/region
  AWS_S3_BUCKET: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/aws/sftp-s3bucket
  FEIGN_CLIENT_CONFIG_DOCGENSERVICE_URL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/platform/docgenService/param
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/growthbook/env/key/param
  GRPC_CLIENT_BULK_UPLOAD_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/url/grpc/bulk-upload-service
  GRPC_CLIENT_FIELD_MAPPING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/url/grpc/field-mapping-service
  GRPC_CLIENT_COMPANYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_COMPANY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_CONTRACTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/contractService/param
  GRPC_CLIENT_CONTRACT_OFFBOARDING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/contractOffboardingService/param
  GRPC_CLIENT_CONTRACT_ONBOARDING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/contractOnboardingService/param
  GRPC_CLIENT_CORESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/coreService/param
  GRPC_CLIENT_COUNTRYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/countryService/param
  GRPC_CLIENT_EXPENSESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/expenseService/param
  GRPC_CLIENT_MEMBERSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/memberService/param
  GRPC_CLIENT_PAYROLLSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/payrollService/param
  GRPC_CLIENT_PAYSE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/paySeService/param
  GRPC_CLIENT_PIGEONSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/pigeonService/param
  GRPC_CLIENT_TIMEOFFSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/timeoffService/param
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/orgManagementService/param
  GRPC_CLIENT_PAYABLESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/payableService/param
  GRPC_CLIENT_DOCGEN_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/url/grpc/docgen-service
  INTEGRATION_WEBHOOK_SFTP_API_KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/customerIntegrationService/webhook/sftp/api-key
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/employee/jwt/publickey/release
  PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_CUSTOMER_INTEGRATION_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_DOCGEN_BASEURL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/platform/docgenService/private/param
  PLATFORM_DOCGEN_PUBLICBASEURL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/platform/docgenService/public/param
  PLATFORM_KNIT_API_KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/services/google/apikey/param
  PLATFORM_MERGE-DEV_API-KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/mergedev/api/key/param
  PLATFORM_PIGEON_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/kafka/bootstrapServers/param
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:778085304246:parameter/database/integrationdb/user/password/release
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/customerIntegrationService/db/url/param
