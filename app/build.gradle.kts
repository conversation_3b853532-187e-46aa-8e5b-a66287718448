import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

plugins {
    id("org.springframework.boot")
    id("org.liquibase.gradle")
    id("java")
    id("io.spring.dependency-management")
    kotlin("jvm")
    kotlin("plugin.spring")
    kotlin("plugin.jpa")
    kotlin("plugin.serialization")
    kotlin("plugin.lombok")
    kotlin("kapt")
}

java.sourceCompatibility = JavaVersion.VERSION_17
java.targetCompatibility = JavaVersion.VERSION_17

dependencies {
    implementation("org.postgresql:postgresql")

    implementation(platform("com.netflix.graphql.dgs:graphql-dgs-platform-dependencies"))
    implementation(project(":schema"))

    runtimeOnly("org.codehaus.janino:janino") // Logback conditions
    runtimeOnly("net.logstash.logback:logstash-logback-encoder") // Structured (JSON) logging

    implementation("com.multiplier.platform:spring-starter")
    implementation("com.multiplier.platform.utility:db-updater-client")
    implementation("com.multiplier.common:aws")
    implementation("com.multiplier.messaging:bulk-upload-service")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("com.netflix.graphql.dgs:graphql-dgs-spring-boot-starter")
    implementation("net.devh:grpc-spring-boot-starter")
    implementation("io.sentry:sentry-spring-boot-starter")
    developmentOnly("org.springframework.boot:spring-boot-devtools")
    implementation("org.springframework.kafka:spring-kafka") {
        exclude(group = "com.fasterxml.jackson.module", module = "jackson-module-scala_2.13")
    }
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.18.2")
    implementation("com.multiplier.platform:spring-starter")
    implementation("com.multiplier:core-service-schema")
    implementation("com.multiplier:contract-service-schema")
    implementation("com.multiplier:country-service-schema")
    implementation("com.multiplier:member-service-schema")
    implementation("com.multiplier:payroll-service-schema")
    implementation("com.multiplier:contract-offboarding-service-schema")
    implementation("com.multiplier:customer-integration-service-graph") {
        exclude(module = "snakeyaml")
    }
    implementation("com.multiplier:pigeon-service-schema")
    implementation("com.multiplier:pigeon-service-client")
    implementation("com.multiplier:expense-service-schema")
    implementation("com.multiplier:company-service-schema")
    implementation("com.multiplier:timeoff-service-schema")
    implementation("com.multiplier:payable-service-schema:5.5.465")
    implementation("com.multiplier:org-management-service-schema:1.1.13")
    implementation("com.multiplier:bulk-upload-service-grpc-schema")
    implementation("com.multiplier.grpc:grpc-common")
    implementation("com.multiplier:contract-onboarding-service-schema")
    implementation("com.multiplier:pay-se-schema")
    implementation("com.multiplier:field-mapping-service-grpc-schema")
    implementation("com.multiplier:growthbook-sdk")
    implementation("com.multiplier.platform:spring-jobs-starter")
    implementation("com.multiplier:document-generation-schema")

    implementation("org.liquibase:liquibase-core")
    implementation ("com.vladmihalcea:hibernate-types-60:2.21.1")
    implementation("org.hibernate.orm:hibernate-envers:6.4.10.Final")
    implementation("org.zalando:problem-spring-web:0.29.1")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.5.1")
    implementation("io.jsonwebtoken:jjwt-api")
    implementation("io.jsonwebtoken:jjwt-impl")
    implementation("io.jsonwebtoken:jjwt-jackson")
    implementation("com.github.daniel-shuy:kafka-protobuf-serde")
    implementation("dev.merge:client")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json")
    implementation("io.ktor:ktor-client-core")
    implementation("io.ktor:ktor-serialization-jackson")
    implementation("io.ktor:ktor-client-content-negotiation")
    implementation("io.ktor:ktor-client-okhttp")
    implementation("io.ktor:ktor-client-auth")
    implementation("io.ktor:ktor-client-resources")
    implementation("net.minidev:json-smart")
    implementation("commons-validator:commons-validator")
    implementation("io.github.microutils:kotlin-logging-jvm:3.0.5")
    implementation ("org.yaml:snakeyaml:2.0")
    implementation ("org.apache.commons:commons-text:1.10.0")
    implementation("net.javacrumbs.shedlock:shedlock-core:4.33.0")
    implementation("net.javacrumbs.shedlock:shedlock-spring:4.33.0")
    implementation("net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.33.0")
    implementation("org.apache.poi:poi-ooxml:5.4.0")
    implementation("com.opencsv:opencsv:5.6")

    implementation("io.grpc:grpc-core")
    implementation("io.grpc:grpc-stub")
    implementation("com.google.protobuf:protobuf-java")
    implementation("com.google.protobuf:protobuf-kotlin")

    implementation("com.neovisionaries:nv-i18n:1.28")
    implementation("org.projectlombok:lombok")
    implementation("org.json:json")
    implementation("com.graphql-java:graphql-java-extended-scalars:22.0")
    implementation("com.graphql-java:graphql-java:22.3")
    annotationProcessor("org.projectlombok:lombok")

    implementation("org.mapstruct:mapstruct:1.5.5.Final")
    kapt("org.mapstruct:mapstruct-processor:1.5.5.Final")

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(module = "junit")
        exclude(module = "mockito-core")
    }
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("org.mockito.kotlin:mockito-kotlin:4.1.0")
    testImplementation("org.mockito:mockito-inline:4.6.1")
    testImplementation("com.ninja-squad:springmockk")
    testImplementation("org.springframework.boot:spring-boot-starter-webflux")
    testImplementation("com.h2database:h2")
    testImplementation("com.github.javafaker:javafaker") {
        exclude(module = "snakeyaml")
    }
    testImplementation("org.springframework.kafka:spring-kafka-test") {
        exclude(group = "com.fasterxml.jackson.module", module = "jackson-module-scala_2.13")
    }
    testImplementation("org.jetbrains.kotlin:kotlin-test:1.5.0")
    testImplementation("io.ktor:ktor-client-mock")
    testImplementation("org.mapstruct:mapstruct:1.5.5.Final")
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:kafka")
    testImplementation("org.testcontainers:postgresql")
    testImplementation("io.kotest:kotest-assertions-core")
    testImplementation("io.kotest:kotest-assertions-json")

    liquibaseRuntime("org.postgresql:postgresql")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.4")
    implementation("dev.merge:merge-java-client:1.0.17")
}

if (!project.hasProperty("runList")) {
    project.ext.set("runList", "main")
}

project.ext.set(
    "diffChangelogFile",
    "src/main/resources/liquibase/changelog/" + LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_changelog.xml"
)

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict", "-Xjvm-default=all")
        jvmTarget = "17"
    }
}

tasks.withType<Test> {
    useJUnitPlatform() {
        excludeTags("manual")
    }
    jvmArgs(
        "--add-opens", "java.base/java.util=ALL-UNNAMED",
        "--add-opens", "java.base/java.lang=ALL-UNNAMED"
    )
}

tasks.bootRun {
    val debuggerPort = System.getenv("DEBUGGER_PORT") ?: "5005"

    jvmArgs = listOf(
        // with this, all `.now()` will give UTC time, mitigating the chance of complicated issues
        // e.g. now-utc = 4AM but shedlock locked_until in stage-DB will be 11AM (stage sees it as future) when a Vietnamese (UTC+7) dev runs the job if he doesn't configure this...
        "-Duser.timezone=UTC",

        "-Xdebug",
        "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:$debuggerPort"
    )
}

tasks.getByName("jar") {
    enabled = false
}

allOpen {
    annotation("javax.persistence.Entity")
    annotation("javax.persistence.Embeddable")
    annotation("javax.persistence.MappedSuperclass")
}

kapt {
    keepJavacAnnotationProcessors = true
}
