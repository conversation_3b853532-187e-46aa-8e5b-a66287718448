package com.multiplier.core.common.rest.client.docgen;

import com.multiplier.core.common.rest.client.docgen.model.CreateDocumentRequest;
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse;
import java.net.MalformedURLException;
import java.net.URL;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DocGenRestClientTest {

    @Mock
    private DocGenClient docGenClient;

    @Mock
    private PlatformEndpointConfiguration configuration;

    @InjectMocks
    private DocGenRestClient docGenRestClient;

    @Test
    void getDocument() {
        var response = new DocumentResponse();
        Mockito.when(docGenClient.getDocument(1)).thenReturn(response);

        var result = docGenRestClient.getDocument(1);

        Assertions.assertEquals(result, response);
        Mockito.verify(docGenClient).getDocument(1);
    }

    @Test
    void createDocument() {
        var request = new CreateDocumentRequest();
        var response = new DocumentResponse();
        Mockito.when(docGenClient.createDocument(request)).thenReturn(response);

        var result = docGenRestClient.createDocument(request);

        Assertions.assertEquals(result, response);
        Mockito.verify(docGenClient).createDocument(request);
    }

    @Test
    void replaceDocument() {
        var response = new DocumentResponse();
        Mockito.when(docGenClient.replaceDocument(Mockito.anyLong(), Mockito.any())).thenReturn(response);

        var result = docGenRestClient.replaceDocument(Mockito.anyLong(), Mockito.any());

        Assertions.assertEquals(result, response);
        Mockito.verify(docGenClient).replaceDocument(Mockito.anyLong(), Mockito.any());
    }

    @Test
    void mapInternalURL_nullableViewURL() {
        var response = new DocumentResponse().id(1L);
        Mockito.when(docGenClient.getDocument(1)).thenReturn(response);
        Mockito.when(configuration.publicEndPoint()).thenReturn("http://localhost:9090");
        Mockito.when(configuration.internalEndpoint()).thenReturn("http://localhost:9090");

        var result = docGenRestClient.getDocument(1);

        Assertions.assertEquals(result, response);
        Assertions.assertEquals("http://localhost:9090/documents/1/download", response.internalDownloadURL().toString());
    }

    @Test
    void mapInternalURL_nonNullViewURL() throws MalformedURLException {
        var response = new DocumentResponse().id(1L)
                .viewURL(new URL("http://localhost:8080/some-path?some-query=1"));
        Mockito.when(docGenClient.getDocument(1)).thenReturn(response);
        Mockito.when(configuration.publicEndPoint()).thenReturn("http://localhost:9090");
        Mockito.when(configuration.internalEndpoint()).thenReturn("http://localhost:8080");

        var result = docGenRestClient.getDocument(1);

        Assertions.assertEquals(result, response);
        Assertions.assertEquals("http://localhost:9090/some-path?some-query=1", result.viewURL().toString());
    }


}