[{"target": {"label": "First Name", "key": "eventData.profile.firstName"}, "source": {"label": "First Name", "key": "firstName"}}, {"target": {"label": "Last Name", "key": "eventData.profile.lastName"}, "source": {"label": "Last Name", "key": "lastName"}}, {"target": {"label": "Position", "key": "eventData.profile.designation"}, "source": {"label": "Position", "key": "position"}}, {"target": {"label": "email", "key": "eventData.profile.workEmail"}, "source": {"label": "email", "key": "email"}}, {"target": {"label": "BILLING_RATE", "key": "eventData.compensation.fixed[0].amount"}, "source": {"label": "BILLING_RATE", "key": "COMP_ITEM:BILLING_RATE"}}, {"target": {"label": "basePay", "key": "eventData.compensation.fixed[0].amount"}, "source": {"label": "basePay", "key": "basePay"}}, {"target": {"label": "CURRENCY", "key": "eventData.compensation.fixed[0].currency"}, "source": {"label": "CURRENCY", "key": "COMP_ATTR:CURRENCY"}}, {"target": {"label": "BILLING_FREQUENCY", "key": "eventData.compensation.fixed[0].frequency"}, "source": {"label": "BILLING_FREQUENCY", "key": "COMP_ATTR:BILLING_FREQUENCY"}}, {"target": {"label": "Employee ID", "key": "eventData.profile.id"}, "source": {"label": "Employee ID", "key": "employeeId"}}, {"target": {"label": "Gender", "key": "eventData.profile.gender"}, "source": {"label": "Gender", "key": "gender", "type": "ENUM", "children": [{"source": {"label": "FEMALE", "key": "FEMALE"}, "target": {"label": "Female", "key": "Female"}}, {"source": {"label": "MALE", "key": "MALE"}, "target": {"label": "Male", "key": "Male"}}]}}]