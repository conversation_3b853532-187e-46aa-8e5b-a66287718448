spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:db;DB_CLOSE_DELAY=-1
    username: sa
    password: sa
  task:
    scheduling:
      enabled: false

shedlock:
  enabled: false

jwt:
  public-key: MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETS938PH+qMKnKpr+9Vx987cerlaLM/E2xJxceMi10v0InLlttdEZ0rY6iY+MrHO5tk4XiLoUr9fj92M3NUkr9A==

cloud:
  aws:
    s3:
      enabled: true
      bucket: sftp-multiplier-s3-local
    region:
      static: ap-southeast-1

kafka:
  group-id: customer-integration-test
  bootstrap-servers: localhost:9092

grpc:
  client:
    bulk-upload-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    core-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    member-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    company-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    pigeon-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    contract-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    expense-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    country-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    contract-offboarding-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    contract-onboarding-service:
      address:  dns:///localhost:9091
      negotiationType: PLAINTEXT
    field-mapping-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    timeoff-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    pay-se:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    authority-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    org-management-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    payable-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    docgen-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT

  server:
    port: ${random.int[40000,63000]} # assign a random port
    security:
      enabled: false

platform:
  kafka:
    auto-startup: true
    group-id: customer-integration-test
    bootstrap-servers: localhost:9092
  docgen:
    baseurl: https://docgen.api.acc.staging.usemultiplier.com
    public-baseurl: https://docgen.api.acc.staging.usemultiplier.com
  knit:
    api-key: 123456789
  trinet:
    api-url: https://apiqe1.trinet.com/
  merge-dev:
    api-key: 12345
    enabled: false
    api-url: https://api.merge.dev

positions:
  cache:
    ttl: 86400
  batch:
    size: 3

feign:
  client:
    config:
      docgen-service:
        url: https://docgen.api.acc.staging.usemultiplier.com/v2

pigeon:
  client:
    kafka:
      bootstrap-servers: localhost:9092

growthbook:
  base-url: https://api-growthbook.usemultiplier.cloud
  env-key: test
  refresh-frequency-ms: 15000

scheduler:
  shedlock:
    enabled: false
  receivedUpdateEventsNotificationScheduler:
    enabled: false
  receivedDeleteEventsScheduler:
    enabled: false
  verifyIntegrationCredentialScheduler:
    enabled: false
  timeOffEventsScheduler:
    enabled: false
  eventProcessingScheduler:
    enabled: false
  manualSyncScheduler:
    enabled: false
  receivedCreateEventsScheduler:
    enabled: false
  archiveProcessedEventsScheduler:
    enabled: false
  gpSyncScheduler:
    enabled: false

integration:
  webhook:
    sftp:
      api-key: abc123
