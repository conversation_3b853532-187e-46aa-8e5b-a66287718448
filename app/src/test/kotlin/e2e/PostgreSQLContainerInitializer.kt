package e2e

import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.containers.PostgreSQLContainer

class PostgreSQLContainerInitializer :
    ApplicationContextInitializer<ConfigurableApplicationContext> {

    private val sqlContainer: PostgreSQLContainer<*> = PostgreSQLContainer("postgres:15-alpine")

    init {
        sqlContainer.start()
    }

    override fun initialize(configurableApplicationContext: ConfigurableApplicationContext) {
        TestPropertyValues.of(
                "spring.datasource.driver-class-name=org.postgresql.Driver",
                "spring.datasource.url=" + sqlContainer.jdbcUrl,
                "spring.datasource.jdbcUrl=" + sqlContainer.jdbcUrl,
                "spring.datasource.username=" + sqlContainer.username,
                "spring.datasource.password=" + sqlContainer.password)
            .applyTo(configurableApplicationContext.environment)
    }
}
