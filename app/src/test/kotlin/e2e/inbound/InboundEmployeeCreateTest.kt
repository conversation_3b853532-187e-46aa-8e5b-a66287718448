package e2e.inbound

import com.fasterxml.jackson.core.type.TypeReference
import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.aRandomString
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.sync.DataMapper.Companion.objectMapper
import e2e.*
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.maps.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldBeEmpty
import io.mockk.CapturingSlot
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.web.reactive.function.client.WebClient

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestDataService::class,
    ]
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class InboundEmployeeCreateTest {

    @LocalServerPort
    private var port: Int = 0

    @Value("\${platform.knit.api-key}")
    private lateinit var knitApiKey: String

    @Autowired
    private lateinit var webClientBuilder: WebClient.Builder

    private lateinit var webClient: WebClient

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var jpaReceivedEventRepository: ReceivedEventRepository

    @Autowired
    private lateinit var syncService: SyncService

    @Autowired
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @BeforeEach
    fun bfe() {
        jpaReceivedEventRepository.deleteAll()
        webClient = webClientBuilder.baseUrl("http://localhost:$port").build()
        testInternalRequestService.mockGetBulkOnboardDataSpecs()
        platformEmployeeDataRepository.deleteAll()
    }

    data class InboundTestData(
        val entityId: Long,
        val createdContract: CapturingSlot<BulkContractOnboardingRequest>,
        val integrationId: Long,
    )


    @Test
    fun `zoho - should only create HRIS profile data when there is no compensation data from the external system`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/zoho_initial_sync.json",
            country = "USA",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) {
            it.context shouldBe OnboardingType.HRIS_PROFILE_DATA
            it.data.all["email"] shouldBe "<EMAIL>"
            it.data.all["legalEntityId"] shouldBe data.entityId.toString()
            it.data.all["basePay"].shouldBeEmpty()
        }
    }

    @Test
    fun `bamboo - should create an employee on a RECORD_NEW event`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/bamboo_new_employee.json",
            country = "MYS",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_MYS"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "Kristin"
            contract.data.all["gender"] shouldBe "FEMALE"
        }
    }

    @Test
    fun `new comp schema - should separate into employment and compensation data inputs`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/bamboo_new_employee.json",
            country = "MYS",
            customerMappings = "field_mappings/new_comp_schema_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.compensationData shouldHaveSize 1
            contract.data.compensationData.first()["group"] shouldBe GroupedEmployeeData.Group.COMPENSATION_DATA.name
            contract.data.compensationData.first()["employeeId"] shouldBe "1043"
            contract.data.compensationData.first()["BILLING_RATE"] shouldBe "11340.0"

            contract.data.employeeData.shouldNotBeEmpty()
            contract.data.employeeData["employeeId"] shouldBe "1043"
            contract.data.employeeData["group"] shouldBe GroupedEmployeeData.Group.EMPLOYMENT_DATA.name
        }
    }

    @Test
    fun `multiplier employee sync - should not add event for inputs with unsupported country`() {
        val payload = resourceAsString("sync_events/multiplier_bamboo_new_employee.json")
        val accountToken = aRandomString()
        val companyId = aRandomLong()
        val entityId = aRandomLong()
        val customerMappingsEnabled = true
        val country = "MYS"


        val integrationId = testDataService.insertCompanyIntegration(accountToken, companyId).id
        testInternalRequestService.mockLegalEntity(companyId = companyId, country = country, entityId = entityId)
        testDataService.insertLegalEntityMapping(integrationId!!, entityId, companyId)
        testInternalRequestService.mockValidBulkOnboardingRequest()
        testInternalRequestService.mockCustomerFieldMappingsEnabled(customerMappingsEnabled)
        testInternalRequestService.mockMtmKnitIntegrationId("multiplier-integration-id")
        testInternalRequestService.mockMtmSupportedCountries("IND")
        testInternalRequestService.mockMtmSupportedDepartments("HR, International Sales")
        testInternalRequestService.mockIsMtmIntegration(true)

        postSyncEvent(accountToken, payload, webClient, knitApiKey)

        val events = jpaReceivedEventRepository.findAll()
        events shouldHaveSize 0
    }

    @Test
    fun `multiplier employee sync - should not add event for inputs with unsupported department`() {
        val payload = resourceAsString("sync_events/multiplier_bamboo_new_employee.json")
        val accountToken = aRandomString()
        val companyId = aRandomLong()
        val entityId = aRandomLong()
        val customerMappingsEnabled = true
        val country = "MYS"


        val integrationId = testDataService.insertCompanyIntegration(accountToken, companyId).id
        testInternalRequestService.mockLegalEntity(companyId = companyId, country = country, entityId = entityId)
        testDataService.insertLegalEntityMapping(integrationId!!, entityId, companyId)
        testInternalRequestService.mockValidBulkOnboardingRequest()
        testInternalRequestService.mockCustomerFieldMappingsEnabled(customerMappingsEnabled)
        testInternalRequestService.mockMtmKnitIntegrationId("multiplier-integration-id")
        testInternalRequestService.mockMtmSupportedCountries(country)
        testInternalRequestService.mockMtmSupportedDepartments("HR")
        testInternalRequestService.mockIsMtmIntegration(true)

        postSyncEvent(accountToken, payload, webClient, knitApiKey)

        val events = jpaReceivedEventRepository.findAll()
        events shouldHaveSize 0
    }

    @Test
    fun `multiplier employee sync - should remove compensation data from inputs with multiplier integration id`() {
        testInternalRequestService.mockIsMtmIntegration(true)
        val data = processEvent(
            eventPath = "sync_events/bamboo_new_employee.json",
            country = "MYS",
            customerMappings = "field_mappings/new_comp_schema_mapping.json",
            accountToken = "multiplier-integration-id",
        )
        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.compensationData shouldHaveSize 1
            contract.data.compensationData.first()["group"] shouldBe GroupedEmployeeData.Group.COMPENSATION_DATA.name
            contract.data.compensationData.first()["employeeId"] shouldBe "1043"
            contract.data.compensationData.first()["BILLING_RATE"] shouldBe "1"
            contract.data.compensationData.first()["CURRENCY"] shouldBe "USD"
            contract.data.compensationData.first()["BILLING_FREQUENCY"] shouldBe "MONTHLY"

            contract.data.employeeData.shouldNotBeEmpty()
            contract.data.employeeData["employeeId"] shouldBe "1043"
        }
    }

    @Test
    fun `multiplier employee sync - should remove compensation data from inputs with multiplier email`() {
        testInternalRequestService.mockIsMtmIntegration(true)

        val data = processEvent(
            eventPath = "sync_events/multiplier_bamboo_new_employee.json",
            country = "MYS",
            customerMappings = "field_mappings/new_comp_schema_mapping.json",
        )
        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.compensationData shouldHaveSize 1
            contract.data.compensationData.first()["group"] shouldBe GroupedEmployeeData.Group.COMPENSATION_DATA.name
            contract.data.compensationData.first()["employeeId"] shouldBe "1043"
            contract.data.compensationData.first()["BILLING_RATE"] shouldBe "1"
            contract.data.compensationData.first()["CURRENCY"] shouldBe "USD"
            contract.data.compensationData.first()["BILLING_FREQUENCY"] shouldBe "MONTHLY"

            contract.data.employeeData.shouldNotBeEmpty()
            contract.data.employeeData["employeeId"] shouldBe "1043"
        }
    }

    private fun processEvent(
        eventPath: String,
        country: String,
        customerMappings: String? = null,
        accountToken: String? = aRandomString(),
    ): InboundTestData {
        val payload = resourceAsString(eventPath)
        val companyId = aRandomLong()
        val entityId = aRandomLong()
        val customerMappingsEnabled = customerMappings != null


        val integrationId = testDataService.insertCompanyIntegration(accountToken!!, companyId).id
        testInternalRequestService.mockLegalEntity(companyId = companyId, country = country, entityId = entityId)
        testDataService.insertLegalEntityMapping(integrationId!!, entityId, companyId)
        testInternalRequestService.mockValidBulkOnboardingRequest()
        testInternalRequestService.mockCustomerFieldMappingsEnabled(customerMappingsEnabled)
        testInternalRequestService.mockMtmKnitIntegrationId("multiplier-integration-id")
        testInternalRequestService.mockMtmSupportedCountries(country)
        testInternalRequestService.mockFieldMappingProfile(companyId,entityId,integrationId)
        testInternalRequestService.mockFieldMappingProfileExecute("profile-id", customerMappings?: "field_mappings/employee_firstName_gender_mapping.json",objectMapper.readValue(payload, object : TypeReference<Map<String, Any>>() {}))
        testInternalRequestService.mockMtmSupportedDepartments("HR, International Sales, People & Culture")

        val slot = testInternalRequestService.recordCreatedContracts()

        postSyncEvent(accountToken, payload, webClient, knitApiKey)

        assertSingleReceivedEvent(jpaReceivedEventRepository) { event ->
            event.integrationId shouldBe accountToken
            event.syncDataType shouldBe "employee"
            event.eventType shouldBe EventType.RECORD_NEW
        }

        syncService.processApprovedCreateEvents()

        return InboundTestData(
            entityId = entityId,
            createdContract = slot,
            integrationId = integrationId
        )
    }

    @Test
    fun `KekaHR should create an employee after a completed RECORD_NEW sync`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/kekahr_new_employee.json",
            country = "VNM",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_VNM"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "Keka Firstname"
            contract.data.all["gender"] shouldBe "FEMALE"
        }
    }

        @Test
        fun `ADP WorkForceNow - should create an employee on a RECORD_NEW event`() {
            testInternalRequestService.mockIsMtmIntegration()
            val data = processEvent(
                eventPath = "sync_events/adp_workForceNow_new_employee.json",
                country = "IND",
                customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
            )

            assertSingleCreatedContract(data.createdContract) { contract ->
                contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_IND"
                contract.data.all["templateVersion"] shouldBe "1.0.2"
                contract.data.all["firstName"] shouldBe "Harrison"
                contract.data.all["gender"] shouldBe "MALE"
            }
    }

    @Test
    fun `personio - should create an employee on a RECORD_NEW event`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/personio_new_employee.json",
            country = "CAN",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_CAN"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "Personio Firstname"
            contract.data.all["gender"] shouldBe "MALE"
        }
    }

    @Test
    fun `Hibob should create an employee after a completed RECORD_NEW sync`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/hibob_new_employee.json",
            country = "IND",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_IND"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "John"
            contract.data.all["gender"] shouldBe "MALE"
        }
    }

    @Test
    fun `Namely should create an employee after a completed RECORD_NEW sync`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/namely_new_employee.json",
            country = "IND",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_IND"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "John"
            contract.data.all["gender"] shouldBe "MALE"
        }
    }

    @Test
    fun `Successfactors - should create an employee on a RECORD_NEW event`() {
        testInternalRequestService.mockIsMtmIntegration()
        val data = processEvent(
            eventPath = "sync_events/successfactors_new_employee.json",
            country = "CAN",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_CAN"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "Jacob"
            contract.data.all["gender"] shouldBe "MALE"
        }
    }

    @Test
    fun `Oracle HCM should create an employee after a completed RECORD_NEW sync`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/oracle_hcm_new_employee.json",
            country = "USA",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_USA"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "John"
            contract.data.all["gender"] shouldBe "MALE"
        }
    }

    @Test
    fun `Paychex should create an employee after a completed RECORD_NEW sync`() {
        testInternalRequestService.mockIsMtmIntegration()

        val data = processEvent(
            eventPath = "sync_events/paychex_new_employee.json",
            country = "IND",
            customerMappings = "field_mappings/employee_firstName_gender_mapping.json",
        )

        assertSingleCreatedContract(data.createdContract) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_IND"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "Adwaith"
            contract.data.all["gender"] shouldBe "MALE"
        }
    }
}

