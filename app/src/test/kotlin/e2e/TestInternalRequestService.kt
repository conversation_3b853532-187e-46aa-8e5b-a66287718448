package e2e

import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity.HasCapabilities
import com.multiplier.contract.offboarding.schema.ContractOffboarding
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.fieldmapping.grpc.schema.ExecuteMappingResponse
import com.multiplier.fieldmapping.grpc.schema.ListProfilesResponse
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.integration.sync.DataMapper.Companion.objectMapper
import com.multiplier.integration.aRandomEmail
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.aRandomString
import com.multiplier.integration.adapter.api.*
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.member.schema.EmailAddress
import com.multiplier.member.schema.Member
import com.multiplier.timeoff.schema.*
import com.ninjasquad.springmockk.MockkBean
import io.mockk.*
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class TestInternalRequestService {

    @MockkBean
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockkBean
    lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockkBean
    lateinit var timeoffServiceAdapter: TimeoffServiceAdapter

    @MockkBean
    lateinit var companyServiceAdapter: NewCompanyServiceAdapter

    @MockkBean
    lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @MockkBean
    lateinit var featureFlagService: FeatureFlagService

    @MockkBean
    lateinit var fieldMappingServiceAdapter: FieldMappingServiceAdapter

    @MockkBean
    lateinit var contractOffboardingServiceAdapter: ContractOffBoardingServiceAdapter

    fun mockGetMultiplierContract(
        contractId: Long = aRandomLong(),
        companyId: Long = aRandomLong(),
        memberId: Long = aRandomLong(),
        status: ContractOuterClass.ContractStatus = ContractOuterClass.ContractStatus.ACTIVE,
        workEmail: String = aRandomEmail(),
        position: String = aRandomString(),
        startOn: LocalDate = LocalDate.now(),
        employeeId: String = aRandomString()
    ): ContractOuterClass.Contract {
        val contract = ContractOuterClass.Contract.newBuilder()
            .setId(contractId)
            .setCompanyId(companyId)
            .setMemberId(memberId)
            .setStatus(status)
            .setWorkEmail(workEmail)
            .setPosition(position)
            .setStartOn(startOn.toGrpcDate())
            .setEmployeeId(employeeId)
            .setCountry("Test")
            .build()

        every {
            contractServiceAdapter.findContractByContractId(contractId)
        } returns contract

        return contract
    }

    fun mockGetMultiplierMember(
        memberId: Long = aRandomLong(),
        firstName: String = aRandomString(),
        lastName: String = aRandomString(),
        primaryEmail: String = aRandomEmail(),
    ): Member {
        val member = Member.newBuilder()
            .setId(memberId)
            .setFirstName(firstName)
            .setLastName(lastName)
            .addAllEmails(
                listOf(
                    EmailAddress.newBuilder()
                        .setType("primary")
                        .setEmail(primaryEmail)
                        .build()
                )
            ).build()

        every {
            memberServiceAdapter.findMemberByMemberId(memberId)
        } returns member

        return member
    }

    fun verifyEmployeeIdUpdatedOnContract(contractId: Long, employeeId: String) {
        verify {
            contractServiceAdapter.updateContractEmployeeId(contractId, employeeId)
        }
    }

    fun mockLegalEntity(
        companyId: Long,
        country: String,
        entityId: Long
    ) {
        every {
            companyServiceAdapter.getLegalEntities(companyId)
        } returns listOf(
            CompanyOuterClass.LegalEntity.newBuilder()
                .setId(entityId)
                .setAddress(
                    CompanyOuterClass.Address.newBuilder()
                        .setCountry(country)
                        .build()
                )
                .setCurrencyCode("USD")
                .setStatus(CompanyOuterClass.LegalEntity.LegalEntityStatus.ACTIVE)
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
    }
    fun mockFieldMappingProfile(
        companyId: Long,
        entityId: Long,
        integrationId: Long
    ){
        every {
            fieldMappingServiceAdapter.listProfiles(companyId)
        } returns ListProfilesResponse.newBuilder()
            .addProfiles(
                Profile.newBuilder()
                    .setId("profile-id")
                    .setIsActive(true)
                    .setConfigMap(
                        Struct.newBuilder()
                            .putFields("entityId", Value.newBuilder().setStringValue(entityId.toString()).build())
                            .putFields("integrationId", Value.newBuilder().setStringValue(integrationId.toString()).build())
                            .build()
                    )
                    .build()
            )
            .build()
    }

    fun mockFieldMappingProfileExecute(
        profileId: String,
        ruleJsonPath: String,
        sourceData: Map<String, Any>,
    ){
        val ruleJson = resourceAsString(ruleJsonPath)
        val rules = objectMapper.readValue(ruleJson, object : TypeReference<List<FieldMappingRule>>() {})
        every {
            fieldMappingServiceAdapter.executeMapping(profileId, any<Map<String, Any>>())
        } returns ExecuteMappingResponse.newBuilder()
            .setTransformedData(
                Struct.newBuilder()
                    .putAllFields(rules.associate {
                        it.source.key to Value.newBuilder().apply {
                                val value = getNestedValue(sourceData, it.target.key)
                                when (value) {
                                    is Number -> setNumberValue(value.toDouble())
                                    else -> setStringValue(value?.toString() ?: "")
                                }
                            }.build()
                    })
                    .build()
            )
            .build()
    }

    fun mockValidBulkOnboardingRequest() {
        every {
            contractOnboardingServiceAdapter.validateBulkOnboarding(any())
        } returns emptyList()
    }

    fun recordCreatedContracts(): CapturingSlot<BulkContractOnboardingRequest> {
        val slot = slot<BulkContractOnboardingRequest>()
        every {
            contractOnboardingServiceAdapter.bulkOnboarding(capture(slot))
        } returns listOf(111L)
        return slot
    }

    fun mockDataSpecs(vararg dataspecs: BulkOnboardDataSpec) {
        every {
            contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any())
        } returns dataspecs.toList()
    }

    fun mockValidOffboardingRequest(targetId: Long) {
        every {
            contractOffboardingServiceAdapter.getContractOffboardings(any())
        } returns emptyList()

        every {
            contractOffboardingServiceAdapter.initialiseResignationOffboarding(any())
        } returns mockk<ContractOffboarding>(relaxed = true) {
            every { id } returns targetId
        }
    }

    fun recordDeletedEvents(): CapturingSlot<Long> {
        val slot = slot<Long>()
        every {
            contractOffboardingServiceAdapter.verifyAndCompleteOffboarding(capture(slot))
        } returns mockk<ContractOffboarding>()
        return slot
    }

    fun mockCustomerFieldMappingsEnabled(enabled: Boolean) {
        every {
            featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any())
        } returns enabled
    }
    fun mockMtmKnitIntegrationId(integrationId: String) {
        every {
            featureFlagService.getStringValue(FeatureFlag.MTM_KNIT_INTEGRATION_ID, any())
        } returns integrationId
    }
    fun mockIsMtmIntegration(boolean: Boolean = false) {
        every {
            featureFlagService.isMtmIntegration(any(), any())
        } returns boolean
    }
    fun mockMtmSupportedCountries(country: String) {
        every {
            featureFlagService.getStringValue(FeatureFlag.MTM_SUPPORTED_COUNTRIES, any())
        } returns listOf(country).joinToString(",")
    }
    fun mockMtmSupportedDepartments(departments: String) {
        every {
            featureFlagService.getStringValue(FeatureFlag.MTM_SUPPORTED_DEPARTMENTS, any())
        } returns listOf(departments).joinToString(",")
    }
    fun mockKnitV2DataModelEnabled() {
        every {
            featureFlagService.isKnitDataModelV2(any())
        } returns true
    }

    fun mockInternalTimeoff(
        timeoffTypeKey: String,
        timeoffId: Long,
        contractId: Long,
        status: GrpcTimeOffStatus
    ) {
        every {
            timeoffServiceAdapter.getTimeOffsByContractIds(any())
        } returns GrpcTimeOffs.newBuilder().addAllTimeOffs(
            listOf(
                GrpcTimeOff.newBuilder()
                    .setId(timeoffId)
                    .setContractId(contractId)
                    .setStatus(status)
                    .setTimeOffType(GrpcTimeOffType.newBuilder().setKey(timeoffTypeKey).build())
                    .build()
            )
        ).build()
    }

    fun mockCreateInboundTimeOffAdapterRequest(internalTypeKey: String) {
        every {
            timeoffServiceAdapter.bulkUpsertTimeOffs(any())
        } returns GrpcBulkTimeOffResponse.newBuilder()
            .setSuccess(true)
            .addItems(GrpcBulkResponseItem.newBuilder().setTimeOffId(aRandomLong()).build())
            .build()
        every {
            timeoffServiceAdapter.getCompanyTimeOffTypes(any())
        } returns GrpcCompanyTimeOffTypesResponse.newBuilder()
            .addSystemTimeOffTypes(GrpcTimeOffType.newBuilder().setKey(internalTypeKey).build())
            .build()
    }

    fun mockRevokeTimeOffAdapterRequest(): CapturingSlot<GrpcBulkRevokeTimeOffRequest> {
        val slot = slot<GrpcBulkRevokeTimeOffRequest>()
        every {
            timeoffServiceAdapter.bulkRevokeTimeoffs(capture(slot))
        } returns GrpcEmpty.newBuilder().build()
        return slot
    }

    fun mockGetBulkOnboardDataSpecs() {
        every {
            contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any())
        } returns emptyList()
    }

    fun getNestedValue(data: Map<String, Any>, path: String): Any? {
        var result: Any? = null

        if (data != null && path.isNotEmpty()) {
            val parts = path.split("[", "]", ".").filter { it.isNotBlank() }
            var current: Any? = data
            var validPath = true

            for (part in parts) {
                current = when (current) {
                    is Map<*, *> -> (current as Map<String, Any>)[part]
                    is List<*> -> extractFromListWithIndex(current, part)
                    else -> null
                }

                if (current == null) {
                    validPath = false
                    break
                }
            }

            if (validPath) {
                result = current
            }
        }

        return result
    }
    fun extractFromListWithIndex(list: List<*>, key: String): Any? =
        key.toIntOrNull()?.let { if (it in list.indices) list[it] else null }


    data class FieldMappingRule(
        val source: MappingField,
        val target: MappingField
    )

    data class MappingField(
        val label: String,
        val key: String,
        val type: String? = null,
        val children: List<ChildMapping>? = null
    )

    data class ChildMapping(
        val source: MappingField,
        val target: MappingField
    )
}
