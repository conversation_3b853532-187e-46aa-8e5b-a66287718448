package com.multiplier.integration.rest

import com.multiplier.integration.adapter.SFTPWebhookAdapter
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.rest.model.SFTPWebhookRequest
import com.multiplier.integration.service.BulkModule
import com.multiplier.integration.service.IntegrationInput
import com.multiplier.integration.service.IntegrationOrchestrator
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.http.HttpStatus

@ExtendWith(MockKExtension::class)
class SFTPWebhookControllerTest {

    @MockK
    private lateinit var integrationOrchestrator: IntegrationOrchestrator

    @MockK
    private lateinit var sftpWebhookAdapter: SFTPWebhookAdapter

    @InjectMockKs
    private lateinit var sftpWebhookController: SFTPWebhookController

    @Nested
    inner class HandleFileUpload {

        @Test
        fun `should process valid webhook request successfully`() {
            // Given
            val request = SFTPWebhookRequest(
                fileURI = "/path/to/file.csv",
                bucket = "sftp-bucket",
                uploadedUser = "<EMAIL>"
            )

            val integrationInput = IntegrationInput(
                type = URIType.SFTP,
                uri = request.fileURI,
                companyId = 123L,
                entityId = 456L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
            )

            every { sftpWebhookAdapter.toIntegrationInput(request) } returns integrationInput
            every { integrationOrchestrator.handleDataIngestion(integrationInput) } just runs

            // When
            val response = sftpWebhookController.handleFileUpload(request)

            // Then
            assertThat(response.statusCode).isEqualTo(HttpStatus.NO_CONTENT)
            assertThat(response.body).isNull()

            // Verify interactions
            verify(exactly = 1) { sftpWebhookAdapter.toIntegrationInput(request) }
            verify(exactly = 1) { integrationOrchestrator.handleDataIngestion(integrationInput) }
        }
    }
}
