package com.multiplier.integration.sync.model

import com.multiplier.integration.scheduler.objectMapperForIncomingUpdates
import e2e.resourceAsString
import io.kotest.matchers.nulls.shouldNotBeNull
import org.junit.jupiter.api.Test

class EventDataTest {

    @Test
    fun `should deserialize bamboo_update_employee json into EventData`() {
        val json = resourceAsString("sync_events/bamboo_update_employee.json")
        val node = objectMapperForIncomingUpdates.readTree(json)

        val eventData = objectMapperForIncomingUpdates.treeToValue(node["eventData"], EventData::class.java)

        eventData.shouldNotBeNull()
    }
}