package com.multiplier.integration.graph

import com.multiplier.integration.graphql.FieldMappingMutator
import com.multiplier.integration.service.FieldMappingService
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class FieldMappingMutatorTest {
    @InjectMockKs
    private lateinit var mutator: FieldMappingMutator

    @MockK
    private lateinit var fieldMappingService: FieldMappingService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

}