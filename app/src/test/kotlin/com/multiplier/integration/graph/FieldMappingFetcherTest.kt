package com.multiplier.integration.graph

import com.multiplier.integration.graphql.FieldMappingFetcher
import com.multiplier.integration.repository.model.LegalMappingStatus
import com.multiplier.integration.service.FieldMappingService
import com.multiplier.integration.types.IntegrationEntityMappingStatusOutput
import com.multiplier.integration.types.IntegrationFieldsMappingContractorOutput
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)
class FieldMappingFetcherTest {
    @InjectMockKs
    private lateinit var fetcher: FieldMappingFetcher

    @MockK
    private lateinit var fieldMappingService: FieldMappingService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getIntegrationEntityMappingStatus successfully`() {
        val mockIntegrationId = 1L
        val mockIntegrationEntityMappingStatusOutput = mockk<IntegrationEntityMappingStatusOutput>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
            every { status } returns LegalMappingStatus.FULLY_MAPPED.name
        }

        every { fieldMappingService.getIntegrationLegalEntityMappings(mockIntegrationId) } returns listOf(
            mockIntegrationEntityMappingStatusOutput
        )

        val resp = fetcher.getIntegrationEntityMappingStatus(mockIntegrationId)

        assertEquals(LegalMappingStatus.FULLY_MAPPED.name, resp[0].status)
    }

    @Test
    fun `getIntegrationFieldsMappingContractorProfile successfully`() {
        val mockIntegrationId = 1L
        val mockIntegrationFieldsMappingContractorOutput = mockk<IntegrationFieldsMappingContractorOutput>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
        }

        every { fieldMappingService.getIntegrationFieldsMappingContractorProfile(mockIntegrationId) } returns mockIntegrationFieldsMappingContractorOutput

        val resp = fetcher.getIntegrationFieldsMappingContractorProfile(mockIntegrationId)

        assertEquals(mockIntegrationId, resp.integrationId)
    }
}