package com.multiplier.integration.graph

import com.multiplier.integration.graphql.DevToolMutator
import com.multiplier.integration.scheduler.EORTimeOffScheduler
import com.multiplier.integration.service.DevMigrationService
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class DevToolMutatorTest {

    private lateinit var devMigrationService: DevMigrationService
    private lateinit var eorTimeOffScheduler: EORTimeOffScheduler
    private lateinit var devToolMutator: DevToolMutator

    @BeforeEach
    fun setup() {
        devMigrationService = mockk()
        eorTimeOffScheduler = mockk()
        devToolMutator = DevToolMutator(devMigrationService, eorTimeOffScheduler)
    }

    @Test
    fun `migrateIntegrationIdForContractIntegrationTable should return success response`() = runBlocking {
        coEvery { devMigrationService.migrateIntegrationIdForContractIntegrationTable(any()) } returns listOf(Unit)

        val result = devToolMutator.migrateIntegrationIdToContractIntegrationTable(100)

        assertTrue(result.success)
        assertEquals("Successfully migrate integration id for contract integration table", result.message)
        coVerify { devMigrationService.migrateIntegrationIdForContractIntegrationTable(100) }
    }

    @Test
    fun `migrateIntegrationIdForContractIntegrationTable should return error response on exception`() = runBlocking {
        coEvery { devMigrationService.migrateIntegrationIdForContractIntegrationTable(any()) } throws RuntimeException("Test exception")

        val result = devToolMutator.migrateIntegrationIdToContractIntegrationTable(100)

        assertEquals(false, result.success)
        assertEquals("Test exception", result.message)
        coVerify { devMigrationService.migrateIntegrationIdForContractIntegrationTable(100) }
    }

    @Test
    fun `startEORTimeoffSyncManually should return success response`() = runBlocking {
        coEvery { eorTimeOffScheduler.processEORTimeOffs(any()) } just Runs

        val result = devToolMutator.triggerEORTimeoffSyncManually(100)

        assertTrue(result.success)
        assertEquals("Successfully run timeoff EOR for integrationId: 100", result.message)
        coVerify { eorTimeOffScheduler.processEORTimeOffs(any()) }
    }

    @Test
    fun `startEORTimeoffSyncManually should return error response on exception`() = runBlocking {
        coEvery { eorTimeOffScheduler.processEORTimeOffs(any()) } throws RuntimeException("Test exception")

        val result = devToolMutator.triggerEORTimeoffSyncManually(100)

        assertEquals(false, result.success)
        assertEquals("Test exception", result.message)
        coVerify { eorTimeOffScheduler.processEORTimeOffs(any()) }
    }
}