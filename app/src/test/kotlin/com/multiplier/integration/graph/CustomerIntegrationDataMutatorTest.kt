package com.multiplier.integration.graph

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.integration.graphql.CustomerIntegrationDataMutator
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.types.SyncEORManuallyResult
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.types.TaskResponse
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)
class CustomerIntegrationDataMutatorTest {
    @MockK
    private lateinit var customerIntegrationService: CustomerIntegrationService

    @MockK
    private lateinit var syncRepository: SyncRepository

    @MockK
    private lateinit var integrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var syncService: SyncService

    @MockK
    private lateinit var currentUser: CurrentUser

    @InjectMockKs
    private lateinit var mutator: CustomerIntegrationDataMutator

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `syncEORManually successfully with ops user`() {
        setUpOpsUser()
        every { customerIntegrationService.syncEORManually(any(), any(), true) } returns SyncEORManuallyResult("1", 1, true, "1")
        val resp = mutator.syncEORManually(1)
        assertEquals(true, resp.success)
    }

    private fun setUpOpsUser() {
        val userContext = mockk<UserContext>()
        val userScopes = mockk<UserScopes>()
        every { userScopes.companyId } returns 1
        every { userContext.experience } returns "operations"
        every { userContext.scopes } returns userScopes
        every { currentUser.context } returns userContext
    }

    @Test
    fun `triggerIntegrationSync successfully with ops user`() {
        setUpOpsUser()
        every { customerIntegrationService.triggerIntegrationSync(any(), any(), SyncType.OUTGOING, true) } returns TaskResponse(true, "Successfully triggered sync")
        val resp = mutator.triggerIntegrationSync("ev_Sl7W272taBk3eJPqSU2OUH", null, SyncType.OUTGOING)
        assertEquals(true, resp.success)
    }
}