
import com.google.protobuf.Int64Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.country.schema.Country
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.CountryServiceAdapter
import com.multiplier.integration.adapter.api.DefaultTimeoffServiceAdapter
import com.multiplier.integration.adapter.api.DefaultTriNetAPIAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.PaymentServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.Data
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.trinet.GetAccessTokenResponse
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getEventsWithExperiencePayload
import com.multiplier.integration.mock.getFailedEvents
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockPlatformEmployeeDataWithTriNetData
import com.multiplier.integration.mock.getMockReceivedEvents
import com.multiplier.integration.mockCompanyIntegration
import com.multiplier.integration.mockGrpcContract
import com.multiplier.integration.mockGrpcMember
import com.multiplier.integration.platforms.*
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.ExternalPlatformValuesRepository
import com.multiplier.integration.repository.LeaveTypeMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.ManualSyncRepository
import com.multiplier.integration.repository.PendingEmployeeRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaManualSync
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.ContractsAnalysis
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.FieldMappingService
import com.multiplier.integration.service.GetReceivedEventListRequest
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.RetryReceivedEventRequest
import com.multiplier.integration.service.email.EmailService
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.CustomerIntegration
import com.multiplier.integration.types.NotificationType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.types.SyncStatus
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.types.TaskResponse
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.util.ReflectionTestUtils
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

@ExtendWith(SpringExtension::class)
internal class CustomerIntegrationServiceTest {

    @MockK
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var syncRepository: SyncRepository

    @MockK
    private lateinit var triNetPlatformStrategy: TriNetPlatformStrategy


    @MockK
    private lateinit var platformRepository: PlatformRepository

    @MockK
    private lateinit var providerRepository: ProviderRepository

    @MockK
    private lateinit var hibobHRPlatformStrategy: HibobHRPlatformStrategy

    @MockK
    private lateinit var bambooHRPlatformStrategy: BambooHRPlatformStrategy

    @MockK
    private lateinit var workdayHRPlatformStrategy: WorkdayHRPlatformStrategy

    @MockK
    private lateinit var personioHRPlatformStrategy: PersonioHRPlatformStrategy

    @MockK
    private lateinit var successfactorsHRPlatformStrategy: SuccessFactorsPlatformStrategy

    @MockK
    private lateinit var kekaHRPlatformStrategy: KekaHRPlatformStrategy

    @MockK
    private lateinit var zohoPlatformStrategy: ZohoPlatformStrategy

    @MockK
    private lateinit var adpWorkForceNowPlatformStrategy: ADPWorkForceNowPlatformStrategy
  
    @MockK
    private lateinit var oraclePlatformStrategy: OracleHCMPlatformStrategy

    @MockK
    private lateinit var paychexPlatformStrategy: PaychexPlatformStrategy

    @MockK
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @MockK
    private lateinit var manualSyncRepository: ManualSyncRepository

    @MockK
    private lateinit var knitAdapter: KnitAdapter

    @MockK
    private lateinit var receivedEventRepository: ReceivedEventRepository

    @MockK
    private lateinit var pendingEmployeeRepository: PendingEmployeeRepository

    @MockK
    private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    private lateinit var eventLogService: EventLogService

    @MockK
    private lateinit var eventLogRepository: EventLogRepository

    @MockK
    private lateinit var leaveTypeMappingRepository: LeaveTypeMappingRepository

    @MockK
    private lateinit var triNetAPIAdapter: DefaultTriNetAPIAdapter

    @MockK
    private lateinit var notificationsService: NotificationsService

    @MockK
    private lateinit var newCompanyServiceAdapter: NewCompanyServiceAdapter

    @MockK
    private lateinit var emailService: EmailService

    @MockK(relaxed = true)
    private lateinit var manualSync: JpaManualSync

    @MockK
    private lateinit var memberService: MemberServiceAdapter

    @MockK
    private lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @InjectMockKs
    private lateinit var customerIntegrationService: CustomerIntegrationService

    @MockK
    private lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    private lateinit var externalPlatformValuesRepository: ExternalPlatformValuesRepository

    @MockK
    private lateinit var dataMapper: DataMapper

    @MockK
    private lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockK
    private lateinit var timeoffServiceAdapter: DefaultTimeoffServiceAdapter

    @MockK
    private lateinit var countryServiceAdapter: CountryServiceAdapter

    @MockK
    private lateinit var fieldMappingService: FieldMappingService

    @BeforeEach
    fun setUp() {
        ReflectionTestUtils.setField(customerIntegrationService, "baseUrl", "<Redacted>")
    }

    @Test
    fun `getLatestSyncResultForIntegration returns latest result when sync is not in progress`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.SUCCESS
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns LocalDateTime.now()
        }

        val events = listOf(mockk<JpaEventLog> {
            every { updatedOn } returns LocalDateTime.now()
            every { eventType } returns EventType.SERVICE_INTERNAL_CREATE_CONTRACT
            every { status } returns EventStatus.SUCCESS
            every { contractId } returns 123L
            every { errorMessage } returns null
        })

        val integration = mockCompanyIntegration()

        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(any()) } returns Optional.of(
            latestManualSync
        )
        every { eventLogRepository.findBySyncId("sync-id") } returns events

        // add mock for exporting error report
        every { platformContractIntegrationRepository.findByContractId(contractId = 123L) } returns emptyList()
        every { manualSyncRepository.save(any()) } returns manualSync

        val result =
            customerIntegrationService.getLatestSyncResultForIntegration(integration.id!!, integration.companyId, false)

        assertNotNull(result)
        assertEquals("sync-id", result?.syncId)
        assertEquals(SyncStatus.SUCCESS, result?.status)
    }

    @Test
    fun `getLatestSyncResultForIntegration handles sync already in progress`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.PENDING
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns LocalDateTime.now()
        }

        val events = listOf(mockk<JpaEventLog> {
            every { updatedOn } returns LocalDateTime.now()
            every { eventType } returns EventType.SERVICE_INTERNAL_CREATE_CONTRACT
            every { status } returns EventStatus.TO_BE_PROCESSED
            every { contractId } returns 123L
        })

        val integration = mockCompanyIntegration()

        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(any()) } returns Optional.of(
            latestManualSync
        )
        every { eventLogRepository.findBySyncId("sync-id") } returns events
        every { manualSyncRepository.save(any()) } returns manualSync

        val result =
            customerIntegrationService.getLatestSyncResultForIntegration(integration.id!!, integration.companyId, false)

        assertNotNull(result)
        assertEquals("sync-id", result?.syncId)
        assertEquals(SyncStatus.PENDING, result?.status)
    }

    @Test
    fun `getLatestSyncResultForIntegration handles failed events correctly`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.PENDING
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns LocalDateTime.now()
        }

        val events = listOf(mockk<JpaEventLog> {
            every { updatedOn } returns LocalDateTime.now()
            every { eventType } returns EventType.SERVICE_INTERNAL_CREATE_CONTRACT
            every { status } returns EventStatus.FAILED
            every { contractId } returns 123L
            every { errorMessage } returns "Test Error"
        })

        val integration = mockCompanyIntegration()

        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(any()) } returns Optional.of(
            latestManualSync
        )
        every { eventLogRepository.findBySyncId("sync-id") } returns events
        every { manualSyncRepository.save(any()) } returns manualSync

        val result =
            customerIntegrationService.getLatestSyncResultForIntegration(integration.id!!, integration.companyId, false)

        assertNotNull(result)
        assertEquals("sync-id", result?.syncId)
        assertEquals(SyncStatus.PENDING, result?.status)
    }

    @Test
    fun `getLatestSyncResultForIntegration returns null when no manual sync is found`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.PENDING
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns LocalDateTime.now()
        }

        val events = listOf(mockk<JpaEventLog> {
            every { updatedOn } returns LocalDateTime.now()
            every { eventType } returns EventType.SERVICE_INTERNAL_CREATE_CONTRACT
            every { status } returns EventStatus.FAILED
            every { contractId } returns 123L
            every { errorMessage } returns "Test Error"
        })

        val integration = mockCompanyIntegration()

        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(any()) } returns Optional.empty()
        every { eventLogRepository.findBySyncId("sync-id") } returns events
        every { manualSyncRepository.save(any()) } returns manualSync

        val result =
            customerIntegrationService.getLatestSyncResultForIntegration(integration.id!!, integration.companyId, false)

        assertNull(result)
    }

    @Test
    fun `getCompanyIntegrationByExternalCompanyIdAndPlatformName successfully`() {
        val externalCompanyId = "Test"
        val platformName = "TriNet"

        val platform = JpaPlatform(
            id = 1L,
            isPositionDropdownEnabled = false
        )
        val provider = JpaProvider(
            id = 1L,
            name = ProviderName.KNIT
        )
        val mockCompanyIntegration = getMockCompanyIntegration()

        every { providerRepository.findFirstByName(ProviderName.TRINET) } returns provider
        every {
            platformRepository.findFirstByCategoryAndNameWithCaseInsensitive(
                PlatformCategory.HRIS,
                platformName
            )
        } returns platform
        every {
            companyIntegrationRepository.findByProviderIdAndPlatformIdAndExternalCompanyId(
                providerId = provider.id!!,
                platformId = platform.id!!,
                externalCompanyId
            )
        } returns listOf(mockCompanyIntegration.get())

        val resp = customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
            externalCompanyId,
            platformName
        )

        assertEquals(mockCompanyIntegration.get().companyId, resp.companyId)
    }

    @Test
    fun `getCompanyIntegrationByExternalCompanyIdAndPlatformName failed for empty input`() {
        val externalCompanyId = ""
        val platformName = ""

        val actualException =
            assertFailsWith<IntegrationIllegalArgumentException> {
                customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                    externalCompanyId,
                    platformName
                )
            }
        assertEquals("Neither externalCompanyId nor platformName can be null or blank", actualException.message)
    }

    @Test
    fun `getCompanyIntegrationByExternalCompanyIdAndPlatformName failed for not found provider`() {
        val externalCompanyId = "Test"
        val platformName = "TriNet"

        every { providerRepository.findFirstByName(ProviderName.TRINET) } returns null

        val actualException =
            assertFailsWith<IntegrationIllegalStateException> {
                customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                    externalCompanyId,
                    platformName
                )
            }
        assertEquals("Provider with name TRINET not found", actualException.message)
    }

    @Test
    fun `getCompanyIntegrationByExternalCompanyIdAndPlatformName failed for not found platform`() {
        val externalCompanyId = "Test"
        val platformName = "TriNet"
        val provider = JpaProvider(
            id = 1L,
            name = ProviderName.KNIT
        )

        every { providerRepository.findFirstByName(ProviderName.TRINET) } returns provider
        every {
            platformRepository.findFirstByCategoryAndNameWithCaseInsensitive(
                PlatformCategory.HRIS,
                platformName
            )
        } returns null

        val actualException =
            assertFailsWith<IntegrationIllegalStateException> {
                customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                    externalCompanyId,
                    platformName
                )
            }
        assertEquals("Platform data for TriNet not found", actualException.message)
    }

    @Test
    fun `getCompanyIntegrationByExternalCompanyIdAndPlatformName failed for not found integration`() {
        val externalCompanyId = "Test"
        val platformName = "TriNet"

        val platform = JpaPlatform(
            id = 1L,
            isPositionDropdownEnabled = false
        )
        val provider = JpaProvider(
            id = 1L,
            name = ProviderName.KNIT
        )

        every { providerRepository.findFirstByName(ProviderName.TRINET) } returns provider
        every {
            platformRepository.findFirstByCategoryAndNameWithCaseInsensitive(
                PlatformCategory.HRIS,
                platformName
            )
        } returns platform
        every {
            companyIntegrationRepository.findByProviderIdAndPlatformIdAndExternalCompanyId(
                providerId = provider.id!!,
                platformId = platform.id!!,
                externalCompanyId
            )
        } returns emptyList()

        val actualException =
            assertFailsWith<IntegrationIllegalStateException> {
                customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                    externalCompanyId,
                    platformName
                )
            }
        assertEquals(
            "Not found active company integration with externalCompanyId $externalCompanyId, platformName $platformName",
            actualException.message
        )
    }

    @Test
    fun `exportSyncSummaryResult successfully with failed events`() {
        val contractId = 100L
        val companyId = 1L
        val events = getFailedEvents(eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE, contractId = contractId)
        val mockContractsAnalysis = ContractsAnalysis(1, 1, 1, true)
        val mockListAdmins = listOf(
            CompanyOuterClass.CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val integration = mockCompanyIntegration(
            companyId,
            platform = JpaPlatform(id = 1L, name = "TriNet", isPositionDropdownEnabled = false)
        )

        every { newCompanyServiceAdapter.getCompanyAdmins(any()) } returns mockCompanyUsers
        every {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any(),
                subject = any()
            )
        } returns Unit

        every { contractServiceAdapter.findContractByContractId(any()) } returns mockGrpcContract()
        every { memberService.findMemberByMemberId(any()) } returns mockGrpcMember()


        customerIntegrationService.exportSyncSummaryResult(events, mockContractsAnalysis, integration)

        verify(exactly = 1) {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any(),
                subject = any()
            )
        }
    }

    @Test
    fun `exportSyncSummaryResult successfully with success events`() {
        val contractId = 100L
        val companyId = 1L
        val events = getEventsWithExperiencePayload(
            eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
            contractId = contractId,
            eventStatus = EventStatus.SUCCESS
        )
        val mockListAdmins = listOf(
            CompanyOuterClass.CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val (_, mockPlatformContractIntegration) = getMockContractIntegration(contractId)
        val mockPlatformEmployeeData =
            getMockPlatformEmployeeDataWithTriNetData(mockPlatformContractIntegration.platformEmployeeId)
        val mockContractsAnalysis = ContractsAnalysis(1, 1, 1, true)
        val integration = mockCompanyIntegration(
            companyId,
            platform = JpaPlatform(id = 1L, name = "TriNet", isPositionDropdownEnabled = false)
        )

        every {
            platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(
                contractId,
                integration.provider.id,
                integration.platform.id
            )
        } returns mockPlatformContractIntegration
        every { platformEmployeeDataRepository.findByEmployeeId(mockPlatformContractIntegration.platformEmployeeId) } returns mockPlatformEmployeeData
        every { newCompanyServiceAdapter.getCompanyAdmins(any()) } returns mockCompanyUsers
        every {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any(),
                subject = any()
            )
        } returns Unit

        customerIntegrationService.exportSyncSummaryResult(events, mockContractsAnalysis, integration)

        verify(exactly = 1) { platformEmployeeDataRepository.findByEmployeeId(any()) }
        verify(exactly = 1) {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any(),
                subject = any()
            )
        }
    }

    @Test
    fun `exportSyncSummaryResult successfully with other events`() {
        val contractId = 100L
        val companyId = 1L
        val events = getEventsWithExperiencePayload(
            eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
            contractId = contractId,
            eventStatus = EventStatus.TO_BE_PROCESSED
        )
        val mockContractsAnalysis = ContractsAnalysis(1, 1, 1, true)
        val mockListAdmins = listOf(
            CompanyOuterClass.CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val integration = mockCompanyIntegration(
            companyId,
            platform = JpaPlatform(id = 1L, name = "TriNet", isPositionDropdownEnabled = false)
        )

        customerIntegrationService.exportSyncSummaryResult(events, mockContractsAnalysis, integration)

        verify(exactly = 0) { platformEmployeeDataRepository.findByEmployeeId(any()) }
        verify(exactly = 0) {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any()
            )
        }
    }

    @Test
    fun `exportSyncSummaryResult with not found employee data`() {
        val contractId = 100L
        val companyId = 1L
        val events = getEventsWithExperiencePayload(
            eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
            contractId = contractId,
            eventStatus = EventStatus.SUCCESS
        )
        val (_, mockPlatformContractIntegration) = getMockContractIntegration(contractId)
        val mockContractsAnalysis = ContractsAnalysis(1, 1, 1, true)
        val mockListAdmins = listOf(
            CompanyOuterClass.CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val integration = mockCompanyIntegration(
            companyId,
            platform = JpaPlatform(id = 1L, name = "TriNet", isPositionDropdownEnabled = false)
        )

        every {
            platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(
                contractId,
                integration.provider.id,
                integration.platform.id
            )
        } returns mockPlatformContractIntegration
        every { platformEmployeeDataRepository.findByEmployeeId(mockPlatformContractIntegration.platformEmployeeId) } returns emptyList()

        customerIntegrationService.exportSyncSummaryResult(events, mockContractsAnalysis, integration)

        verify(exactly = 1) { platformEmployeeDataRepository.findByEmployeeId(any()) }
        verify(exactly = 0) {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any()
            )
        }
    }

    @Test
    fun `exportSyncSummaryResult with not found employee contract integration`() {
        val contractId = 100L
        val companyId = 1L
        val events = getEventsWithExperiencePayload(
            eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
            contractId = contractId,
            eventStatus = EventStatus.SUCCESS
        )
        val mockContractsAnalysis = ContractsAnalysis(1, 1, 1, true)
        val mockListAdmins = listOf(
            CompanyOuterClass.CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val integration = mockCompanyIntegration(
            companyId,
            platform = JpaPlatform(id = 1L, name = "TriNet", isPositionDropdownEnabled = false)
        )

        every { platformContractIntegrationRepository.findByContractId(contractId) } returns emptyList()

        customerIntegrationService.exportSyncSummaryResult(events, mockContractsAnalysis, integration)

        verify(exactly = 0) { platformEmployeeDataRepository.findByEmployeeId(any()) }
        verify(exactly = 0) {
            notificationsService.sendingResultEmail(
                mockCompanyUsers,
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any()
            )
        }
    }

    @Test
    fun `dismissSyncResult should return success when dismissal is processed correctly`() {
        val syncId = "validSyncId"
        val companyId = 1L
        val manualSync = mockk<JpaManualSync>(relaxed = true)
        val integration = mockCompanyIntegration(companyId)

        every { manualSyncRepository.findBySyncId(syncId) } returns Optional.of(manualSync)
        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.save(manualSync) } returns manualSync

        // Execute the function under test
        val result = customerIntegrationService.dismissSyncResult(syncId, companyId)

        // Validate the outcomes
        assertEquals(true, result.success)
        assertEquals("Sync result dismissed successfully", result.message)
        verify(exactly = 1) { manualSyncRepository.save(manualSync) }
    }

    @Test
    fun `dismissSyncResult should return error when sync is not found`() {
        val syncId = "nonexistentSyncId"
        val companyId = 1L

        every { manualSyncRepository.findBySyncId(syncId) } returns Optional.empty()

        val result = customerIntegrationService.dismissSyncResult(syncId, companyId)

        assertEquals(false, result.success)
        assertEquals("Manual sync not found for syncId=$syncId", result.message)
    }

    @Test
    fun `dismissSyncResult should return error when integration is not found`() {
        val syncId = "validSyncId"
        val companyId = 1L
        val manualSync = mockk<JpaManualSync>(relaxed = true)

        every { manualSyncRepository.findBySyncId(syncId) } returns Optional.of(manualSync)
        every { companyIntegrationRepository.findById(manualSync.integrationId) } returns Optional.empty()

        val result = customerIntegrationService.dismissSyncResult(syncId, companyId)

        assertEquals(false, result.success)
        assertEquals("Integration not found for integrationId=${manualSync.integrationId}", result.message)
    }

    @Test
    fun `dismissSyncResult should return error when company ID does not match`() {
        val syncId = "validSyncId"
        val companyId = 1L
        val wrongCompanyId = 2L
        val manualSync = mockk<JpaManualSync>(relaxed = true)
        val integration = mockk<JpaCompanyIntegration>(relaxed = true)

        every { manualSync.integrationId } returns 10L
        every { integration.companyId } returns wrongCompanyId
        every { manualSyncRepository.findBySyncId(syncId) } returns Optional.of(manualSync)
        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)

        val result = customerIntegrationService.dismissSyncResult(syncId, companyId)

        assertEquals(false, result.success)
        assertEquals("Integration is not related to company id of current user companyId: 1", result.message)
    }

    @Test
    fun `exportSyncSummaryResult failed for non trinet platform`() {
        val contractId = 100L
        val companyId = 1L
        val events = getFailedEvents(eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE, contractId = contractId)
        val mockContractsAnalysis = ContractsAnalysis(1, 1, 1, true)
        val integration = mockCompanyIntegration(
            companyId,
            platform = JpaPlatform(id = 1L, name = "Hibob", isPositionDropdownEnabled = false)
        )

        customerIntegrationService.exportSyncSummaryResult(events, mockContractsAnalysis, integration)

        verify(exactly = 0) {
            notificationsService.sendingResultEmail(
                any(),
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any()
            )
        }
    }

    @Test
    fun `getLatestSyncResultForIntegration set completed on`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.IN_PROGRESS
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns null
        }

        val events = listOf(mockk<JpaEventLog> {
            every { updatedOn } returns LocalDateTime.now()
            every { eventType } returns EventType.SERVICE_INTERNAL_CREATE_CONTRACT
            every { status } returns EventStatus.FAILED
            every { contractId } returns 123L
            every { errorMessage } returns "Test Error"
        })

        val integration = mockCompanyIntegration()

        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(any()) } returns Optional.of(
            latestManualSync
        )
        every { manualSyncRepository.findByIntegrationIdOrderByCreatedOnDesc(any()) } returns listOf(latestManualSync)

        every { eventLogRepository.findBySyncId("sync-id") } returns events
        every { manualSyncRepository.save(any()) } returns manualSync

        val result =
            customerIntegrationService.getLatestSyncResultForIntegration(integration.id!!, integration.companyId, false)

        assertNotNull(result)
        assertEquals("sync-id", result?.syncId)
        assertEquals(SyncStatus.IN_PROGRESS, result?.status)
        verify(exactly = 0) {
            notificationsService.sendingResultEmail(
                any(),
                templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
                templateParams = any(),
                attachments = any()
            )
        }
    }

    @Test
    fun `getSyncSummaryResultDownloadableFile successfully`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.SUCCESS
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns LocalDateTime.now()
            every { integrationId } returns 1L
        }

        val events = listOf(mockk<JpaEventLog> {
            every { updatedOn } returns LocalDateTime.now()
            every { eventType } returns EventType.SERVICE_INTERNAL_CREATE_CONTRACT
            every { status } returns EventStatus.SUCCESS
            every { contractId } returns 123L
            every { errorMessage } returns "Validation Error"
        })
        val (_, mockPlatformContractIntegration) = getMockContractIntegration(123L)
        val mockPlatformEmployeeData =
            getMockPlatformEmployeeDataWithTriNetData(mockPlatformContractIntegration.platformEmployeeId)
        val integration = mockCompanyIntegration(
            1L,
            platform = JpaPlatform(id = 1L, name = "TriNet", isPositionDropdownEnabled = false)
        )

        every { companyIntegrationRepository.findById(1L) } returns Optional.of(integration)
        every {
            platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(
                123L,
                integration.provider.id,
                integration.platform.id
            )
        } returns mockPlatformContractIntegration
        every { platformEmployeeDataRepository.findByEmployeeId(mockPlatformContractIntegration.platformEmployeeId) } returns mockPlatformEmployeeData
        every { manualSyncRepository.findBySyncId("sync-id") } returns Optional.of(latestManualSync)
        every { eventLogRepository.findBySyncId("sync-id") } returns events
        every { manualSyncRepository.findByIntegrationIdOrderByCreatedOnDesc(any()) } returns listOf(latestManualSync)

        val resp = customerIntegrationService.getSyncSummaryResultDownloadableFile("sync-id", 1L)

        assertEquals(true, resp.isDownloaded)
    }

    @Test
    fun `getSyncSummaryResultDownloadableFile failed with status = false`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.IN_PROGRESS
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns LocalDateTime.now()
        }

        every { manualSyncRepository.findBySyncId("sync-id") } returns Optional.of(latestManualSync)

        val resp = customerIntegrationService.getSyncSummaryResultDownloadableFile("sync-id", 1L)

        assertEquals(false, resp.isDownloaded)
    }

    @Test
    fun `getLatestSyncResultForIntegration set completed on for empty events`() {
        val latestManualSync = mockk<JpaManualSync>(relaxed = true) {
            every { status } returns SyncStatus.IN_PROGRESS
            every { syncId } returns "sync-id"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
            every { completedOn } returns null
        }

        val integration = mockCompanyIntegration()

        every { companyIntegrationRepository.findById(any()) } returns Optional.of(integration)
        every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(any()) } returns Optional.of(
            latestManualSync
        )
        every { eventLogRepository.findBySyncId("sync-id") } returns emptyList()
        every { manualSyncRepository.save(any()) } returns manualSync

        val result =
            customerIntegrationService.getLatestSyncResultForIntegration(integration.id!!, integration.companyId, false)

        assertNotNull(result)
        assertEquals("sync-id", result?.syncId)
        assertEquals(SyncStatus.IN_PROGRESS, result?.status)
        verify(exactly = 1) { manualSyncRepository.save(any()) }
    }

//     @Test
//     fun `getExternalLeaveTypes returns empty list successfully`() {
//         val integration = JpaCompanyIntegration(
//             1L, 2L, JpaProvider(1L, ProviderName.KNIT), JpaPlatform(1L, PlatformCategory.HRIS, "Platform1", true),
//             "token", true, true, true, false, false, null, null, null, null
//         )
//         every { companyIntegrationRepository.findById(1L) } returns Optional.of(integration)
//
//         coEvery {
//             knitAdapter.getLeaveTypes(
//                 any(),
//                 any(),
//             )
//         } returns GetLeaveTypesResponse(success = true, responseCode = 200, errors = "", data = LeaveTypeListResponse(
//             emptyList()
//         ))
//
//         val resp = customerIntegrationService.getExternalLeaveTypes(integration.companyId, integration.id!!)
//         assertEquals(emptyList(), resp)
//     }

//     @Test
//     fun `getExternalLeaveTypes returns valid list successfully`() {
//         val integration = JpaCompanyIntegration(
//             1L, 2L, JpaProvider(1L, ProviderName.KNIT), JpaPlatform(1L, PlatformCategory.HRIS, "Platform1", true),
//             "token", true, true, true, false, false, null, null, null, null
//         )
//         every { companyIntegrationRepository.findById(1L) } returns Optional.of(integration)
//
//         coEvery {
//             knitAdapter.getLeaveTypes(
//                 any(),
//                 any(),
//             )
//         } returns GetLeaveTypesResponse(success = true, responseCode = 200, errors = "", data = LeaveTypeListResponse(
//             leaveTypes = listOf(LeaveType(id = "1", name = "TEST", type = LeaveTypeEnum.VACATION))
//         ))
//
//         val resp = customerIntegrationService.getExternalLeaveTypes(integration.companyId, integration.id!!)
//         assertEquals(resp.size, 1)
//     }

//     @Test
//     fun `getLeaveTypeMappingDefinition successfully`() {
//         val mockJpaLeaveTypesMapping = JpaLeaveTypesMapping(1L, 1L, 1L,1L, 1L)
//         val mockRespList = mutableListOf<JpaLeaveTypesMapping>(mockJpaLeaveTypesMapping)
//         every { leaveTypeMappingRepository.findByCompanyIdAndIntegrationId(any(), any()) } returns mockRespList
//
//         val resp = customerIntegrationService.getLeaveTypeMappingDefinition(1L, 1L)
//         assertEquals(resp.size, 1)
//     }

    @Test
    fun `findCompanyConnectedIntegrationsByCompanyIds should return empty map when companyIds is empty`() {
        val result = customerIntegrationService.findCompanyConnectedIntegrationsByCompanyIds(emptySet())
        assertTrue(result.isEmpty())
    }

    @Test
    fun `findCompanyConnectedIntegrationsByCompanyIds should return map of integrations`() {
        val companyId1 = 1L
        val companyId2 = 2L

        val platform1 = JpaPlatform(1L, PlatformCategory.HRIS, "Platform1", true)
        val platform2 = JpaPlatform(2L, PlatformCategory.HRIS, "Platform2", false)
        val platform3 = JpaPlatform(3L, PlatformCategory.HRIS, "Platform3", true)

        val provider1 = JpaProvider(1L, ProviderName.KNIT)

        val integration1 = JpaCompanyIntegration(
            1L, companyId1, provider1, platform1, "token1", true,
            lastOutgoingSyncTime = LocalDateTime.now(), lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now(), lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now()
        )
        val integration2 = JpaCompanyIntegration(
            2L, companyId1, provider1, platform2, "token2", false,
            lastOutgoingSyncTime = LocalDateTime.now(), lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now(), lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now()
        )
        val integration3 = JpaCompanyIntegration(
            3L, companyId2, provider1, platform3, "token3", true,
            lastOutgoingSyncTime = LocalDateTime.now(), lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now(), lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now()
        )

        every { companyIntegrationRepository.findByCompanyIdIn(setOf(companyId1, companyId2)) } returns (listOf(integration1, integration2, integration3))

        val result = customerIntegrationService.findCompanyConnectedIntegrationsByCompanyIds(setOf(companyId1, companyId2))

        assertEquals(2, result.size)
        assertEquals(2, result[companyId1]?.size)
        assertEquals(1, result[companyId2]?.size)

        val customerIntegrations1 = result[companyId1]
        assertNotNull(customerIntegrations1)
        assertEquals(CustomerIntegration(integration1.id!!, integration1.platform.name, integration1.platform.category, integration1.enabled, integration1.platform.isPositionDropdownEnabled), customerIntegrations1?.get(0))
        assertEquals(CustomerIntegration(integration2.id!!, integration2.platform.name, integration2.platform.category, integration2.enabled, integration2.platform.isPositionDropdownEnabled), customerIntegrations1?.get(1))

        val customerIntegrations2 = result[companyId2]
        assertNotNull(customerIntegrations2)
        assertEquals(CustomerIntegration(integration3.id!!, integration3.platform.name, integration3.platform.category, integration3.enabled, integration3.platform.isPositionDropdownEnabled), customerIntegrations2?.get(0))
    }

    @Nested
    inner class SyncEORManually {

        @Test
        fun `throw exception when company integration not found`() {
            val integrationId = 1L

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.empty()

            assertThrows<IntegrationIllegalStateException> { customerIntegrationService.syncEORManually(integrationId, null) }
        }

        @Test
        fun `throw exception when validateIntegrationCompanyMatch fail`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 2L
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)

            assertThrows<BadRequestException> {
                customerIntegrationService.syncEORManually(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `manual sync is already in progress`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { id } returns 1L
                every { companyId } returns 1L
            }
            val jpaManualSync = mockk<JpaManualSync>(relaxed = true) {
                every { id } returns 1L
                every { status } returns SyncStatus.IN_PROGRESS
            }
            val jpaEventLog = mockk<JpaEventLog>(relaxed = true) {
                every { id } returns 1L
                every { status } returns EventStatus.TO_BE_PROCESSED
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(integrationId) } returns Optional.of(
                jpaManualSync
            )
            every { eventLogRepository.findBySyncId(jpaManualSync.syncId) } returns listOf(jpaEventLog)

            val resp = customerIntegrationService.syncEORManually(integrationId, companyUserId)

            assertEquals(null, resp.syncId)
            assertEquals(integrationId, resp.integrationId)
            assertEquals(false, resp.success)
            assertEquals("Manual sync is already in progress", resp.message)
        }

        @Test
        fun `manually sync`() {
            val integrationId = 1L
            val companyUserId = 1L
            val mockCompany = CompanyOuterClass.Company.newBuilder()
                .setDisplayName("Test")
                .setIsTest(false)
                .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
                .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
                .build()

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { id } returns 1L
                every { companyId } returns 1L
            }
            val jpaManualSync = JpaManualSync(
                syncId = "syncId",
                integrationId = integrationId,
                status = SyncStatus.IN_PROGRESS,
                type = SyncType.MANUAL_OUTGOING,
                startedOn = LocalDateTime.now(),
                completedOn = null,
                dismissedOn = null
            )
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(integrationId) } returns Optional.empty()
            every { contractServiceAdapter.getContractIdsByContractFilters(any(), any()) } returns emptyList()
            every { newCompanyServiceAdapter.getCompanyById(any()) } returns mockCompany
            every { eventLogRepository.saveAll(emptyList()) } returns emptyList()
            every { manualSyncRepository.save(any()) } returns jpaManualSync

            val resp = customerIntegrationService.syncEORManually(integrationId, companyUserId)

            assertEquals(integrationId, resp.integrationId)
            assertEquals(true, resp.success)
            assertEquals("Manual sync started successfully", resp.message)
        }
    }

    @Nested
    inner class GetLatestSyncResultForIntegration {

        @Test
        fun `should export sync result`() {
            val integrationId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { id } returns 1L
                every { companyId } returns 1L
            }
            val jpaManualSync = mockk<JpaManualSync>(relaxed = true) {
                every { id } returns 1L
                every { status } returns SyncStatus.IN_PROGRESS
                every { startedOn } returns LocalDateTime.of(2024,1,1,0,0)
                every { completedOn } returns null
            }
            val jpaEventLog = mockk<JpaEventLog>(relaxed = true) {
                every { id } returns 1L
                every { status } returns EventStatus.SUCCESS
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(integrationId) } returns Optional.of(
                jpaManualSync
            )
            every { eventLogRepository.findBySyncId(jpaManualSync.syncId) } returns listOf(jpaEventLog)
            every { manualSyncRepository.save(any()) } returns jpaManualSync
            every { manualSyncRepository.findByIntegrationIdOrderByCreatedOnDesc(integrationId) } returns listOf(
                jpaManualSync,
                jpaManualSync
            )
            every {
                eventLogRepository.findByCompanyIdAndEventTimeRange(
                    any(), any(), any(), any()
                )
            } returns emptyList()

            val resp = customerIntegrationService.getLatestSyncResultForIntegration(integrationId, null, true)

            assertEquals(SyncStatus.IN_PROGRESS, resp?.status)
        }
    }

    @Nested
    inner class ValidateIntegrationCredentials {

        @Test
        fun `throw exception when company integration not found`() {
            val integrationId = 1L

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.empty()

            assertThrows<IntegrationIllegalStateException> { customerIntegrationService.validateIntegrationCredentials(integrationId, null, false) }
        }

        @Test
        fun `throw exception when validateIntegrationCompanyMatch fail`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 2L
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)

            assertThrows<BadRequestException> {
                customerIntegrationService.validateIntegrationCredentials(
                    integrationId,
                    companyUserId,
                    true
                )
            }
        }

        @Test
        fun `throw exception when integration is not enabled`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { enabled } returns false
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.validateIntegrationCredentials(
                    integrationId,
                    companyUserId,
                    true
                )
            }
        }

        @Test
        fun `throw exception when integration provide name is not TRINET`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { enabled } returns true
                every { provider.name } returns ProviderName.KNIT
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.validateIntegrationCredentials(
                    integrationId,
                    companyUserId,
                    true
                )
            }
        }

        @Test
        fun `validate access token unsuccessfully`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { enabled } returns true
                every { provider.name } returns ProviderName.TRINET
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { triNetAPIAdapter.getAccessToken(any(), any()) } returns null
            every { companyIntegrationRepository.save(jpaCompanyIntegration) } returns jpaCompanyIntegration
            every { notificationsService.sendAdminIntegrationCredentialExpired(any(), any()) } answers {}

            val resp = customerIntegrationService.validateIntegrationCredentials(integrationId, companyUserId, true)

            assertEquals(false, resp.success)
            assertEquals("Invalid or expired credentials", resp.message)
        }

        @Test
        fun `validate access token successfully`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { enabled } returns true
                every { provider.name } returns ProviderName.TRINET
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { triNetAPIAdapter.getAccessToken(any(), any()) } returns GetAccessTokenResponse(
                tokenType = "",
                accessToken = "",
                apiProductList = "",
                expiresIn = ""
            )

            val resp = customerIntegrationService.validateIntegrationCredentials(integrationId, companyUserId, true)

            assertEquals(true, resp.success)
            assertEquals("Integration credentials validated successfully", resp.message)
        }
    }

    @Nested
    inner class GetPositionsForIntegration {

        @Test
        fun `throw exception when company integration not found`() {
            val integrationId = 1L

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.empty()

            assertThrows<IntegrationIllegalStateException> { customerIntegrationService.getPositionsForIntegration(integrationId, null) }
        }

        @Test
        fun `throw exception when validateIntegrationCompanyMatch fail`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 2L
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)

            assertThrows<BadRequestException> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `throw exception when integration provider id is null`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns null
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `throw exception when provider not found`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns 1L
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { providerRepository.findById(1L) } returns Optional.empty()

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `throw exception when provider name is not KNIT`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns 1L
            }
            val jpaProvider = mockk<JpaProvider>(relaxed = true) {
                every { name } returns ProviderName.TRINET
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { providerRepository.findById(1L) } returns Optional.of(jpaProvider)

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `throw exception when integration platform id is not found`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns 1L
                every { platform.id } returns null
            }
            val jpaProvider = mockk<JpaProvider>(relaxed = true) {
                every { name } returns ProviderName.KNIT
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { providerRepository.findById(1L) } returns Optional.of(jpaProvider)

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `throw exception when fail to retrieve positions data`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns 1L
                every { platform.id } returns 1L
            }
            val jpaProvider = mockk<JpaProvider>(relaxed = true) {
                every { name } returns ProviderName.KNIT
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { providerRepository.findById(1L) } returns Optional.of(jpaProvider)
            coEvery { knitAdapter.getPositionsDetails(any(), any()) } returns null

            assertThrows<IntegrationIllegalStateException> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `throw exception when positionsData success is not true`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns 1L
                every { platform.id } returns 1L
            }
            val jpaProvider = mockk<JpaProvider>(relaxed = true) {
                every { name } returns ProviderName.KNIT
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { providerRepository.findById(1L) } returns Optional.of(jpaProvider)
            coEvery { knitAdapter.getPositionsDetails(any(), any()) } returns GetPositionDetailResponse(
                success = false
            )

            assertThrows<Exception> {
                customerIntegrationService.getPositionsForIntegration(
                    integrationId,
                    companyUserId
                )
            }
        }

        @Test
        fun `succeed to retrieve positions data`() {
            val integrationId = 1L
            val companyUserId = 1L

            val jpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { companyId } returns 1L
                every { provider.id } returns 1L
                every { platform.id } returns 1L
            }
            val jpaProvider = mockk<JpaProvider>(relaxed = true) {
                every { name } returns ProviderName.KNIT
            }
            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(jpaCompanyIntegration)
            every { providerRepository.findById(1L) } returns Optional.of(jpaProvider)
            every { countryServiceAdapter.getContractDetailRestriction() } returns
                    listOf(Country.GrpcContractDetailRestriction.newBuilder()
                        .setCountryCode(Country.GrpcCountryCode.NULL_COUNTRY_CODE)
                        .setSearchTerm("CFO")
                        .addAllAlternatives(
                            listOf("Head Of Finance", "Test Alternative")
                        )
                        .build()
                    )
            coEvery { knitAdapter.getPositionsDetails(any(), any()) } returns GetPositionDetailResponse(
                success = true,
                data = Data(
                    positions = listOf(Position(
                        positionId = "",
                        designation = "CFO",
                        department = ""
                    )),
                    workShifts = emptyList()
                )
            )

            val resp = customerIntegrationService.getPositionsForIntegration(
                integrationId,
                companyUserId
            )

            assertEquals(2, resp.size)
            assertEquals("Head Of Finance", resp.get(0).designation)
            assertEquals("Test Alternative", resp.get(1).designation)

            coEvery { knitAdapter.getPositionsDetails(any(), any()) } returns GetPositionDetailResponse(
                success = true,
                data = Data(
                    positions = listOf(Position(
                        positionId = "",
                        designation = "",
                        department = ""
                    )),
                    workShifts = emptyList()
                )
            )

            val resp1 = customerIntegrationService.getPositionsForIntegration(
                integrationId,
                companyUserId
            )

            assertEquals(1, resp1.size)
        }
    }

    @Test
    fun `getPlatformStrategyByCompanyId successfully`() {
        val mockCompanyId = 1L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { platform.name } returns "Hibob"
            every { enabled } returns true
            every { companyId } returns mockCompanyId
            every { platform.category } returns PlatformCategory.HRIS
        }

        every { companyIntegrationRepository.findByCompanyIdIn(setOf(mockCompanyId)) } returns listOf(mockCompanyIntegration)

        val resp = customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId)

        assertEquals("Hibob", resp.second.name)
    }

    @Test
    fun `disconnectCompanyIntegration successfully`() {
        val integrationId = 1L
        val mockCompanyId = 2L
        val mockIntegration = getMockCompanyIntegration()

        every { companyIntegrationRepository.findById(integrationId) } returns mockIntegration
        every { companyIntegrationRepository.save(any()) } returns mock<JpaCompanyIntegration>()
        every { receivedEventRepository.deleteByIntegrationId("Test") } returns 1
        every { syncRepository.deleteByIntegrationId("Test") } returns 1
        every { pendingEmployeeRepository.deleteByIntegrationId("Test") } returns 1
        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns emptyList()
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        every { fieldMappingService.handleFieldMappingsOnDisconnection(integrationId) } returns Unit

        val resp = customerIntegrationService.disconnectCompanyIntegration(integrationId, mockCompanyId, "operations", 1L)

        assertEquals(integrationId, resp.id)
    }

    @Test
    fun `changeSyncState incoming sync disabled successfully`() {
        val integrationId = 1L
        val mockCompanyId = 100L
        val mockIntegration = getMockCompanyIntegration()

        every { companyIntegrationRepository.findById(integrationId) } returns mockIntegration
        every { companyIntegrationRepository.save(any()) } returns mock<JpaCompanyIntegration>()

        val resp = customerIntegrationService.changeSyncState(integrationId, syncType = SyncType.INCOMING, false, mockCompanyId)

        assertEquals(true, resp.success)
    }

    @Test
    fun `triggerIntegrationSync EOR sync successfully`() {
        val mockSyncId = "sr_TKtBOj6M4IAhSbrw1HPMgW"
        val mockReceivedEvents = getMockReceivedEvents(firstName = "\"Britanni\"", lastName = "\"Buchanan\"", birthDate = "null", gender = "null", maritalStatus = "null", status = "null", terminationDate = "\"2024-01-30T00:00:00Z\"", eventType = com.multiplier.integration.repository.model.EventType.RECORD_NEW)

        every { receivedEventRepository.findBySyncIdAndAndProcessed(mockSyncId, true) } returns mockReceivedEvents
        every { receivedEventRepository.saveAll(any<List<JpaReceivedEvent>>()) } returns mockReceivedEvents

        val resp = customerIntegrationService.triggerIntegrationSync(syncId = mockSyncId, syncType = SyncType.INCOMING, isOpsUser = true, eventId = null)

        assertEquals(true, resp.success)

        verify(exactly = 1) { receivedEventRepository.findBySyncIdAndAndProcessed(mockSyncId, true) }
        verify(exactly = 1) { receivedEventRepository.saveAll(any<List<JpaReceivedEvent>>()) }
    }

    @Test
    fun `triggerIntegrationSync EOR sync successfully by eventId`() {
        val mockEventId = "ev_Sl7W272taBk3eJPqSU2OUH"
        val mockReceivedEvents = getMockReceivedEvents(firstName = "\"Britanni\"", lastName = "\"Buchanan\"", birthDate = "null", gender = "null", maritalStatus = "null", status = "null", terminationDate = "\"2024-01-30T00:00:00Z\"", eventType = com.multiplier.integration.repository.model.EventType.RECORD_NEW)

        every { receivedEventRepository.findByEventIdAndAndProcessed(mockEventId, true) } returns Optional.of(mockReceivedEvents[0])
        every { receivedEventRepository.save(any()) } returns mockReceivedEvents[0]

        val resp = customerIntegrationService.triggerIntegrationSync(eventId = mockEventId, syncType = SyncType.INCOMING, isOpsUser = true, syncId = null)

        assertEquals(true, resp.success)
        verify(exactly = 1) { receivedEventRepository.findByEventIdAndAndProcessed(mockEventId, true) }
        verify(exactly = 1) { receivedEventRepository.save(any()) }
    }

    @Test
    fun `triggerIntegrationSync EOR sync failed`() {
        assertThrows<IllegalArgumentException> {
            customerIntegrationService.triggerIntegrationSync(eventId = null, syncType = SyncType.INCOMING, isOpsUser = true, syncId = null)
        }
    }

    @Test
    fun `triggerIntegrationSync GP sync successfully by eventId`() {
        val mockEventId = "ev_Sl7W272taBk3eJPqSU2OUH"
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId(mockEventId) } returns eventLog
        every { eventLogRepository.save(any()) } returns eventLog

        val resp = customerIntegrationService.triggerIntegrationSync(eventId = mockEventId, syncType = SyncType.OUTGOING, isOpsUser = true, syncId = null)

        assertEquals(true, resp.success)
        verify(exactly = 1) { eventLogService.findEventLogByEventId(mockEventId) }
        verify(exactly = 1) { eventLogRepository.save(any()) }
    }

    @Test
    fun `triggerIntegrationSync GP sync failed`() {
        assertThrows<BadRequestException> {
            customerIntegrationService.triggerIntegrationSync(eventId = null, syncType = SyncType.OUTGOING, isOpsUser = true, syncId = null)
        }
    }

    @Test
    fun `getAccountingIntegration successfully`() {
        val mockCompanyId = 1L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { platform.name } returns "xero"
            every { enabled } returns true
            every { platform.category } returns PlatformCategory.ACCOUNTING
            every { companyId } returns mockCompanyId
        }

        every { companyIntegrationRepository.findByCompanyIdIn(setOf(mockCompanyId)) } returns listOf(mockCompanyIntegration)

        val resp = customerIntegrationService.getAccountingIntegrationInfo(mockCompanyId)

        assertEquals("xero", resp.name)
    }

    @Test
    fun `getAccountingIntegration failed`() {
        val mockCompanyId = 1L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { platform.name } returns "hibob"
            every { enabled } returns true
            every { platform.category } returns PlatformCategory.HRIS
            every { companyId } returns mockCompanyId
        }

        every { companyIntegrationRepository.findByCompanyIdIn(setOf(mockCompanyId)) } returns listOf(mockCompanyIntegration)


        assertThrows<IntegrationIllegalStateException>(
            "No active integration found for company id = $mockCompanyId"
        ) {
            val resp = customerIntegrationService.getAccountingIntegrationInfo(mockCompanyId)

        }
    }

    @Test
    fun `getReceivedEventList returns expected events`() {
        val integration = JpaCompanyIntegration(
             1L, 2L, JpaProvider(1L, ProviderName.KNIT), JpaPlatform(1L, PlatformCategory.HRIS, "Platform1", true),
             "token", true, true, true, false, false, false, null, null, null, null
         )
        val request = GetReceivedEventListRequest(
            companyId = 1L,
            platformID = 1,
            syncId = "sync1",
            searchTerm = "",
            startOffset = 0,
            pageSize = 10
        )
        val expectedEvents = listOf(
            JpaReceivedEvent(
                eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
                syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
                integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
                eventType = com.multiplier.integration.repository.model.EventType.RECORD_NEW,
                confirmedByUser = true,
                processed = false,
                id = 1L,
                receivedTime = LocalDateTime.now(),
                identifiervalue = "3277135280330507201",
                syncDataType = "employer",
                data = "",
                errors = null,
            ),
            JpaReceivedEvent(
                eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
                syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
                integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
                eventType = com.multiplier.integration.repository.model.EventType.RECORD_NEW,
                confirmedByUser = true,
                processed = false,
                id = 1L,
                receivedTime = LocalDateTime.now(),
                identifiervalue = "3277135280330507201",
                syncDataType = "employer",
                data = "",
                errors = null,
            )
        )

        every {
            receivedEventRepository.findByIntegrationIdAndSyncIdOrderByCreatedOn(
                any(), any(), any()
            )
        } returns expectedEvents

        every { companyIntegrationRepository.findEnabledIntegrationsByCompanyIdAndPlatformId(any(), any()) } returns listOf(integration)

        val result = customerIntegrationService.getReceivedEventList(request)

        assertEquals(expectedEvents, result)
    }

    @Test
    fun `retryReceivedEvent with eventIdListCommaSeparated updates multiple events`() {
        val eventIds = "event1,event2"
        val request = RetryReceivedEventRequest(eventId = null, syncId = null, eventIdListCommaSeparated = eventIds)
        val event1 = JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = com.multiplier.integration.repository.model.EventType.RECORD_NEW,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = "",
            errors = null,
        )
        val event2 = JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = com.multiplier.integration.repository.model.EventType.RECORD_NEW,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = "",
            errors = null,
        )

        every { receivedEventRepository.findByEventId("event1") } returns event1
        every { receivedEventRepository.findByEventId("event2") } returns event2
        every { receivedEventRepository.save(any()) } returns event1

        val response = customerIntegrationService.retryReceivedEvent(request)

        assertEquals(TaskResponse(true, "Successfully retried received event"), response)
    }
}
