package com.multiplier.integration.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.company.schema.grpc.CompanyOuterClass.*
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity.HasCapabilities
import com.multiplier.contract.offboarding.schema.ContractOffboardingStatus
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.*
import com.multiplier.integration.adapter.model.ContractOffBoardingRequest
import com.multiplier.integration.adapter.util.ContractOnboardingService
import com.multiplier.integration.mock.*
import com.multiplier.integration.platforms.TriNetPlatformStrategy
import com.multiplier.integration.repository.*
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.service.email.EmailService
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.fieldmappings.CompensationSchemaConfigService
import com.multiplier.integration.service.fieldmappings.EmployeeDataFieldMapper
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.model.*
import com.multiplier.integration.types.FetchEmployeesResult
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Member
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirement
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementField
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirements
import graphql.Assert
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.core.env.Environment
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.test.assertFailsWith
import kotlin.test.assertTrue

@ExtendWith(SpringExtension::class)
class SyncServiceTest {
    @MockK
    lateinit var jpaReceivedEventRepository: ReceivedEventRepository

    @MockK
    lateinit var pendingEmployeeRepository: PendingEmployeeRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var syncRepository: SyncRepository

    @MockK
    lateinit var notificationsService: NotificationsService

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var triNetPlatformStrategy: TriNetPlatformStrategy

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var integrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var timeoffEventRepository: TimeoffEventRepository

    @MockK
    lateinit var entityIntegrationRepository: JpaEntityIntegrationRepository

    @MockK
    lateinit var environment: Environment

    @MockK
    lateinit var countryService: CountryServiceAdapter

    @MockK
    lateinit var sentMessageRepository: SentMessageRepository

    @MockK
    lateinit var newKnitAdapter: KnitAdapter

    @MockK
    lateinit var customerIntegrationService: CustomerIntegrationService

    @MockK
    lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var emailService: EmailService

    @MockK
    lateinit var companyService: NewCompanyServiceAdapter

    @MockK
    lateinit var contractOffboardingServiceAdapter: ContractOffBoardingServiceAdapter

    @MockK
    lateinit var dataMapper: DataMapper

    @MockK
    lateinit var expenseServiceAdapter: ExpenseServiceAdapter

    @MockK
    lateinit var expenseProcessorService: ExpenseProcessorService

    @MockK
    lateinit var newCompanyServiceAdapter: NewCompanyServiceAdapter

    @MockK
    lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @MockK
    lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockK
    lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK
    lateinit var employeeDataFieldMapper: EmployeeDataFieldMapper

    @MockK
    lateinit var compensationSchemaConfigService: CompensationSchemaConfigService

    @InjectMockKs
    lateinit var synService: SyncService

    @MockK
    lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    lateinit var featureFlagService: FeatureFlagService

    @MockK
    lateinit var timeoffService: TimeoffService

    @MockK
    lateinit var platformTimeoffIntegrationRepository: PlatformTimeoffIntegrationRepository

    @MockK
    lateinit var contractOnboardingService: ContractOnboardingService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        every {
            employeeDataFieldMapper.mapEmployeeData(any(), any(), any(), any(), any(), any())
        } answers { firstArg() }

        every {
            compensationSchemaConfigService.groupEmployeeData(any(), any())
        } answers {
            GroupedEmployeeData(secondArg())
        }
        every { contractOnboardingService.cleanBulkContractOnboardingRequest(any(), any()) } answers { firstArg() }
        every { contractOnboardingService.cleanBulkContractOnboardingMtmRequest(any(), any()) } answers { firstArg() }
        every { featureFlagService.getStringValue(FeatureFlag.MTM_KNIT_INTEGRATION_ID) } returns "multiplier"
        every { knitAdapter.getHibobEmployeeBankAccounts(any(), any(), any()) } returns emptyList()
    }

    @Test
    fun `should process delete events successfully`() {
        val contractId = 1L
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "\"INACTIVE\"",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val mockInternalEmployeeData = getMockKnitEmployeeData(
            firstName = "Britanni",
            lastName = "Buchanan",
        )
        val terminationDate = LocalDate.of(2024, 1, 30)
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair
        val mockUpdatedPlatformEmployeeData = getMockUpdatedTerminationDatePlatformEmployeeData()
        val mockContractOffboarding =
            getMockContractOffboarding(
                contractId,
                lastWorkingDay = terminationDate,
                status = ContractOffboardingStatus.INITIATED
            )
        val mockUpdateContractOffboarding =
            getMockContractOffboarding(
                contractId,
                lastWorkingDay = terminationDate,
                status = ContractOffboardingStatus.COMPLETED
            )

        every { dataMapper.map(mockReceivedEvents[0]) } returns mockEventData
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { dataMapper.map(mockPlatformEmployeeData[0].employeeData) } returns mockInternalEmployeeData
        every { contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId)) } returns listOf(
            mockContractOffboarding
        )
        every { contractOffboardingServiceAdapter.verifyAndCompleteOffboarding(mockContractOffboarding.id) } returns mockUpdateContractOffboarding
        every { platformEmployeeDataRepository.save(any()) } returns mockUpdatedPlatformEmployeeData

        synService.processDeletedEvent(mockReceivedEvents[0])

        verify(exactly = 1) { contractOffboardingServiceAdapter.verifyAndCompleteOffboarding(mockContractOffboarding.id) }
    }

    @Test
    fun `process delete events failed with getTerminationDate exception`() {
        val contractId = 1L
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "\"INACTIVE\"",
            terminationDate = "\"2024-01-30\""
        )
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val mockInternalEmployeeData = getMockKnitEmployeeData(
            firstName = "Britanni",
            lastName = "Buchanan",
        )
        val terminationDate = LocalDate.of(2024, 1, 30)
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair
        val mockContractOffboarding =
            getMockContractOffboarding(
                contractId,
                lastWorkingDay = terminationDate,
                status = ContractOffboardingStatus.INITIATED
            )

        every { dataMapper.map(mockReceivedEvents[0]) } returns mockEventData
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { dataMapper.map(mockPlatformEmployeeData[0].employeeData) } returns mockInternalEmployeeData
        every { contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId)) } returns listOf(
            mockContractOffboarding
        )

        assertThrows(EntityNotFoundException::class.java) {
            synService.processDeletedEvent(mockReceivedEvents[0])
        }
    }

    @Test
    fun `should init offboarding and complete successfully if not existed offboarding`() {
        val contractId = 1L
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "\"INACTIVE\"",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val mockInternalEmployeeData = getMockKnitEmployeeData(
            firstName = "Britanni",
            lastName = "Buchanan",
        )
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair
        val mockUpdatedPlatformEmployeeData = getMockUpdatedTerminationDatePlatformEmployeeData()
        val terminationDate = LocalDate.of(2024, 1, 30)
        val mockUpdateContractOffboarding =
            getMockContractOffboarding(
                contractId,
                lastWorkingDay = terminationDate,
                status = ContractOffboardingStatus.COMPLETED
            )
        val mockContractOffboarding =
            getMockContractOffboarding(
                contractId,
                lastWorkingDay = terminationDate,
                status = ContractOffboardingStatus.INITIATED
            )

        val initRequest = ContractOffBoardingRequest(
            contractId = contractId,
            lastWorkingDay = terminationDate.toString(),
            terminationReason = "Terminated by external platform"
        )

        every { dataMapper.map(mockReceivedEvents[0]) } returns mockEventData
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { dataMapper.map(mockPlatformEmployeeData[0].employeeData) } returns mockInternalEmployeeData
        every { contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId)) } returns emptyList()
        every { contractOffboardingServiceAdapter.initialiseResignationOffboarding(initRequest) } returns mockContractOffboarding
        every { contractOffboardingServiceAdapter.verifyAndCompleteOffboarding(mockUpdateContractOffboarding.id) } returns mockUpdateContractOffboarding
        every { platformEmployeeDataRepository.save(any()) } returns mockUpdatedPlatformEmployeeData

        synService.processDeletedEvent(mockReceivedEvents[0])

        verify(exactly = 1) { contractOffboardingServiceAdapter.initialiseResignationOffboarding(initRequest) }
        verify(exactly = 1) {
            contractOffboardingServiceAdapter.verifyAndCompleteOffboarding(
                mockUpdateContractOffboarding.id
            )
        }
    }

    @Test
    fun `should throw not found employee data when process delete events`() {
        val contractId = 1L
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "\"INACTIVE\"",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val (mockCompanyIntegration, _) = mockContractIntegrationPair

        every { dataMapper.map(mockReceivedEvents[0]) } returns mockEventData
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns emptyList()


        val actualException =
            assertFailsWith<EntityNotFoundException> {
                synService.processDeletedEvent(mockReceivedEvents[0])
            }

        assertEquals(
            actualException.message,
            "Cache employee data not found for platformEmployeeId=3277135280330507201"
        )
    }

    @Test
    fun `should start manual sync successfully`() {
        val integrationId = 1L
        val manualSyncsInProgress: ConcurrentHashMap<String, Boolean> = ConcurrentHashMap<String, Boolean>()
        val companyIntegration: Optional<JpaCompanyIntegration> = getMockCompanyIntegration()

        every { integrationRepository.findById(integrationId) } returns companyIntegration
        every { integrationRepository.save(any()) } returns companyIntegration.get()
        coEvery {
            knitAdapter.startSync(
                companyIntegration.get().accountToken,
                PlatformCategory.EXPENSES
            )
        } returns "testManualSync"

        synService.setPrivateField("manualSyncsInProgress", manualSyncsInProgress)
        runBlocking {
            synService.startManualSync(1, 100L, PlatformCategory.EXPENSES)
        }
        Assertions.assertTrue(companyIntegration.get().incomingSyncInProgress)
    }

    @Test
    fun `should process empty approved create events`() {
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_NEW,
                any(),
                true,
                false
            )
        } returns getEmptyMockReceivedEvents()

        synService.processApprovedCreateEvents()

        verify(exactly = 0) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should process approved create events`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_NEW,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { environment.matchesProfiles("stage") } returns true
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()

        synService.processApprovedCreateEvents()

        verify(exactly = 2) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should process approved create events with country as custom field`() {
        val mockReceivedEvents = getMockReceivedEventsWithCustomCountry(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_NEW,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { environment.matchesProfiles("stage") } returns true
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()

        synService.processApprovedCreateEvents()

        verify(exactly = 2) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should return false when there is any error during contract creation`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)
        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_NEW,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { environment.matchesProfiles("stage") } returns true
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        justRun { notificationsService.sendingResultEmail(any(), any()) }

        every { newCompanyServiceAdapter.getCompanyAdmins(any()) } returns CompanyUsers.newBuilder()
            .addAllUsers(mockListAdmins).build()

        synService.processApprovedCreateEvents()

        verify(exactly = 2) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should send email when there are validation errors during bulk new record validation`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "null"
        )
        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .build()
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_NEW,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { environment.matchesProfiles("stage") } returns true
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        justRun { notificationsService.sendingResultEmail(any(), any(), any(), any(), any()) }
        every { newCompanyServiceAdapter.getCompanyAdmins(any()) } returns CompanyUsers.newBuilder()
            .addAllUsers(mockListAdmins).build()
        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(any(), any()) } returns emptyList()
        val mockEntityId = 1L

        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setCompanyId(companyId)
                .setId(mockEntityId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
        val mockLegalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns mockEntityId
            every { isEnabled } returns true
        }

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                companyIntegration.get().id!!,
                mockEntityId
            )
        } returns Optional.of(mockLegalEntityMapping)
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf("Test Error")
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns true
        every { jpaReceivedEventRepository.getUnsyncedEntityCountryListByIntegrationId(any()) } returns listOf("CAN")
        every { featureFlagService.isMtmIntegration(any(), any()) } returns false

        synService.processApprovedCreateEvents()
        verify(exactly = 1) { notificationsService.sendingResultEmail(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `should manually Import Employees with static bank details`() {
        val mockReceivedEvents = getMockReceivedEventsWithBankAccounts(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockLegalEntities = GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setCompanyId(100)
                        .setAddress(
                            Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .build()
                )
            )
            .build()
        every { syncRepository.findBySyncId("1") } returns getMockJPASync()
        every { integrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { integrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { environment.matchesProfiles("stage") } returns true
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { companyIntegrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { notificationsService.sendAdminEmployeesImportCompleted(100L, any()) } returns Unit
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_NEW,
                false,
                "1"
            )
        } returns mockReceivedEvents
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_UPDATE,
                false,
                "1"
            )
        } returns listOf()
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns mockLegalEntities.entitiesList
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(1)
        every { pendingEmployeeRepository.save(any()) } answers {
            arg<JpaPendingEmployee>(0)
        }
        every { platformEmployeeDataRepository.save(any()) } answers {
            arg<JpaPlatformEmployeeData>(0)
        }
        every { contractServiceAdapter.findContractByContractId(1) } returns ContractOuterClass.Contract.newBuilder()
            .build()
        every { platformContractIntegrationRepository.save(any()) } answers {
            arg<JpaPlatformContractIntegration>(0)
        }
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.getDefaultInstance()

        synService.manuallyImportEmployees("1", 100L)

        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
        verify(exactly = 1) { companyIntegrationRepository.save(any()) }

    }

    @Test
    fun `should import update event for Employees with bank details dynamic not yet in cache`() {
        val mockReceivedEvents = getMockReceivedEventsWithBankAccounts(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockLegalEntities = GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setCompanyId(100)
                        .setAddress(
                            Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .build()
                )
            )
            .build()
        every { syncRepository.findBySyncId("1") } returns getMockJPASync()
        every { integrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { integrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { environment.matchesProfiles("stage") } returns true
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { companyIntegrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { notificationsService.sendAdminEmployeesImportCompleted(100L, any()) } returns Unit
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_NEW,
                false,
                "1"
            )
        } returns listOf()
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_UPDATE,
                false,
                "1"
            )
        } returns mockReceivedEvents
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns mockLegalEntities.entitiesList
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(1)
        every { pendingEmployeeRepository.save(any()) } answers {
            arg<JpaPendingEmployee>(0)
        }
        every { platformEmployeeDataRepository.save(any()) } answers {
            arg<JpaPlatformEmployeeData>(0)
        }
        every { contractServiceAdapter.findContractByContractId(1) } returns ContractOuterClass.Contract.newBuilder()
            .build()
        every { platformContractIntegrationRepository.save(any()) } answers {
            arg<JpaPlatformContractIntegration>(0)
        }
        val mockPaymentAccountRequirements = IntegrationPaymentAccountRequirements.newBuilder().addRequirements(
            IntegrationPaymentAccountRequirement.newBuilder().addAllAccountRequirements(
                listOf(
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("accountHolderName")
                        .setMappedKey("accountHolderName")
                        .build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("accountNumber")
                        .setMappedKey("accountNumber")
                        .build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("bankName")
                        .setMappedKey("bankName").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("branchName")
                        .setMappedKey("branchName").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("swiftCode")
                        .setMappedKey("swiftCode").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("localBankCode")
                        .setMappedKey("localBankCode").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("ifscCode")
                        .setMappedKey("ifscCode").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("iban")
                        .setMappedKey("iban").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("routingNumber")
                        .setMappedKey("routingNumber").build(),
                    IntegrationPaymentAccountRequirementField.newBuilder()
                        .setRequirementKey("country")
                        .setMappedKey("country").build()
                )
            ).build(),
        ).build()
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns mockPaymentAccountRequirements

        synService.manuallyImportEmployees("1", 100L)

        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
        verify(exactly = 1) { companyIntegrationRepository.save(any()) }
    }

    @Test
    fun `handle bank details update`() {
        val mockReceivedEvents = getMockReceivedEventsWithBankAccounts(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val objectMapper = ObjectMapper().registerKotlinModule()
        val receivedEvent = objectMapper.readTree(mockReceivedEvents.get(0).data)
        val eventDataJsonNode: JsonNode = receivedEvent.get("eventData")
        val eventDataJsonString: String = objectMapper.writeValueAsString(eventDataJsonNode)
        val eventData: EventData = objectMapper.readValue(eventDataJsonString, EventData::class.java)

        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.getDefaultInstance()

        synService.handleBankUpdate(1, eventData, "USA")

        verify(exactly = 1) { memberServiceAdapter.upsertBankDetails(any()) }
    }

    @Test
    fun `handle empty bank details update`() {
        val mockReceivedEvents = getMockReceivedEventsWithBlankBankAccounts(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val objectMapper = ObjectMapper().registerKotlinModule()
        val receivedEvent = objectMapper.readTree(mockReceivedEvents.get(0).data)
        val eventDataJsonNode: JsonNode = receivedEvent.get("eventData")
        val eventDataJsonString: String = objectMapper.writeValueAsString(eventDataJsonNode)
        val eventData: EventData = objectMapper.readValue(eventDataJsonString, EventData::class.java)

        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.getDefaultInstance()

        synService.handleBankUpdate(1, eventData, "USA")

        verify(exactly = 1) { memberServiceAdapter.upsertBankDetails(any()) }
    }

    @Test
    fun `should import Employees with no compensation data`() {
        val mockReceivedEvents = getMockReceivedEventsNoCompensation(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockLegalEntities = GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setCompanyId(100)
                        .setAddress(
                            Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .build()
                )
            )
            .build()
        every { syncRepository.findBySyncId("1") } returns getMockJPASync()
        every { integrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { integrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { environment.matchesProfiles("stage") } returns true
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { companyIntegrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { notificationsService.sendAdminEmployeesImportCompleted(100L, any()) } returns Unit
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_NEW,
                false,
                "1"
            )
        } returns mockReceivedEvents
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_UPDATE,
                false,
                "1"
            )
        } returns listOf()
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns mockLegalEntities.entitiesList
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(1)
        every { pendingEmployeeRepository.save(any()) } answers {
            arg<JpaPendingEmployee>(0)
        }
        every { platformEmployeeDataRepository.save(any()) } answers {
            arg<JpaPlatformEmployeeData>(0)
        }
        every { contractServiceAdapter.findContractByContractId(1) } returns ContractOuterClass.Contract.newBuilder()
            .build()
        every { platformContractIntegrationRepository.save(any()) } answers {
            arg<JpaPlatformContractIntegration>(0)
        }
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.getDefaultInstance()

        synService.manuallyImportEmployees("1", 100L)

        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
        verify(exactly = 1) { companyIntegrationRepository.save(any()) }

    }

    @Test
    fun `should import Employees with bank accounts edge case empty`() {
        val mockReceivedEvents = getMockReceivedEventsWithBankAccountsEmpty(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockLegalEntities = GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setCompanyId(100)
                        .setAddress(
                            Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .build()
                )
            )
            .build()
        every { syncRepository.findBySyncId("1") } returns getMockJPASync()
        every { integrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { integrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { environment.matchesProfiles("stage") } returns true
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { companyIntegrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { notificationsService.sendAdminEmployeesImportCompleted(100L, any()) } returns Unit
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_NEW,
                false,
                "1"
            )
        } returns mockReceivedEvents
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_UPDATE,
                false,
                "1"
            )
        } returns listOf()
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns mockLegalEntities.entitiesList
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(1)
        every { pendingEmployeeRepository.save(any()) } answers {
            arg<JpaPendingEmployee>(0)
        }
        every { platformEmployeeDataRepository.save(any()) } answers {
            arg<JpaPlatformEmployeeData>(0)
        }
        every { contractServiceAdapter.findContractByContractId(1) } returns ContractOuterClass.Contract.newBuilder()
            .build()
        every { platformContractIntegrationRepository.save(any()) } answers {
            arg<JpaPlatformContractIntegration>(0)
        }
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.getDefaultInstance()

        synService.manuallyImportEmployees("1", 100L)

        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
        verify(exactly = 1) { companyIntegrationRepository.save(any()) }

    }

    @Test
    fun `should import Employees with bank accounts edge case null account type`() {
        val mockReceivedEvents = getMockReceivedEventsWithBankAccountsNullAccountType(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockLegalEntities = GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setCompanyId(100)
                        .setAddress(
                            Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .build()
                )
            )
            .build()
        every { syncRepository.findBySyncId("1") } returns getMockJPASync()
        every { integrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { integrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { environment.matchesProfiles("stage") } returns true
        every { countryService.getSupportedCurrencies(any(), any()) } returns getGrpcCurrencyCode()
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                any(),
                any()
            )
        } returns emptyList()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { companyIntegrationRepository.save(any()) } returns getMockCompanyIntegration().get()
        every { notificationsService.sendAdminEmployeesImportCompleted(100L, any()) } returns Unit
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_NEW,
                false,
                "1"
            )
        } returns mockReceivedEvents
        every {
            jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(
                EventType.RECORD_UPDATE,
                false,
                "1"
            )
        } returns listOf()
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns mockLegalEntities.entitiesList
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(1)
        every { pendingEmployeeRepository.save(any()) } answers {
            arg<JpaPendingEmployee>(0)
        }
        every { platformEmployeeDataRepository.save(any()) } answers {
            arg<JpaPlatformEmployeeData>(0)
        }
        every { contractServiceAdapter.findContractByContractId(1) } returns ContractOuterClass.Contract.newBuilder()
            .build()
        every { platformContractIntegrationRepository.save(any()) } answers {
            arg<JpaPlatformContractIntegration>(0)
        }
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.getDefaultInstance()

        synService.manuallyImportEmployees("1", 100L)

        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
        verify(exactly = 1) { companyIntegrationRepository.save(any()) }

    }

    @Test
    fun `should get employees to sync`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\"",
            eventType = EventType.RECORD_NEW
        )
        every { integrationRepository.findById(1) } returns getMockCompanyIntegration()
        every { syncRepository.findSyncOrderByStartTime("Test") } returns listOf(getMockJPASync().get())
        every {
            jpaReceivedEventRepository.setMatchingSyncToProvidedSyncForUnprocessedEventsForGivenIntegration(
                "Test",
                "Test"
            )
        } returns 0
        every { jpaReceivedEventRepository.findBySyncIdAndAndProcessed("Test", false) } returns mockReceivedEvents
        every { integrationRepository.save(any()) } returns mockk()

        val fetchEmployeesResult: FetchEmployeesResult = synService.getEmployeesToSync(1, 100L)

        Assertions.assertTrue(fetchEmployeesResult.employees.size == 1)
    }

    @Test
    fun `should send Invites`() {
        val response: BulkContract.InviteBulkMemberContractsResponse =
            BulkContract.InviteBulkMemberContractsResponse.getDefaultInstance()

        every { syncRepository.findBySyncId("Test") } returns getMockJPASync()
        every { integrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { pendingEmployeeRepository.findByIntegrationIdAndInviteRequested("Test", false) } returns listOf(
            getJpaPendingEmployee()
        )
        every { pendingEmployeeRepository.setInvitationRequestedForAll("Test", true) } returns 1
        every { contractServiceAdapter.inviteBulkMemberContracts(any()) } returns response

        synService.sendInvites("Test", 100L)

        verify(exactly = 1) { contractServiceAdapter.inviteBulkMemberContracts(any()) }
    }

    @Test
    fun `should handle Incoming Event`() {
        val event: JsonNode = getEventAsJSONNode("sync.events.processed")
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\"",
            eventType = EventType.RECORD_NEW
        )

        every { syncRepository.findBySyncId("Test") } returns Optional.empty()
        every { syncRepository.deleteByIntegrationId("Test") } returns 1
        every { syncRepository.save(any()) } returns getMockJPASync().get()
        every { companyIntegrationRepository.findByAccountToken("Test") } returns getMockCompanyIntegration().get()
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        every { companyIntegrationRepository.save(any()) } returns getMockCompanyIntegration().get()

        synService.handleIncomingEvent(event, "Test")

        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `handleIncomingEvent creates new sync when not found`() {
        val event = getEventAsJSONNode("record.new")
        val testIntegrationId = "test-integration-id"

        every { syncRepository.findBySyncId(any()) } returns Optional.empty()
        every { syncRepository.save(any()) } returns mockk<JpaSync> {
            every { integrationId } returns testIntegrationId
        }
        every { companyIntegrationRepository.findByAccountToken(testIntegrationId) } returns mockk<JpaCompanyIntegration> {
            every { incomingSyncEnabled } returns true
            every { incomingSyncInProgress } returns false
        }
        every { dataMapper.map(any<JsonNode>()) } returns mockk<EventData> {
            every { leaveRequests } returns listOf()
        }
        every { jpaReceivedEventRepository.save(any()) } returns mockk()
        every { syncRepository.deleteByIntegrationId(any()) } returns 1
        synService.handleIncomingEvent(event, testIntegrationId)

        verify { syncRepository.save(any()) }
    }

    @Test
    fun `handleIncomingEvent uses existing sync when found`() {
        val event = getEventAsJSONNode("record.new")
        val integrationId = "test-integration-id"
        val existingSync = JpaSync("existing-sync-id", integrationId, LocalDateTime.now(), null, null, true)
        val integration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { incomingSyncEnabled } returns true
        }

        every { syncRepository.findBySyncId(any()) } returns Optional.of(existingSync)
        every { companyIntegrationRepository.findByAccountToken(integrationId) } returns integration
        every { jpaReceivedEventRepository.save(any()) } returns mockk()
        every { dataMapper.map(any<JsonNode>()) } returns mockk<EventData> {
            every { leaveRequests } returns listOf()
        }
        synService.handleIncomingEvent(event, integrationId)

        verify(exactly = 0) { syncRepository.save(any()) }
    }

    @Test
    fun `handleIncomingEvent processes sync_events_processed event`() {
        val event = getEventAsJSONNode("sync.events.processed")
        val integrationId = "test-integration-id"
        val existingSync = JpaSync("existing-sync-id", integrationId, LocalDateTime.now(), null, null, true)
        val integration = mockk<JpaCompanyIntegration>(relaxed = true)

        every { syncRepository.findBySyncId(any()) } returns Optional.of(existingSync)
        every { companyIntegrationRepository.findByAccountToken(integrationId) } returns integration
        every { syncRepository.save(any()) } returns mockk()
        every { companyIntegrationRepository.save(any()) } returns mockk()
        every { jpaReceivedEventRepository.save(any()) } returns mockk()

        synService.handleIncomingEvent(event, integrationId)

        verify { syncRepository.save(any()) }
        verify { companyIntegrationRepository.save(any()) }
    }

    @Test
    fun `handleIncomingEvent processes regular event`() {
        val event = getEventAsJSONNode("record.new")
        val integrationId = "test-integration-id"
        val existingSync = JpaSync("existing-sync-id", integrationId, LocalDateTime.now(), null, null, true)
        val integration = mockk<JpaCompanyIntegration>(relaxed = true)
        integration.incomingSyncEnabled = true

        every { syncRepository.findBySyncId(any()) } returns Optional.of(existingSync)
        every { companyIntegrationRepository.findByAccountToken(integrationId) } returns integration
        every { jpaReceivedEventRepository.save(any()) } returns mockk()
        every { dataMapper.map(any<JsonNode>()) } returns mockk<EventData> {
            every { leaveRequests } returns listOf()
        }
        synService.handleIncomingEvent(event, integrationId)

        verify { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `processTimeoffEvents saves new timeoff event when no matching events exist`() {
        val event = getEventAsJSONNode("record.new")
        val existingSync = JpaSync("existing-sync-id", "test-integration-id", LocalDateTime.now(), null, null, true)
        val matchingIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { incomingSyncEnabled } returns true
            every { timeOffSyncEnabled } returns true
        }
        every { companyIntegrationRepository.findByAccountToken(any()) } returns matchingIntegration
        every { syncRepository.findBySyncId(any()) } returns Optional.of(existingSync)
        every { dataMapper.map(any<JsonNode>()) } returns mockk<EventData> {
            every { leaveRequests } returns listOf(
                EmployeeLeaveRequest(
                    id = "123", leaveType = mockk<LeaveType>(relaxed = true),
                    employeeId = "55",
                    startDate = Date(),
                    endDate = Date(),
                    requestedOn = Date(),
                    note = "hhh",
                    status = Status.APPROVED,
                    unit = com.multiplier.integration.sync.model.Unit.DAYS,
                    amount = 10.0,
                    isPaid = IsPaid.TRUE
                )
            )
            every { customFields } returns null
            every { profile } returns mockk<EmployeeProfile> {
                every { id } returns "55"
                every { firstName } returns "John"
                every { lastName } returns "Doe"
                every { workEmail } returns "<EMAIL>"
                every { employeeNumber } returns "hhfshaf"
            }
        }
        every { timeoffEventRepository.findAllByExternalIdAndIntegrationId(any(), any()) } returns emptyList()
        every { timeoffEventRepository.save(any()) } returns mockk()
        every { jpaReceivedEventRepository.save(any()) } returns mockk()

        synService.handleIncomingEvent(event, "test-integration-id")

        verify { timeoffEventRepository.save(any()) }
    }

    @Test
    fun `processTimeoffEvents saves new timeoff event when matching events exist`() {
        val event = getEventAsJSONNode("record.new")
        val existingSync = JpaSync("existing-sync-id", "test-integration-id", LocalDateTime.now(), null, null, true)
        val timeoffEvent = mockk<JpaTimeoffEvent> {
            every { externalId } returns "123"
            every { integrationId } returns 1L
            every { timeoffType } returns "type1"
            every { processed = any() } just Runs
        }
        val matchingIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { incomingSyncEnabled } returns true
            every { timeOffSyncEnabled } returns true
        }
        every { companyIntegrationRepository.findByAccountToken(any()) } returns matchingIntegration
        every { syncRepository.findBySyncId(any()) } returns Optional.of(existingSync)
        every { dataMapper.map(any<JsonNode>()) } returns mockk<EventData> {
            every { leaveRequests } returns listOf(
                EmployeeLeaveRequest(
                    id = "123", leaveType = mockk<LeaveType>(relaxed = true),
                    employeeId = "55",
                    startDate = Date(),
                    endDate = Date(),
                    requestedOn = Date(),
                    note = "hhh",
                    status = Status.APPROVED,
                    unit = com.multiplier.integration.sync.model.Unit.DAYS,
                    amount = 10.0,
                    isPaid = IsPaid.TRUE
                )
            )
            every { customFields } returns null
            every { profile } returns mockk<EmployeeProfile> {
                every { id } returns "55"
                every { firstName } returns "John"
                every { lastName } returns "Doe"
                every { workEmail } returns "<EMAIL>"
                every { employeeNumber } returns "hhfshaf"
            }
        }
        every { timeoffEventRepository.findAllByExternalIdAndIntegrationId(any(), any()) } returns listOf(timeoffEvent)
        every { timeoffEventRepository.save(any()) } returns mockk()
        every { jpaReceivedEventRepository.save(any()) } returns mockk()
        justRun { timeoffService.revokeTimeoffEvent(any(), any()) }
        every { platformTimeoffIntegrationRepository.findByExternalTimeoffId(any()) } returns null

        synService.handleIncomingEvent(event, "test-integration-id")
        verify { timeoffEventRepository.save(any()) }
    }

    @Test
    fun `should convert string to date`() {
        val date: com.google.type.Date = synService.convertStringToDate("2011-11-02T02:50:12.208121212Z")
        Assert.assertTrue(date.year == 2011)
        Assert.assertTrue(date.month == 11)
        Assert.assertTrue(date.day == 2)
    }

    @Test
    fun `should wait for sync to finish`() {
        var syncOver: Boolean

        every { syncRepository.findByIntegrationIdAndInProgress("Test", false) } returns emptyList()
        syncOver = synService.waitForSyncToFinish("Test", 3)
        Assertions.assertFalse(syncOver)
        every { syncRepository.findByIntegrationIdAndInProgress("Test", false) } returns listOf(getMockJPASync().get())
        syncOver = synService.waitForSyncToFinish("Test", 3)
        Assertions.assertTrue(syncOver)
    }

    @Test
    fun `should insert new employee into contract integration`() {
        val objectMapper = ObjectMapper().registerKotlinModule()
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val mockCompanyIntegration: JpaCompanyIntegration = getMockCompanyIntegration().get()
        val eventDataDict: Map<String, Any> = objectMapper.convertValue(mockEventData)
        every { companyIntegrationRepository.findByAccountToken("Test") } returns mockCompanyIntegration
        every { providerRepository.findById(1L) } returns Optional.of(mockCompanyIntegration.provider)
        every { platformRepository.findById(1L) } returns Optional.of(mockCompanyIntegration.platform)
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(100L).second

        synService.insertNewEmployeeIntoContractIntegration(
            eventData = eventDataDict,
            contract = getMockContract(contractId = 100L, memberId = 100L), mockCompanyIntegration, null
        )

        verify(exactly = 1) { platformContractIntegrationRepository.save(any()) }
    }

    @Test
    fun `should process processExpenseUpdateEvents successfully`() {
        val mockUpdatedReceivedEvent = getMockUpdatedReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "\"2024-01-30T00:00:00Z\""
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                any(),
                any(),
                any(),
                any()
            )
        } returns listOf(mockUpdatedReceivedEvent)
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()

        synService.processExpenseUpdateEvents()

        verify(exactly = 1) {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                any(),
                any(),
                any(),
                any()
            )
        }
    }

    @Test
    fun `should process expense update events successfully`() {
        // Mocking setup
        val mockExpenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "APPROVED")
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(mockExpenseUpdateEvent)
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(
                any(),
                any()
            )
        } returns createMockCreateExpensesRequestWithReportId(123L, 456L, "externalId")
        every { expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE) } just Runs
        every { jpaReceivedEventRepository.save(any()) } returns mockExpenseUpdateEvent

        // Execute the method under test
        synService.processExpenseUpdateEvents()

        // Verification
        verify(exactly = 1) {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(
                any(),
                EventType.RECORD_UPDATE
            )
        }
        verify(exactly = 1) { expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE) }
        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should skip non-approved expense update events`() {
        // Assume this method properly mocks and returns a JpaCompanyIntegration instance
        val mockCompanyIntegration = getMockCompanyIntegration().get()
        every { companyIntegrationRepository.findByAccountToken("b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==") } returns mockCompanyIntegration

        // Mock setup for non-approved expense update events
        val mockExpenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "OPEN")
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(mockExpenseUpdateEvent)

        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)
        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        synService.processExpenseUpdateEvents()

        // Verification to ensure processBulkCreateExpenseRequest is not called for non-approved events
        verify(exactly = 0) { expenseProcessorService.processBulkCreateExpenseRequest(any(), any()) }
    }


    @Test
    fun `should handle empty list of expense update events gracefully`() {
        // Setup for an empty list of update events
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns emptyList()

        // Execute the method under test
        synService.processExpenseUpdateEvents()

        // Verification to ensure no processing attempts on an empty list
        verify(exactly = 0) { expenseProcessorService.processBulkCreateExpenseRequest(any(), any()) }
    }

    @Test
    fun `should mark expense update events as processed after successful processing`() {
        // Mock setup for the findByAccountToken method
        val mockCompanyIntegration = getMockCompanyIntegration().get()
        every { companyIntegrationRepository.findByAccountToken("b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==") } returns mockCompanyIntegration
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)
        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )

        // Continue with your existing setup
        val mockExpenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "APPROVED")
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(mockExpenseUpdateEvent)
        every {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(
                any(),
                any()
            )
        } returns createMockCreateExpensesRequestWithReportId(123L, 456L, "externalId")
        every { expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE) } just Runs
        every { jpaReceivedEventRepository.save(mockExpenseUpdateEvent) } answers {
            mockExpenseUpdateEvent.apply {
                processed = true
            }
        }

        // Execute the method under test
        synService.processExpenseUpdateEvents()

        // Assertions and verification
        assertTrue(mockExpenseUpdateEvent.processed!!)
    }

    @Test
    fun `should process multiple approved expense update events`() {
        // Assume these are correctly generated events marked as "APPROVED"
        val mockExpenseUpdateEvents = listOf(
            getMockExpenseDataReceivedEvents(status = "APPROVED"),
            getMockExpenseDataReceivedEvents(status = "APPROVED")
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)
        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )

        // Mock the repository call to return your mock events
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns mockExpenseUpdateEvents

        // Mock additional dependencies as needed...
        mockExpenseUpdateEvents.forEach { event ->
            every {
                expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(event), EventType.RECORD_UPDATE)
            } returns createMockCreateExpensesRequestWithReportId(123L, 456L, "externalId")
            every {
                expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE)
            } just Runs
            every { jpaReceivedEventRepository.save(event) } answers { event.apply { processed = true } }
        }

        // Mock company integration lookup
        mockExpenseUpdateEvents.forEach { event ->
            every { companyIntegrationRepository.findByAccountToken(event.integrationId!!) } returns getMockCompanyIntegration().get()
        }

        // Execute the method under test
        synService.processExpenseUpdateEvents()

        // Verify that each event is marked as processed
        mockExpenseUpdateEvents.forEach { event ->
            assertTrue(event.processed!!)
        }
    }

    @Test
    fun `should handle errors during expense update event processing`() {
        val mockExpenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "APPROVED")
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(mockExpenseUpdateEvent)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )

        // Mock the CompanyIntegrationRepository response
        every { companyIntegrationRepository.findByAccountToken("b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==") } returns getMockCompanyIntegration().get()

        // Simulate an exception being thrown during expense request generation
        every { expenseProcessorService.generateExpenseRequestFromExpenseEvents(any(), any()) } throws RuntimeException(
            "Test exception"
        )

        // Execute the method under test
        assertDoesNotThrow { synService.processExpenseUpdateEvents() }

        // Verification to ensure no processing occurs due to the thrown exception
        verify(exactly = 0) { expenseProcessorService.processBulkCreateExpenseRequest(any(), any()) }
    }

    @Test
    fun `should gracefully handle when there are no expense update events to process`() {
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns emptyList()

        // Execute the method under test
        synService.processExpenseUpdateEvents()

        // Verify that no processing attempts are made
        verify(exactly = 0) { expenseProcessorService.processBulkCreateExpenseRequest(any(), any()) }
    }

    @Test
    fun `should process bulk delete for non-approved expense when entity exists`() {
        // Given
        val expenseUpdateEvent =
            getMockExpenseDataReceivedEvents(status = "OPEN") // Assuming OPEN status means non-approved
        val expenseRequest = createMockCreateExpensesRequestWithReportId(
            contractId = 456L,
            externalId = "externalId",
            isApproved = false
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(expenseUpdateEvent)
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(
                listOf(expenseUpdateEvent),
                EventType.RECORD_UPDATE
            )
        } returns expenseRequest
        every { entityIntegrationRepository.findByExternalId(expenseRequest.first!!) } returns listOf(
            getMockEntityIntegration(expenseRequest.first!!, 123L)
        )
        every { expenseProcessorService.processBulkRevokeExpenseRequest(any()) } just Runs
        every { entityIntegrationRepository.delete(any()) } just Runs

        // When
        synService.processExpenseUpdateEvents()

        // Then
        verify(exactly = 1) {
            expenseProcessorService.processBulkRevokeExpenseRequest(match {
                it.expenseIdsList.contains(
                    123L
                )
            })
        }
        verify(exactly = 1) { entityIntegrationRepository.delete(match { it.externalId == expenseRequest.first }) }
    }

    @Test
    fun `should not proceed with creation when no entity found for externalExpenseId`() {
        // Given
        val expenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "OPEN") // OPEN status for non-approved
        val expenseRequest = createMockCreateExpensesRequestWithReportId(
            contractId = 456L,
            externalId = "externalId",
            isApproved = false
        )
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(
            mockSyncEndEvent
        )
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(expenseUpdateEvent)
        every { companyIntegrationRepository.findByAccountToken(any()) } returns getMockCompanyIntegration().get()
        every {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(
                listOf(expenseUpdateEvent),
                EventType.RECORD_UPDATE
            )
        } returns expenseRequest
        every { entityIntegrationRepository.findByExternalId(expenseRequest.first!!) } returns emptyList()
        every { expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE) } just Runs

        // When
        synService.processExpenseUpdateEvents()

        // Then
        verify(exactly = 0) { expenseProcessorService.processBulkRevokeExpenseRequest(any()) }
        verify(exactly = 0) { expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE) }
    }

    @Test
    fun `should not proceed with creation when no sync end event found`() {
        // Given
        val expenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "OPEN") // OPEN status for non-approved

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns emptyList()
        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                EventType.RECORD_UPDATE,
                Constants.SyncDataType.EXPENSE,
                true,
                false
            )
        } returns listOf(expenseUpdateEvent)

        // When
        synService.processExpenseUpdateEvents()

        // Then
        verify(exactly = 0) { companyIntegrationRepository.findByAccountToken(any()) }
        verify(exactly = 0) { expenseProcessorService.processBulkCreateExpenseRequest(any(), EventType.RECORD_UPDATE) }
    }

    @Test
    fun `should process create employee event successfully`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\""
        )
        val mockEntityId = 1L

        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setId(mockEntityId)
                .setCompanyId(companyId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)

        val mockLegalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns mockEntityId
            every { isEnabled } returns true
        }

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                companyIntegration.get().id!!,
                mockEntityId
            )
        } returns Optional.of(mockLegalEntityMapping)

        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns emptyList()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(contractId)
        every { pendingEmployeeRepository.save(any()) } returns mock<JpaPendingEmployee>()
        every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every { platformContractIntegrationRepository.save(any()) } returns mock<JpaPlatformContractIntegration>()
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns true
        every { featureFlagService.isMtmIntegration(any(), any()) } returns false

        val resp =
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())

        assertEquals(true, resp)
    }

    @Test
    fun `should process create employee event failed for existed employee`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "null"
        )

        val companyIntegration = getMockCompanyIntegration()

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns listOf(mock<JpaPlatformEmployeeData>())

        val resp =
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())

        assertEquals(false, resp)
        verify(exactly = 0) { newCompanyServiceAdapter.getLegalEntities(companyIntegration.get().companyId) }
    }

    @Test
    fun `should process create employee event failed for invalid onboarding employee data`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "null"
        )
        val mockEntityId = 1L

        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setCompanyId(companyId)
                .setId(mockEntityId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )

        val mockLegalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns mockEntityId
            every { isEnabled } returns true
        }

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                companyIntegration.get().id!!,
                mockEntityId
            )
        } returns Optional.of(mockLegalEntityMapping)

        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf("Test Error")
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns true
        every { featureFlagService.isMtmIntegration(any(), any()) } returns false

        val resp =
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())

        assertEquals(false, resp)
        verify(exactly = 0) { contractOnboardingServiceAdapter.bulkOnboarding(any()) }
    }

    @Test
    fun `should process create employee event failed for not enable entity mapping`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\""
        )
        val mockEntityId = 1L

        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setId(mockEntityId)
                .setCompanyId(companyId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
        val mockLegalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns mockEntityId
            every { isEnabled } returns false
        }

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                companyIntegration.get().id!!,
                mockEntityId
            )
        } returns Optional.of(mockLegalEntityMapping)
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns true

        assertThrows(RuntimeException::class.java) {
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())
        }
    }

    @Test
    fun `should process create employee event successfully with disabled feature flag`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\""
        )
        val mockReceivedEventsWithBlankBankAccount = getMockReceivedEventsWithBlankBankAccounts(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\""
        )
        val mockReceivedEventsWithBankAccount = getMockReceivedEventsWithBankAccounts(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\""
        )
        val mockEntityId = 1L
        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setId(mockEntityId)
                .setCompanyId(companyId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEventsWithBankAccount[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns emptyList()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(contractId)
        every { pendingEmployeeRepository.save(any()) } returns mock<JpaPendingEmployee>()
        every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every { platformContractIntegrationRepository.save(any()) } returns mock<JpaPlatformContractIntegration>()
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns false

        every { featureFlagService.isMtmIntegration(any(), any()) } returns false

        val resp =
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())

        assertEquals(true, resp)

        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.newBuilder()
            .addRequirements(
                IntegrationPaymentAccountRequirement.newBuilder()
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder().setMappedKey("clabe")
                            .setRequirementKey("clabe").build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder().setMappedKey("accountNumber")
                            .setRequirementKey("accountNumber").build()
                    )
                    .build()
            ).build()
        val resp1 = synService.processEmployeeEvent(
            mockReceivedEventsWithBankAccount[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(true, resp1)

        val resp2 = synService.processEmployeeEvent(
            mockReceivedEventsWithBlankBankAccount[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(true, resp2)
    }

    @Test
    fun `should process create employee event successfully with disabled feature flag custom field mapping edge cases`() {
        val mockReceivedEventsWithBankAccountCustom1 = getMockReceivedEventsWithBankAccountsCustom(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\"",
            customFields = """
                {
                    "fields": {
                    "beneficiaryName": null,
                    "maritalStatus": null,
                    "nationality": null
                }
                }
            """.trimIndent(),
            dependents = """
                [
                    {
                        "firstName": "test"
                    }
                ]
            """.trimIndent(),
            locations = """
                {
                    "workAddress": {
                        "city": null,
                        "state": "CA",
                        "country": "US",
                        "zipCode": null,
                        "addressType": "WORK",
                        "addressLine1": null,
                        "addressLine2": null
                    },
                    "presentAddress": null,
                    "permanentAddress": null
                }
            """.trimIndent()
        )
        val mockEntityId = 1L
        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setId(mockEntityId)
                .setCompanyId(companyId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEventsWithBankAccountCustom1[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns emptyList()
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(contractId)
        every { pendingEmployeeRepository.save(any()) } returns mock<JpaPendingEmployee>()
        every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every { platformContractIntegrationRepository.save(any()) } returns mock<JpaPlatformContractIntegration>()
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns false
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.newBuilder()
            .addRequirements(
                IntegrationPaymentAccountRequirement.newBuilder()
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder().setMappedKey("clabe")
                            .setRequirementKey("clabe").build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder().setMappedKey("accountNumber")
                            .setRequirementKey("accountNumber").build()
                    )
                    .build()
            ).build()

        every { featureFlagService.isMtmIntegration(any(), any()) } returns false

        val resp1 = synService.processEmployeeEvent(
            mockReceivedEventsWithBankAccountCustom1[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(true, resp1)

        val mockReceivedEventsWithBankAccountCustom2 = getMockReceivedEventsWithBankAccountsCustom(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\"",
            customFields = """
                {
                    "fields": null
                }
            """.trimIndent(),
            dependents = """
                [
                    {
                        "firstName": "test"
                    }
                ]
            """.trimIndent()
        )
        val resp2 = synService.processEmployeeEvent(
            mockReceivedEventsWithBankAccountCustom2[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(true, resp2)

        val mockReceivedEventsWithBankAccountCustom3 = getMockReceivedEventsWithBankAccountsCustom(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\"",
            customFields = """
                {
                    "fields": null
                }
            """.trimIndent(),
            dependents = """
                [
                    
                ]
            """.trimIndent()
        )
        val resp3 = synService.processEmployeeEvent(
            mockReceivedEventsWithBankAccountCustom3[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(true, resp3)

    }

    @Test
    fun `should process create employee event successfully with disabled feature flag validating duplicate email or workEmail`() {
        val email = "email"
        val workEmail = "workEmail"
        val mockReceivedEventsWithBankAccountCustom1 = getMockReceivedEventsWithBankAccountsCustom(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "\"2025-12-25T00:00:00Z\"",
            customFields = """
                {
                    "fields": {
                    "beneficiaryName": null,
                    "maritalStatus": null,
                    "nationality": null
                }
                }
            """.trimIndent(),
            dependents = """
                [
                    {
                        "firstName": "test"
                    }
                ]
            """.trimIndent(),
            email = "\"$email\"",
            workEmail = "\"$workEmail\"",
        )
        val mockEntityId = 1L
        val companyIntegration = getMockCompanyIntegration()
        val companyId = companyIntegration.get().companyId
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setId(mockEntityId)
                .setCompanyId(companyId)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEventsWithBankAccountCustom1[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns legalEntities
        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf("email $email is already used")
        every { contractOnboardingServiceAdapter.bulkOnboarding(any()) } returns listOf(contractId)
        every { pendingEmployeeRepository.save(any()) } returns mock<JpaPendingEmployee>()
        every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every { platformContractIntegrationRepository.save(any()) } returns mock<JpaPlatformContractIntegration>()
        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, any()) } returns false
        every { paymentServiceAdapter.getIntegrationPaymentAccountRequirements(any()) } returns IntegrationPaymentAccountRequirements.newBuilder()
            .addRequirements(
                IntegrationPaymentAccountRequirement.newBuilder()
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder().setMappedKey("clabe")
                            .setRequirementKey("clabe").build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder().setMappedKey("accountNumber")
                            .setRequirementKey("accountNumber").build()
                    )
                    .build()
            ).build()
        val mockMember = Member.newBuilder().setId(memberId).build()
        every { memberServiceAdapter.findMemberByEmailAddress(any()) } returns mockMember
        every { contractServiceAdapter.findContractByMemberId(any()) } returns mockContract
        every { featureFlagService.isMtmIntegration(any(), any()) } returns false

        val resp1 = synService.processEmployeeEvent(
            mockReceivedEventsWithBankAccountCustom1[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(false, resp1)

        every { contractOnboardingServiceAdapter.validateBulkOnboarding(any()) } returns listOf("$workEmail already exists")
        val resp2 = synService.processEmployeeEvent(
            mockReceivedEventsWithBankAccountCustom1[0],
            inviteMember = false,
            errorList = mutableListOf()
        )
        assertEquals(false, resp2)
    }

    @Test
    fun `should process create employee event failed for null employee legal entity`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "null"
        )

        val companyIntegration = getMockCompanyIntegration()

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns emptyList()

        assertThrows(RuntimeException::class.java) {
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())
        }
    }

    @Test
    fun `should process create employee event failed for legal entity mapping does not present`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "\"1979-12-25T00:00:00Z\"",
            gender = "\"FEMALE\"",
            maritalStatus = "\"SINGLE\"",
            status = "\"ACTIVE\"",
            terminationDate = "null"
        )
        val legalEntities = listOf(
            LegalEntity.newBuilder()
                .setId(1342)
                .setCompanyId(4324)
                .setAddress(
                    Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setHasCapabilities(HasCapabilities.newBuilder().setGlobalPayroll(true).build())
                .build()
        )

        val companyIntegration = getMockCompanyIntegration()

        every { companyIntegrationRepository.findByAccountToken(mockReceivedEvents[0].integrationId!!) } returns companyIntegration.get()
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.get().id!!,
                "3277135280330507201"
            )
        } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns legalEntities

        every { featureFlagService.isOn(any()) } returns true
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(any(), any()) } returns Optional.empty()

        assertThrows(RuntimeException::class.java) {
            synService.processEmployeeEvent(mockReceivedEvents[0], inviteMember = false, errorList = mutableListOf())
        }
    }
}
