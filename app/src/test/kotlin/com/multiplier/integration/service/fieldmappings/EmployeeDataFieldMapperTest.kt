package com.multiplier.integration.service.fieldmappings

import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.model.OnboardingType

import com.multiplier.integration.repository.LegalEntityMappingRepository

import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import io.kotest.matchers.shouldBe
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.Optional
import java.util.UUID
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class EmployeeDataFieldMapperTest {

    @MockK
    private lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    private lateinit var featureFlagService: FeatureFlagService

    @MockK
    private lateinit var fieldMappingServiceAdapter: FieldMappingServiceAdapter

    @InjectMockKs
    private lateinit var employeeDataFieldMapper: EmployeeDataFieldMapper

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should use field mapping service when feature flag is enabled`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = UUID.randomUUID().toString()
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns legalEntityId
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns
                    mapOf("mappedKey" to mockk { every { allFields.values } returns mutableListOf("mappedValue") })
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["mappedKey"] shouldBe "mappedValue"
    }


    @Test
    fun `should throw exception when new field mapping service fails`() {
        val legalEntityId = 1L
        val eventData = mapOf("mappedKey" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns legalEntityId
            every { isEnabled } returns true
        })

        assertThrows<IllegalStateException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should return empty map when feature flag is disabled`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns false
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result shouldBe emptyMap()
    }

    @Test
    fun `should throw exception when legal entity mapping not found`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L

        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.empty()

        assertThrows<RuntimeException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should throw exception when legal entity mapping is disabled`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L

        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns false
        })

        assertThrows<RuntimeException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should throw exception when no field mapping profile found`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns emptyList()
        }

        assertThrows<IllegalStateException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should apply email override when email is empty but workEmail is not`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = UUID.randomUUID().toString()
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe "<EMAIL>"
        result["workEmail"] shouldBe ""
    }

    @Test
    fun `should add legalEntityId for HRIS_PROFILE_DATA onboarding type`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = UUID.randomUUID().toString()
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "mappedKey" to mockk { every { allFields.values } returns mutableListOf("mappedValue") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.HRIS_PROFILE_DATA,
            4L,
            integrationId,
            companyId
        )

        result["legalEntityId"] shouldBe legalEntityId.toString()
    }

    @Test
    fun `should handle exception in field mapping service execution`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every {
            fieldMappingServiceAdapter.executeMapping(
                profileId,
                eventData
            )
        } throws RuntimeException("Mapping failed")

        assertThrows<IllegalStateException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should handle missing profile in field mapping service`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns emptyList()
        }

        assertThrows<IllegalStateException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should handle email override when email is empty but workEmail is not`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe "<EMAIL>"
        result["workEmail"] shouldBe ""
    }

    @Test
    fun `should not override email when both email and workEmail are present`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe "<EMAIL>"
        result["workEmail"] shouldBe "<EMAIL>"
    }

    @Test
    fun `should handle getOverrides with HRIS_PROFILE_DATA onboarding type`() {
        val legalEntityId = 5L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "mappedKey" to mockk { every { allFields.values } returns mutableListOf("mappedValue") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.HRIS_PROFILE_DATA,
            4L,
            integrationId,
            companyId
        )

        result["mappedKey"] shouldBe "mappedValue"
        result["legalEntityId"] shouldBe legalEntityId.toString()
    }

    @Test
    fun `should handle empty email and workEmail fields`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("") },
                "mappedKey" to mockk { every { allFields.values } returns mutableListOf("mappedValue") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe ""
        result["workEmail"] shouldBe ""
        result["mappedKey"] shouldBe "mappedValue"
    }

    @Test
    fun `should handle null email and workEmail fields`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "mappedKey" to mockk { every { allFields.values } returns mutableListOf("mappedValue") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["mappedKey"] shouldBe "mappedValue"
        // Should not contain email or workEmail keys when they're not in the original mapping
        assertFalse(result.containsKey("email"))
        assertFalse(result.containsKey("workEmail"))
    }

    @Test
    fun `should handle field mapping service returning empty transformed data`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns emptyMap()
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        assertTrue(result.isEmpty())
    }

    @Test
    fun `should throw IllegalStateException when no field mapping profile found`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        // Return profiles that don't match the criteria
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns "999" // Different entity ID
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns "profile-123"
            })
        }

        val exception = assertThrows<IllegalStateException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }

        exception.message shouldBe "No field mapping profile found for companyId=$companyId"
    }

    @Test
    fun `should throw RuntimeException when legal entity mapping is disabled`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns false // Disabled mapping
        })

        val exception = assertThrows<RuntimeException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                4L,
                integrationId,
                companyId
            )
        }

        exception.message shouldBe "Legal entity field mapping for entity $legalEntityId not enabled for GP sync"
    }

    @Test
    fun `getOverrides should replace email with workEmail when email is null and workEmail is not null`() {
        val legalEntityId = 1L
        val eventData = mapOf(
            "workEmail" to "<EMAIL>",
            "firstName" to "John"
        ) // email is null (not present in map)
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, any()) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("") },
                "firstName" to mockk { every { allFields.values } returns mutableListOf("John") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe "<EMAIL>"
        result["workEmail"] shouldBe ""
        result["firstName"] shouldBe "John"
    }

    @Test
    fun `getOverrides should replace email with workEmail when email is empty string and workEmail is not empty`() {
        val legalEntityId = 1L
        val eventData = mapOf(
            "email" to "", // Empty string
            "workEmail" to "<EMAIL>",
            "firstName" to "John"
        )
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, any()) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("") },
                "firstName" to mockk { every { allFields.values } returns mutableListOf("John") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe "<EMAIL>"
        result["workEmail"] shouldBe ""
        result["firstName"] shouldBe "John"
    }

    @Test
    fun `getOverrides should not replace email when workEmail is null or empty`() {
        val legalEntityId = 1L
        val eventData = mapOf(
            "email" to "", // Empty email
            "firstName" to "John"
            // workEmail is null (not present)
        )
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, any()) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("") },
                "firstName" to mockk { every { allFields.values } returns mutableListOf("John") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe ""
        result["firstName"] shouldBe "John"
        // workEmail should not be present in result since it wasn't in original data
        assertFalse(result.containsKey("workEmail"))
    }

    @Test
    fun `getOverrides should not replace email when workEmail is empty string`() {
        val legalEntityId = 1L
        val eventData = mapOf(
            "email" to "", // Empty email
            "workEmail" to "", // Empty workEmail
            "firstName" to "John"
        )
        val companyId = 2L
        val integrationId = 3L
        val profileId = "profile-123"
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, any()) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "email" to mockk { every { allFields.values } returns mutableListOf("") },
                "workEmail" to mockk { every { allFields.values } returns mutableListOf("") },
                "firstName" to mockk { every { allFields.values } returns mutableListOf("John") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            eventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            4L,
            integrationId,
            companyId
        )

        result["email"] shouldBe ""
        result["workEmail"] shouldBe ""
        result["firstName"] shouldBe "John"
    }

    @Test
    fun `should handle field mapping service adapter exceptions gracefully`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val platformId = 4L
        val ffAttributes = mapOf("company" to companyId, "platform" to platformId)

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } throws RuntimeException("Service unavailable")

        assertThrows<RuntimeException> {
            employeeDataFieldMapper.mapEmployeeData(
                eventData,
                legalEntityId,
                OnboardingType.GLOBAL_PAYROLL,
                platformId,
                integrationId,
                companyId
            )
        }
    }

    @Test
    fun `should handle complex nested event data correctly`() {
        val legalEntityId = 1L
        val companyId = 2L
        val integrationId = 3L
        val platformId = 4L
        val profileId = UUID.randomUUID().toString()
        val ffAttributes = mapOf("company" to companyId, "platform" to platformId)

        val complexEventData = mapOf(
            "employee" to mapOf(
                "personalInfo" to mapOf(
                    "firstName" to "John",
                    "lastName" to "Doe",
                    "email" to "<EMAIL>"
                ),
                "workInfo" to mapOf(
                    "department" to "Engineering",
                    "position" to "Senior Developer"
                )
            )
        )

        every {
            featureFlagService.isOn(
                FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED,
                ffAttributes
            )
        } returns true
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                legalEntityId
            )
        } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { isEnabled } returns true
        })
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, complexEventData) } returns mockk {
            every { transformedData.fieldsMap } returns mapOf(
                "firstName" to mockk { every { allFields.values } returns mutableListOf("John") },
                "lastName" to mockk { every { allFields.values } returns mutableListOf("Doe") },
                "email" to mockk { every { allFields.values } returns mutableListOf("<EMAIL>") },
                "department" to mockk { every { allFields.values } returns mutableListOf("Engineering") },
                "position" to mockk { every { allFields.values } returns mutableListOf("Senior Developer") }
            )
        }

        val result = employeeDataFieldMapper.mapEmployeeData(
            complexEventData,
            legalEntityId,
            OnboardingType.GLOBAL_PAYROLL,
            platformId,
            integrationId,
            companyId
        )

        assertTrue(result.isNotEmpty())
        result["firstName"] shouldBe "John"
        result["lastName"] shouldBe "Doe"
        result["email"] shouldBe "<EMAIL>"
        result["department"] shouldBe "Engineering"
        result["position"] shouldBe "Senior Developer"
    }
}

