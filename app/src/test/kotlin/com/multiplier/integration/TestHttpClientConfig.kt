package com.multiplier.integration

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.multiplier.integration.adapter.api.DefaultKnitAdapter
import com.multiplier.integration.adapter.api.MergeDevAdapterTest
import com.multiplier.integration.adapter.api.resources.knit.ApiError
import com.multiplier.integration.adapter.api.resources.knit.CompensationPlanData
import com.multiplier.integration.adapter.api.resources.knit.CompensationPlanDetail
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.Data
import com.multiplier.integration.adapter.api.resources.knit.DepartmentsListResponse
import com.multiplier.integration.adapter.api.resources.knit.DocumentData
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.GetCompensationPlanResponse
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.adapter.api.resources.knit.GetTerminationReasonResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveBalance
import com.multiplier.integration.adapter.api.resources.knit.LeaveBalanceResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequestResponseData
import com.multiplier.integration.adapter.api.resources.knit.LeaveEligibleConditionEnum
import com.multiplier.integration.adapter.api.resources.knit.LeavePaidConditionEnum
import com.multiplier.integration.adapter.api.resources.knit.LeaveType
import com.multiplier.integration.adapter.api.resources.knit.LeaveTypeEnum
import com.multiplier.integration.adapter.api.resources.knit.LeaveUnitEnum
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.ResponseData
import com.multiplier.integration.adapter.api.resources.knit.TerminateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.TerminationReason
import com.multiplier.integration.adapter.api.resources.knit.TerminationReasonResponse
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationResponse
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsResponse
import com.multiplier.integration.adapter.api.resources.knit.WorkShift
import com.multiplier.integration.adapter.api.resources.knit.hibob.GetWorksitesResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSite
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteData
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteResponseBody
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteResponseBodySerialized
import com.multiplier.integration.adapter.api.resources.knit.oracle.GetLegalEntitiesResponse
import com.multiplier.integration.adapter.api.resources.knit.oracle.LegalEntityBodyWrapper
import com.multiplier.integration.adapter.api.resources.knit.oracle.LegalEntityResponseWrapper
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategoriesData
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategoriesResponse
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategory
import com.multiplier.integration.adapter.model.CreateSyncRequest
import com.multiplier.integration.mock.createEmployeeData
import com.multiplier.integration.mock.createEmployeeResponse
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.io.IOException
import java.nio.charset.Charset

class TestHttpClientConfig {
    companion object {
        fun successKnitHttpClient(): HttpClient = createCustomHttpClient {
            mapOf(
                "/hr.employee.create" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(createEmployeeResponse(
                        success = true,
                        data = createEmployeeData(employeeId = "emp123456"),
                        errors = null,
                        responseCode = 200
                    ))
                ),
                "/hr.employees.positions" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(GetPositionDetailResponse(
                        success = true,
                        data = Data(
                            positions = listOf(Position(positionId = "1", designation = "Software Engineer", department = "Engineering")),
                            workShifts = listOf(WorkShift(workShiftId = "1", workShiftName = "Morning Shift"))
                        ),
                        responseCode = 200
                    ))
                ),
                "/hr.compensation.plans" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        GetCompensationPlanResponse(
                        success = true,
                            data = CompensationPlanData(
                                fixed = listOf(
                                    CompensationPlanDetail(
                                        planId = "1",
                                        planName = "Basic Salary",
                                        description = "Basic monthly salary",
                                        amount = 3000.00,
                                        currency = "USD",
                                        payPeriod = "Monthly",
                                        frequency = "Monthly",
                                        type = "Fixed"
                                    )
                                ),
                                variable = listOf(
                                    CompensationPlanDetail(
                                        planId = "2",
                                        planName = "Performance Bonus",
                                        description = "Annual performance bonus",
                                        percentage = 15.0,
                                        frequency = "Yearly",
                                        type = "Variable"
                                    )
                                ),
                                stock = listOf(
                                    CompensationPlanDetail(
                                        planId = "3",
                                        planName = "Stock Options",
                                        description = "Employee stock options",
                                        targetShares = 250.0,
                                        frequency = "At vesting",
                                        type = "Stock"
                                    )
                                )
                            ),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.employees.document.upload" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        CreateDocumentResponse(
                        success = true,
                        data = DocumentData(documentId = "doc123"),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.employee.compensation.update" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        UpdateCompensationResponse(
                        success = true,
                        data = ResponseData(compensationId = "comp123"),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.employee.update" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        UpdateEmployeeDetailsResponse(
                        success = true,
                        data = ResponseData(compensationId = "comp123"),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.documents.categories" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        DocumentCategoriesResponse(
                        success = true,
                        data = DocumentCategoriesData(categories = listOf(
                            DocumentCategory(id = "1", name = "Contract"),
                            DocumentCategory(id = "2", name = "Resume")
                        )),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.employees.leave.balance" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        LeaveBalanceResponse(
                        success = true,
                        data = listOf(
                            LeaveBalance(
                                leaveType = LeaveType(id = "1", name = "Vacation", type = LeaveTypeEnum.VACATION),
                                unit = LeaveUnitEnum.DAYS,
                                balance = 10.0,
                                used = 2.0,
                                isPaid = LeavePaidConditionEnum.TRUE,
                                isEligible = LeaveEligibleConditionEnum.TRUE
                            )
                        ),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.employee.terminate" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        TerminateEmployeeResponse(
                        success = true,
                        data = JsonObject(mapOf()),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.termination.reasons" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        GetTerminationReasonResponse(
                        success = true,
                        data = TerminationReasonResponse(
                            reasons = listOf(TerminationReason(id = "1", name = "Retirement"))
                        ),
                        responseCode = 200
                    )
                    )
                ),
                "/hr.departments.list" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = """
                        {
                            "success": true,
                            "data": {
                                "departments": [
                                    {
                                        "id": "dept1",
                                        "name": "Engineering",
                                        "companyId": "company1",
                                        "description": "Engineering Department"
                                    },
                                    {
                                        "id": "dept2",
                                        "name": "Marketing",
                                        "companyId": "company2",
                                        "description": "Marketing Department"
                                    }
                                ]
                            },
                            "responseCode": 200
                        }
                    """
                ),
                "/fields.list" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = """
                        {
                          "success": true,
                          "data": {
                            "default": [
                              {
                                "fieldId": "address.city",
                                "fieldFromApp": "address.city",
                                "mappedKey": "presentAddress.city",
                                "dataType": "STRING",
                                "label": "City"
                              }
                            ],
                            "unmapped": [
                              {
                                "fieldId": "Branch name||financial.custom.field_1719220253652",
                                "fieldFromApp": "Branch name||financial.custom.field_1719220253652",
                                "mappedKey": null,
                                "dataType": "STRING",
                                "label": "Branch name"
                              }
                            ],
                            "mapped": [
                              {
                                "fieldId": "Continuous employment start date",
                                "fieldFromApp": "Continuous employment start date",
                                "mappedKey": "continuousEmploymentStartDate",
                                "dataType": "STRING",
                                "label": null
                              }
                            ]
                          }
                        }
                    """,
                ),
                "/hr.employees.field.values" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = """
                        {
                          "success": true,
                          "data": {
                            "fields": [
                              {
                                "id": null,
                                "label": "Contractor"
                              },
                              {
                                "id": null,
                                "label": "Full-Time"
                              },
                              {
                                "id": null,
                                "label": "Furloughed"
                              },
                              {
                                "id": null,
                                "label": "Part-Time"
                              },
                              {
                                "id": null,
                                "label": "Terminated"
                              }
                            ]
                          }
                        }
                        """
                ),
                "/field.map" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = """
                        {
                          "success": true,
                          "data": "success"
                        }
                        """
                ),
                "/hr.leave.types" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = """
                       {
                          "success": true,
                          "data": {
                            "leaveTypes": [
                              {
                                "id": "2518151",
                                "name": "Paid Vacation UK",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2518150",
                                "name": "Paid Vacation SE",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2518149",
                                "name": "Paid Vacation NL",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2518146",
                                "name": "Sick days",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2518147",
                                "name": "Parental leave",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2518145",
                                "name": "Maternity protection",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2518148",
                                "name": "Home office",
                                "type": "NOT_SPECIFIED"
                              },
                              {
                                "id": "2521283",
                                "name": "Hourly Off",
                                "type": "NOT_SPECIFIED"
                              }
                            ]
                          }
                        }
                    """
                ),
                "/hr.employees.leave.create" to HttpResponseConfig(
                    status = HttpStatusCode.OK,
                    content = Json.encodeToString(
                        LeaveCreateRequestResponse(
                            success = true,
                            data = LeaveCreateRequestResponseData(leaveRequestId = "leave123"),
                            responseCode = 201
                        )
                    )
                )
            )
        }

        fun errorResponseKnitHttpClient(): HttpClient = createCustomHttpClient {
            mapOf(
                "/hr.employee.create" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = ""
                ),
                "/hr.employees.positions" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(GetPositionDetailResponse(
                        success = false,
                        error = ApiError(msg = "Invalid request parameters"),
                        responseCode = 400
                    ))
                ),
                "/hr.compensation.plans" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(GetCompensationPlanResponse(
                        success = false,
                        error = ApiError(msg = "Invalid request parameters"),
                        responseCode = 400
                    ))
                ),
                "/hr.employees.document.upload" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(CreateDocumentResponse(
                        success = false,
                        error = ErrorResponse(msg = "Invalid document data"),
                        responseCode = 400
                    ))
                ),
                "/hr.employee.compensation.update" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(UpdateCompensationResponse(
                        success = false,
                        error = UpdateCompensationErrorResponse(msg = "Invalid compensation details"),
                        responseCode = 400
                    ))
                ),
                "/hr.employee.update" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(UpdateEmployeeDetailsResponse(
                        success = false,
                        error = ErrorResponse(msg = "Invalid employee details"),
                        responseCode = 400
                    ))
                ),
                "/hr.documents.categories" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(DocumentCategoriesResponse(
                        success = false,
                        error = com.multiplier.integration.adapter.api.resources.workday.ErrorResponse(msg = "Failed to fetch categories"),
                        responseCode = 400
                    ))
                ),
                "/hr.employees.leave.balance" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(LeaveBalanceResponse(
                        success = false,
                        error = ApiError(msg = "Failed to fetch data"),
                        responseCode = 400
                    ))
                ),
                "/hr.employee.terminate" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(TerminateEmployeeResponse(
                        success = false,
                        error = ErrorResponse(msg = "Failed to terminate employee"),
                        responseCode = 400
                    ))
                ),
                "/hr.termination.reasons" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = Json.encodeToString(GetTerminationReasonResponse(
                        success = false,
                        error = ErrorResponse(msg = "Error fetching termination reasons"),
                        responseCode = 400
                    ))
                ),
                "/passthrough" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = ""
                ),
                "/sync.start" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = ""
                ),
                "/fields.list" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = """
                        {
                          "success": false,
                          "error": {
                            "msg": "Invalid Integration ID"
                          }
                        }
                        """
                ),
                "/hr.employees.field.values" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = """
                        {
                          "success": false,
                          "error": {
                            "msg": "Invalid Integration ID"
                          }
                        }
                        """
                ),
                "/hr.departments.list" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = """
                        {
                          "success": false,
                          "error": {
                            "msg": "Invalid Integration ID"
                          }
                        }
                        """
                ),
                "/field.map" to HttpResponseConfig(
                    status = HttpStatusCode.BadRequest,
                    content = """
                        {
                          "success": false,
                          "data": "failed"
                        }
                        """
                ),
                "/hr.employees.leave.create" to HttpResponseConfig(
                    status = HttpStatusCode.NotFound,
                    content = Json.encodeToString(
                        LeaveCreateRequestResponse(
                            success = false,
                            error = ApiError(msg = "Leave type not found"),
                            responseCode = 404
                        )
                    )
                )
            )
        }

        fun exceptionKnitHttpClient(): HttpClient = createCustomHttpClient {
            throw IOException("Simulated server error")
        }

        private fun createCustomHttpClient(handler: () -> Map<String, HttpResponseConfig>): HttpClient {
            return HttpClient(MockEngine) {
                engine {
                    addHandler { request ->
                        handler().entries.firstOrNull { it.key == request.url.encodedPath }?.let {
                            respond(
                                content = it.value.content,
                                status = it.value.status,
                                headers = headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        } ?: respond("Not Found", HttpStatusCode.NotFound)
                    }
                }
                defaultRequest {
                    contentType(ContentType.Application.Json)
                    accept(ContentType.Application.Json)
                }
                install(ContentNegotiation) {
                    jackson {
                        registerModule(JavaTimeModule())
                        findAndRegisterModules()
                        enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                        enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                        disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                    }
                }
                install(Resources)
                install(HttpTimeout) {
                    requestTimeoutMillis = 60_000 // 1 minute
                    connectTimeoutMillis = 60_000 // 1 minute
                    socketTimeoutMillis = 60_000 // 1 minute
                }
            }
        }

        fun successPassthroughHttpClient(): HttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    // First, decode the body to find out what the actual request path is
                    val requestBody = request.body.toByteArray().toString(Charset.defaultCharset())
                    val passthroughRequest = Json.decodeFromString<PassthroughRequest>(requestBody)

                    // Route the request based on the 'path' attribute in the passthrough request
                    when (passthroughRequest.path) {
                        "legalEmployersLov" -> {
                            val legalEntitiesJson = """
                                {
                                    "items": [
                                        {
                                            "OrganizationId": 300100037952498,
                                            "EffectiveStartDate": "2023-01-01",
                                            "EffectiveEndDate": "2023-12-31",
                                            "Name": "Test Legal Entity",
                                            "LegislationCode": "US",
                                            "PayrollStatutoryUnitId": 123456789
                                        }
                                    ]
                                }
                            """.trimIndent()

                            val responseContent = Json.encodeToString(
                                GetLegalEntitiesResponse(
                                    success = true,
                                    data = LegalEntityResponseWrapper(
                                        response = LegalEntityBodyWrapper(body = legalEntitiesJson)
                                    ),
                                    responseCode = 200
                                )
                            )
                            respond(responseContent, HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        "company/named-lists/site" -> {
                            // Simulate a response for this endpoint
                            val responseContent = Json.encodeToString(
                                GetWorksitesResponse(
                                    success = true,
                                    data = WorkSiteResponse(
                                        responseJson = WorkSiteResponseBodySerialized(
                                            body = WorkSiteData(
                                                name = "Main Office",
                                                values = listOf(
                                                    WorkSite(id = 1, name = "Main Office", archived = false)
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                            respond(responseContent, HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        "meta/lists" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "<lists><list fieldId='1' alias='Main'><name>Main Office</name><options><option id='101' name='HQ' archived='false'/></options></list></lists>"
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "FOLocation" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": {
                                                        "d": {
                                                            "results": [
                                                                {"name": "Main Office", "externalCode": "101", "timezone": "EST", "locationGroup": "HQ", "internalCode": "1001"}
                                                            ]
                                                        }
                                                    }
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )

                        }
                        "FOBusinessUnit" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": {
                                                        "d": {
                                                            "results": [
                                                                {"name": "Human Resources", "externalCode": "HR123", "description": "Human Resources Department"}
                                                            ]
                                                        }
                                                    }
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "employees/1/files/view/" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "<categories><category id='1'><name>HR Documents</name><canUploadFiles>yes</canUploadFiles></category><category id='2'><name>Finance</name><canUploadFiles>yes</canUploadFiles></category></categories>",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "employees/directory" -> {
                            respond(
                                """{
                                          "success": true,
                                          "data": {
                                            "response": {
                                              "body": "<?xml version=\"1.0\"?>\n<directory>\n <fieldset>\n  <field id=\"displayName\">Display name</field>\n  <field id=\"firstName\">First name</field>\n  <field id=\"lastName\">Last name</field>\n  <field id=\"preferredName\">Preferred name</field>\n  <field id=\"jobTitle\">Job title</field>\n  <field id=\"workPhone\">Work Phone</field>\n  <field id=\"mobilePhone\">Mobile Phone</field>\n  <field id=\"workEmail\">Work Email</field>\n  <field id=\"department\">Department</field>\n  <field id=\"location\">Location</field>\n  <field id=\"division\">Division</field>\n  <field id=\"facebook\">Facebook URL</field>\n  <field id=\"linkedIn\">LinkedIn URL</field>\n  <field id=\"twitterFeed\">Twitter Feed</field>\n  <field id=\"pronouns\">Pronouns</field>\n  <field id=\"workPhoneExtension\">Work Ext.</field>\n  <field id=\"supervisor\">Manager</field>\n  <field id=\"photoUploaded\">Employee photo</field>\n  <field id=\"photoUrl\">Photo URL</field>\n  <field id=\"canUploadPhoto\">Can Upload Photo</field>\n </fieldset>\n <employees>\n  <employee id=\"117\">\n   <field id=\"displayName\">Utkarsh Chauhan</field>\n   <field id=\"firstName\">Utkarsh</field>\n   <field id=\"lastName\">Chauhan</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">SDE</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\">7531585741</field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\"></field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=UC</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"140\">\n   <field id=\"displayName\">Lara Christiana</field>\n   <field id=\"firstName\">Lara</field>\n   <field id=\"lastName\">Christiana</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">SDE</field>\n   <field id=\"workPhone\">+****************</field>\n   <field id=\"mobilePhone\">+****************</field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Mexico</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\">Tempor quam possimus</field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=LC</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"120\">\n   <field id=\"displayName\">Halla Mejia</field>\n   <field id=\"firstName\">Halla</field>\n   <field id=\"lastName\">Mejia</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">Account Manager</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Canada Custom</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=HM</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"113\">\n   <field id=\"displayName\">Petar Novakovic</field>\n   <field id=\"firstName\">Petar</field>\n   <field id=\"lastName\">Novakovic</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\"></field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\"></field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=PN</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"139\">\n   <field id=\"displayName\">Time Off</field>\n   <field id=\"firstName\">Time</field>\n   <field id=\"lastName\">Off</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">QA</field>\n   <field id=\"workPhone\">9952866125</field>\n   <field id=\"mobilePhone\">9952866124</field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\"></field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=TO</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"112\">\n   <field id=\"displayName\">Nick Payne</field>\n   <field id=\"firstName\">Nick</field>\n   <field id=\"lastName\">Payne</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\"></field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\"></field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=NP</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"118\">\n   <field id=\"displayName\">Bam sanity</field>\n   <field id=\"firstName\">Bam</field>\n   <field id=\"lastName\">sanity</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">QA</field>\n   <field id=\"workPhone\">1235</field>\n   <field id=\"mobilePhone\">35584</field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Mexico</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=BS</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"119\">\n   <field id=\"displayName\">Reece Schroeder</field>\n   <field id=\"firstName\">Reece</field>\n   <field id=\"lastName\">Schroeder</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">CSM</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Canada Custom</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=RS</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"129\">\n   <field id=\"displayName\">Canada Shepherd</field>\n   <field id=\"firstName\">Canada</field>\n   <field id=\"lastName\">Shepherd</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">Product</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Canada Custom</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=CS</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"114\">\n   <field id=\"displayName\">Pradeep Sundaram</field>\n   <field id=\"firstName\">Pradeep</field>\n   <field id=\"lastName\">Sundaram</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\"></field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\"></field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=PS</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"124\">\n   <field id=\"displayName\">Minh Test</field>\n   <field id=\"firstName\">Minh</field>\n   <field id=\"lastName\">Test</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">QA</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Canada Custom</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=MT</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"125\">\n   <field id=\"displayName\">Minh Test 2</field>\n   <field id=\"firstName\">Minh</field>\n   <field id=\"lastName\">Test 2</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">QA</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Canada Custom</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=MT</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n  <employee id=\"127\">\n   <field id=\"displayName\">Minh Test 3</field>\n   <field id=\"firstName\">Minh</field>\n   <field id=\"lastName\">Test 3</field>\n   <field id=\"preferredName\"></field>\n   <field id=\"jobTitle\">QA</field>\n   <field id=\"workPhone\"></field>\n   <field id=\"mobilePhone\"></field>\n   <field id=\"workEmail\"><EMAIL></field>\n   <field id=\"department\"></field>\n   <field id=\"location\">Canada Custom</field>\n   <field id=\"division\"></field>\n   <field id=\"facebook\"></field>\n   <field id=\"linkedIn\"></field>\n   <field id=\"twitterFeed\"></field>\n   <field id=\"pronouns\"></field>\n   <field id=\"workPhoneExtension\"></field>\n   <field id=\"supervisor\"></field>\n   <field id=\"photoUploaded\">false</field>\n   <field id=\"photoUrl\">https://resources.bamboohr.com/employees/photos/initials.php?initials=MT</field>\n   <field id=\"canUploadPhoto\">yes</field>\n  </employee>\n </employees>\n</directory>\n",
                                              "headers": {
                                                "Date": "Thu, 12 Sep 2024 06:23:59 GMT",
                                                "Content-Type": "text/xml; charset=UTF-8",
                                                "Transfer-Encoding": "chunked",
                                                "Connection": "keep-alive",
                                                "Set-Cookie": "AWSALBTGCORS=9FuTLYLK2COlWlNEEl1t0kbt8N4/POKPggSsHaOOIGZ4WBQgLVz5q0jVyL0CynO6SW+hWnbYEpoJJJpqHeds7mkt6aMcr04G+SAn0iQ+o+pWOP+1WrmseatYfIorREx3kY/VgQNTB3y0YI9EiSJSf9WhIbXAEI5MLwDsA9kbq9km; Expires=Thu, 19 Sep 2024 06:23:59 GMT; Path=/; SameSite=None; Secure",
                                                "Server": "nginx",
                                                "Vary": "Authorization,User-Agent",
                                                "Strict-Transport-Security": "max-age=31536000; includeSubdomains;",
                                                "X-Content-Type-Options": "nosniff"
                                              }
                                            }
                                          }
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "people/search" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "{\"employees\":[{\"/employment/custom/field_1726822551323\":{\"value\":{\"value\":1000,\"currency\":\"SGD\"}},\"fullName\":\"Virgil dijk\",\"/payroll/employment/siteWorkingPattern\":{\"value\":{\"workingPatternType\":\"hourly\",\"days\":{\"sunday\":0,\"tuesday\":8,\"wednesday\":8,\"monday\":8,\"friday\":8,\"thursday\":8,\"saturday\":0},\"hoursPerDay\":8,\"workingPatternId\":2617080}},\"/about/avatar\":{\"value\":\"https://media-process.hibob.com/image/upload/b_rgb:85C88A/co_white,l_text:Arial_50_bold:VD,g_center/hibob/default-avatar/transparent_avatar.png?token=X19jbGRfdG9rZW5fXz1leHA9NDgzNDYyODAxNH5hY2w9KiUyZmhpYm9iJTJmZGVmYXVsdC1hdmF0YXIlMmZ0cmFuc3BhcmVudF9hdmF0YXIucG5nKn5obWFjPThlYWExNDdjOWEzNWQwZjg3ZmQ1YmJkZTRiYTA4ZjE1YzExZjYzNWQxMmVkNzRmZGE1NmIwZDU5MDg4YTJmOGE=&vendor=cloudinary\"},\"displayName\":\"Virgil Dijk\",\"payroll\":{\"employment\":{\"hoursInDayNotWorked\":8,\"standardWorkingPattern\":null,\"fte\":100,\"type\":null,\"flsaCode\":null,\"actualWorkingPattern\":{\"workingPatternType\":\"hourly\",\"days\":{\"sunday\":0,\"tuesday\":8,\"wednesday\":8,\"monday\":8,\"friday\":8,\"thursday\":8,\"saturday\":0},\"hoursPerDay\":8,\"workingPatternId\":2617080},\"contract\":\"Full time\",\"calendarId\":null,\"salaryPayType\":null,\"personalWorkingPatternType\":null,\"workingPattern\":null,\"activeEffectiveDate\":\"2024-09-25\",\"weeklyHours\":40,\"siteWorkingPattern\":{\"workingPatternType\":\"hourly\",\"days\":{\"sunday\":0,\"tuesday\":8,\"wednesday\":8,\"monday\":8,\"friday\":8,\"thursday\":8,\"saturday\":0},\"hoursPerDay\":8,\"workingPatternId\":2617080}}},\"/payroll/employment/activeEffectiveDate\":{\"value\":\"2024-09-25\"},\"custom\":{\"field_1721890980367\":\"Engineering\"},\"/root/firstName\":{\"value\":\"Virgil\"},\"/employment/custom/field_1726821381249\":{\"value\":\"*********\"},\"/work/employeeIdInCompany\":{\"value\":1512},\"/work/tenureDurationYears\":{\"value\":0},\"/about/foodPreferences\":{\"value\":[]},\"/work/site\":{\"value\":\"Singapore\"},\"creationDateTime\":\"2024-09-23T09:29:45.746533\",\"/work/workChangeType\":{\"value\":\"New Employee\"},\"/about/hobbies\":{\"value\":[]},\"/payroll/employment/hoursInDayNotWorked\":{\"value\":8},\"/root/email\":{\"value\":\"<EMAIL>\"},\"employee\":{\"payrollManager\":null,\"hrbp\":null,\"itAdmin\":null,\"buddy\":null},\"/work/title\":{\"value\":\"*********\"},\"work\":{\"shortStartDate\":\"09-25\",\"startDate\":\"2024-09-25\",\"manager\":null,\"tenureDuration\":null,\"custom\":{\"field_1717403923990\":null,\"field_1722934230830\":null},\"durationOfEmployment\":null,\"reportsToIdInCompany\":null,\"employeeIdInCompany\":1512,\"reportsTo\":null,\"indirectReports\":null,\"siteId\":2528611,\"tenureDurationYears\":0,\"department\":null,\"tenureYears\":null,\"isManager\":false,\"title\":\"*********\",\"site\":\"Singapore\",\"originalStartDate\":\"2024-09-25\",\"activeEffectiveDate\":\"2024-09-25\",\"directReports\":null,\"workChangeType\":\"New Employee\",\"secondLevelManager\":null,\"daysOfPreviousService\":0,\"yearsOfService\":0},\"/payroll/employment/weeklyHours\":{\"value\":40},\"/root/creationDateTime\":{\"value\":\"2024-09-23T09:29:45.746533\"},\"avatarUrl\":null,\"secondName\":null,\"/work/startDate\":{\"value\":\"2024-09-25\"},\"about\":{\"foodPreferences\":[],\"socialData\":{\"linkedin\":null,\"twitter\":null,\"facebook\":null},\"superpowers\":[],\"hobbies\":[],\"about\":null,\"avatar\":\"https://media-process.hibob.com/image/upload/b_rgb:85C88A/co_white,l_text:Arial_50_bold:VD,g_center/hibob/default-avatar/transparent_avatar.png?token=X19jbGRfdG9rZW5fXz1leHA9NDgzNDYyODAxNH5hY2w9KiUyZmhpYm9iJTJmZGVmYXVsdC1hdmF0YXIlMmZ0cmFuc3BhcmVudF9hdmF0YXIucG5nKn5obWFjPThlYWExNDdjOWEzNWQwZjg3ZmQ1YmJkZTRiYTA4ZjE1YzExZjYzNWQxMmVkNzRmZGE1NmIwZDU5MDg4YTJmOGE=&vendor=cloudinary\"},\"/root/fullName\":{\"value\":\"Virgil dijk\"},\"/payroll/employment/contract\":{\"value\":\"Full time\"},\"/payroll/employment/actualWorkingPattern\":{\"value\":{\"workingPatternType\":\"hourly\",\"days\":{\"sunday\":0,\"tuesday\":8,\"wednesday\":8,\"monday\":8,\"friday\":8,\"thursday\":8,\"saturday\":0},\"hoursPerDay\":8,\"workingPatternId\":2617080}},\"companyId\":635277,\"/root/coverImageUrl\":{\"value\":\"https://media-process.hibob.com/image/upload/v1/hibob/public-image/cover/white_default_cover_image.png?token=X19jbGRfdG9rZW5fXz1leHA9MTczMDgwMTQ2NX5hY2w9KiUyZmhpYm9iJTJmcHVibGljLWltYWdlJTJmY292ZXIlMmZ3aGl0ZV9kZWZhdWx0X2NvdmVyX2ltYWdlLnBuZyp+aG1hYz0wMDQ5MGJkNjQ5N2Y2ZDQ5OGY5ZTVmZTMxYTYwNTI1NDA4ZTVhOGE4YWFmMWU3ZTZjYzVkNzlmNzVjODA3YTkw&vendor=cloudinary\"},\"/work/yearsOfService\":{\"value\":0},\"/work/daysOfPreviousService\":{\"value\":0},\"/root/surname\":{\"value\":\"dijk\"},\"/work/shortStartDate\":{\"value\":\"09-25\"},\"/work/activeEffectiveDate\":{\"value\":\"2024-09-25\"},\"/root/id\":{\"value\":\"3463352273902502881\"},\"email\":\"<EMAIL>\",\"surname\":\"dijk\",\"/root/companyId\":{\"value\":635277},\"/root/displayName\":{\"value\":\"Virgil Dijk\"},\"/payroll/employment/fte\":{\"value\":100},\"/work/isManager\":{\"value\":false},\"/work/originalStartDate\":{\"value\":\"2024-09-25\"},\"employment\":{\"custom\":{\"field_1726822551323\":{\"value\":1000,\"currency\":\"SGD\"},\"field_1726821381249\":\"*********\"}},\"coverImageUrl\":\"https://media-process.hibob.com/image/upload/v1/hibob/public-image/cover/white_default_cover_image.png?token=X19jbGRfdG9rZW5fXz1leHA9MTczMDgwMTQ2NX5hY2w9KiUyZmhpYm9iJTJmcHVibGljLWltYWdlJTJmY292ZXIlMmZ3aGl0ZV9kZWZhdWx0X2NvdmVyX2ltYWdlLnBuZyp+aG1hYz0wMDQ5MGJkNjQ5N2Y2ZDQ5OGY5ZTVmZTMxYTYwNTI1NDA4ZTVhOGE4YWFmMWU3ZTZjYzVkNzlmNzVjODA3YTkw&vendor=cloudinary\",\"/about/superpowers\":{\"value\":[]},\"/root/custom/field_1721890980367\":{\"value\":\"Engineering\"},\"id\":\"3463352273902502881\",\"firstName\":\"Virgil\",\"/work/siteId\":{\"value\":2528611}}]}",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "employees/1/time_off/request" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "{\"id\":\"1\",\"employeeId\":\"150\",\"start\":\"2024-12-03\",\"end\":\"2024-12-04\",\"created\":\"2025-01-07\",\"status\":{\"status\":\"requested\",\"lastChanged\":\"2025-01-07 11:34:32\",\"lastChangedByUserId\":\"2503\"},\"name\":\"Blossom Fletcher\",\"type\":{\"id\":\"87\",\"name\":\"Casual Leave\"},\"amount\":{\"unit\":\"days\",\"amount\":\"2\"},\"notes\":[],\"dates\":{\"2024-12-03\":\"1\",\"2024-12-04\":\"1\"},\"comments\":[{\"employeeId\":\"114\",\"comment\":\"let's go\",\"commentDate\":\"2025-01-07\",\"commenterName\":\"Pradeep Sundaram\"}],\"approvers\":[{\"userId\":\"2516\",\"displayName\":\"Ashwin PT\",\"employeeId\":\"190\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2507\",\"displayName\":\"Minh Tran\",\"employeeId\":\"116\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2385\",\"displayName\":\"Nick Payne\",\"employeeId\":\"112\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2502\",\"displayName\":\"Petar Novakovic\",\"employeeId\":\"113\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2503\",\"displayName\":\"Pradeep Sundaram\",\"employeeId\":\"114\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2515\",\"displayName\":\"Utkarsh chauhan\",\"employeeId\":\"189\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"}],\"actions\":{\"view\":true,\"edit\":true,\"cancel\":true,\"approve\":true,\"deny\":true,\"bypass\":true},\"policyType\":\"discretionary\",\"usedYearToDate\":0,\"balanceOnDateOfRequest\":0}",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "time_off/requests/1/status" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": null,
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "people/1/bank-accounts" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "{\"values\":[{\"amount\":86,\"allocation\":\"amount\",\"branchAddress\":\"5817 Vincenzo Oval\",\"bankName\":\"Alejandrin Altenwerth\",\"accountNumber\":\"488\",\"routingNumber\":\"269\",\"bankAccountType\":\"Checking\",\"bicOrSwift\":\"Aliquam saepe sequi.\",\"changedBy\":\"3272034319354299362\",\"iban\":\"Labore assumenda natus molestias sunt.\",\"accountNickname\":\"Huy's Checking\",\"useForBonus\":false,\"id\":8427897}]}",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        else -> respond("Not Found", HttpStatusCode.NotFound)
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(Resources)
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }

        fun successEdgeCasePassthroughHttpClient(): HttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    val requestBody = request.body.toByteArray().toString(Charset.defaultCharset())
                    val passthroughRequest = Json.decodeFromString<PassthroughRequest>(requestBody)

                    when (passthroughRequest.path) {
                        "company/named-lists/site" -> {
                            val responseContent = Json.encodeToString(
                                GetWorksitesResponse(
                                    success = true,
                                    data = WorkSiteResponse(
                                        response = WorkSiteResponseBody(
                                            body = Json.encodeToString(WorkSiteData(
                                                name = "Main Office",
                                                values = listOf(
                                                    WorkSite(id = 1, name = "Main Office", archived = false)
                                                ))
                                            )
                                        )
                                    )
                                )
                            )
                            respond(responseContent, HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        else -> respond("Not Found", HttpStatusCode.NotFound)
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(Resources)
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }

        fun exceptionPassthroughHttpClient(): HttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    val requestBody = request.body.toByteArray().toString(Charset.defaultCharset())
                    val passthroughRequest = Json.decodeFromString<PassthroughRequest>(requestBody)

                    when (passthroughRequest.path) {
                        "company/named-lists/site" -> {
                            val responseContent = Json.encodeToString(
                                GetWorksitesResponse(
                                    success = true,
                                    data = WorkSiteResponse(
                                        response = WorkSiteResponseBody(
                                            body = "error" + Json.encodeToString(WorkSiteData(
                                                name = "Main Office",
                                                values = listOf(
                                                    WorkSite(id = 1, name = "Main Office", archived = false)
                                                ))
                                            )
                                        )
                                    )
                                )
                            )
                            respond(responseContent, HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        "employees/1/files/view/" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "<categorfsafies><category id='1'><name>HR Documents</name></category><category id='2'><name>Finance</name></category></categories>",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "employees/directory" -> {
                            respond(
                                """{
                                            "success": false,
                                            "error": {
                                                "msg": null
                                             }
                                        }""",
                                HttpStatusCode.NotFound,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "people/search" -> {
                            respond(
                                """{
                                            "success": false,
                                            "error": {
                                                "msg": null
                                             }
                                        }""",
                                HttpStatusCode.NotFound,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "employees/1/time_off/request" -> {
                            respond(
                                """"{
                                    success": true,
                                            "data": {
                                                "response": {
                                                    "body": "{\"id\":\"1\",\"employeeId\":\"150\",\"start\":\"2024-12-03\",\"end\":\"2024-12-04\",\"created\":\"2025-01-07\",\"status\":{\"status\":\"requested\",\"lastChanged\":\"2025-01-07 11:34:32\",\"lastChangedByUserId\":\"2503\",\"name\":\"Blossom Fletcher\",\"type\":{\"id\":\"87\",\"name\":\"Casual Leave\"},\"amount\":{\"unit\":\"days\",\"amount\":\"2\"},\"notes\":{\"manager\":\"let's go\"},\"dates\":{\"2024-12-03\":\"1\",\"2024-12-04\":\"1\"},\"comments\":[{\"employeeId\":\"114\",\"comment\":\"let's go\",\"commentDate\":\"2025-01-07\",\"commenterName\":\"Pradeep Sundaram\"}],\"approvers\":[{\"userId\":\"2516\",\"displayName\":\"Ashwin PT\",\"employeeId\":\"190\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2507\",\"displayName\":\"Minh Tran\",\"employeeId\":\"116\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2385\",\"displayName\":\"Nick Payne\",\"employeeId\":\"112\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2502\",\"displayName\":\"Petar Novakovic\",\"employeeId\":\"113\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2503\",\"displayName\":\"Pradeep Sundaram\",\"employeeId\":\"114\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"},{\"userId\":\"2515\",\"displayName\":\"Utkarsh chauhan\",\"employeeId\":\"189\",\"photoUrl\":\"https:\\/\\/resources.bamboohr.com\\/images\\/photo_person_160x160.png\"}],\"actions\":{\"view\":true,\"edit\":true,\"cancel\":true,\"approve\":true,\"deny\":true,\"bypass\":true},\"policyType\":\"discretionary\",\"usedYearToDate\":0,\"balanceOnDateOfRequest\":0}",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "time_off/requests/1/status" -> {
                            respond("Error", HttpStatusCode.InternalServerError
                            )
                        }
                        else -> respond("Not Found", HttpStatusCode.NotFound)
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(Resources)
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }

        fun emptyResponsePassthroughHttpClient(): HttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    val requestBody = request.body.toByteArray().toString(Charset.defaultCharset())
                    val passthroughRequest = Json.decodeFromString<PassthroughRequest>(requestBody)

                    when (passthroughRequest.path) {
                        "legalEmployersLovBlank" -> {
                            // Return an empty response body to trigger the empty response handling
                            respond("", HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        "legalEmployersLov" -> {
                            val legalEntitiesJson = """
                                {
                                    "items": []
                                }
                            """.trimIndent()

                            val responseContent = Json.encodeToString(
                                GetLegalEntitiesResponse(
                                    success = true,
                                    data = LegalEntityResponseWrapper(
                                        response = LegalEntityBodyWrapper(body = legalEntitiesJson)
                                    ),
                                    responseCode = 200
                                )
                            )
                            respond(responseContent, HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        "company/named-lists/site" -> {
                            val responseContent = Json.encodeToString(
                                GetWorksitesResponse(
                                    success = true,
                                    data = WorkSiteResponse(
                                        response = WorkSiteResponseBody(
                                        )
                                    )
                                )
                            )
                            respond(responseContent, HttpStatusCode.OK, headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString()))
                        }
                        "employees/1/files/view/" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "employees/directory" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "<employee>",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        "people/search" -> {
                            respond(
                                """{
                                            "success": true,
                                            "data": {
                                                "response": {
                                                    "body": "{\"employees\":[]}",
                                                    "headers": {}
                                                }
                                            },
                                            "responseCode": 200
                                        }""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        else -> respond("Not Found", HttpStatusCode.NotFound)
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(Resources)
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }

        fun authKnitHttpClient(): HttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    if (Json.parseToJsonElement(request.body.toByteArray().toString(Charset.defaultCharset())).jsonObject["filters"]?.jsonArray?.firstOrNull()?.jsonObject?.get("category")?.jsonPrimitive?.content == "successCategory") {
                        respond(
                            """{
                                        "success": true,
                                        "msg": {"token": "valid_token"}
                                    }""",
                            HttpStatusCode.OK,
                            headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                        )
                    } else {
                        respond(
                            """{
                                        "success": false,
                                        "error": {"msg": "invalid_category"}
                                    }""",
                            HttpStatusCode.BadRequest,
                            headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                        )
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(Resources)
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }

        fun syncKnitHttpClient(): HttpClient = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    if (request.url.encodedPath == "/expense.status.update") {
                        val requestBody = request.body.toByteArray().toString(Charset.defaultCharset())
                        val updateRequest =
                            Json.decodeFromString<DefaultKnitAdapter.UpdateExpenseReportRequest>(requestBody)
                        when (updateRequest.reportStatus) {
                            "REIMBURSED" -> {
                                if (updateRequest.reportId == "validReportId") {
                                    respond(
                                        """{"success": true, "data": "Report updated successfully"}""",
                                        HttpStatusCode.OK,
                                        headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                                    )
                                } else if (updateRequest.reportId == "invalidReportId") {
                                    respond(
                                        """{"success": false, "error": {"msg": "Invalid report ID"}}""",
                                        HttpStatusCode.BadRequest,
                                        headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                                    )
                                } else {
                                    throw IOException("Simulated server error")
                                }
                            }

                            else -> respond(
                                """{"success": false, "error": {"msg": "Invalid status"}}""",
                                HttpStatusCode.BadRequest,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                    } else if (request.url.encodedPath == "/sync.start") {
                        val requestBody = request.body.toByteArray().decodeToString()
                        val syncRequest = Json.decodeFromString<CreateSyncRequest>(requestBody)
                        if (syncRequest.dataType.contains("employ")) {
                            respond(
                                """{"success": false, "data": {"syncJobId": "12345"}}""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                        else if(syncRequest.syncType.contains("initial")) {
                                respond(
                                    """{"success": true, "data": {"syncJobId": "12345"}}""",
                                    HttpStatusCode.OK,
                                    headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                                )
                            }
                        else {
                            respond(
                                """{"success": true, "data": {"syncJobId": "12345"}}""",
                                HttpStatusCode.OK,
                                headersOf(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                            )
                        }
                    } else {
                        respond("Not Found", HttpStatusCode.NotFound)
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(Resources)
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }

        fun success201KnitHttpClient(): HttpClient = createCustomHttpClient {
            mapOf(
                "/hr.employee.create" to HttpResponseConfig(
                    status = HttpStatusCode.Created,
                    content = Json.encodeToString(createEmployeeResponse(
                        success = true,
                        data = createEmployeeData(employeeId = "emp123456"),
                        errors = null,
                        responseCode = 201
                    ))
                ),
                "/hr.employees.document.upload" to HttpResponseConfig(
                    status = HttpStatusCode.Created,
                    content = Json.encodeToString(CreateDocumentResponse(
                        success = true,
                        data = DocumentData(documentId = "doc123"),
                        responseCode = 201
                    ))
                ),
            )
        }

        fun success201MergeDevHttpClient(): HttpClient = createCustomHttpClient {
            mapOf(
                "/api/accounting/v1/vendor-credits" to HttpResponseConfig(
                    status = HttpStatusCode.Created,
                    content = MergeDevAdapterTest().vendorCreditHttpTextBody(),
                )
            )
        }
    }
}

data class HttpResponseConfig(val status: HttpStatusCode, val content: String)

@Serializable
data class PassthroughRequest(
    val method: String,
    val path: String,
    val body: String? = null,
    val headers: Map<String, String>? = null,
)