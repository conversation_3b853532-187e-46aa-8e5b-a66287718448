package com.multiplier.integration.config

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.util.*

class AuditConfigTest {

    @Nested
    inner class CurrentUserWiringTest {
        private lateinit var currentUser: CurrentUser
        private lateinit var currentUserWiring: CurrentUserWiring

        @BeforeEach
        fun setup() {
            currentUser = mockk()
            currentUserWiring = CurrentUserWiring()
        }

        @Test
        fun `should return operations user id when available`() {
            // Given
            val context = mockk<UserContext>()
            val scopes = mockk<UserScopes>()
            every { currentUser.context } returns context
            every { context.scopes } returns scopes
            every { scopes.operationsUserId } returns 100L
            every { scopes.companyUserId } returns 200L

            // When
            val platformUser = currentUserWiring.platformUser(currentUser)

            // Then
            assertThat(platformUser.id).isEqualTo(100L)
        }

        @Test
        fun `should return company user id when operations user id is null`() {
            // Given
            val context = mockk<UserContext>()
            val scopes = mockk<UserScopes>()
            every { currentUser.context } returns context
            every { context.scopes } returns scopes
            every { scopes.operationsUserId } returns null
            every { scopes.companyUserId } returns 200L

            // When
            val platformUser = currentUserWiring.platformUser(currentUser)

            // Then
            assertThat(platformUser.id).isEqualTo(200L)
        }

        @Test
        fun `should return -1 when current user is null`() {
            // Act
            val platformUser = currentUserWiring.platformUser(null)

            // Assert
            assertThat(platformUser.userId).isEqualTo(-1L)
        }
    }

    @Nested
    inner class AuditorAwareImplTest {
        private lateinit var platformUser: PlatformUser
        private lateinit var auditorAwareImpl: AuditorAwareImpl

        @BeforeEach
        fun setup() {
            platformUser = mockk()
            auditorAwareImpl = AuditorAwareImpl(platformUser)
        }

        @Test
        fun `should return user id when platform user is available`() {
            // Arrange
            every { platformUser.userId } returns 100L

            // Act
            val result = auditorAwareImpl.getCurrentAuditor()

            // Assert
            assertThat(result).isEqualTo(Optional.of(100L))
        }

        @Test
        fun `should return empty optional when user id is -1`() {
            // Arrange
            every { platformUser.userId } returns -1L

            // Act
            val result = auditorAwareImpl.getCurrentAuditor()

            // Assert
            assertThat(result).isEqualTo(Optional.of(-1L))
        }
    }

    @Nested
    inner class AuditConfigTest {
        private lateinit var auditConfig: AuditConfig
        private lateinit var auditorAwareImpl: AuditorAwareImpl

        @BeforeEach
        fun setup() {
            auditConfig = AuditConfig()
            auditorAwareImpl = mockk()
        }

        @Test
        fun `should return auditor aware implementation`() {
            // Act
            val result = auditConfig.auditorAware(auditorAwareImpl)

            // Assert
            assertThat(result).isEqualTo(auditorAwareImpl)
        }
    }
}