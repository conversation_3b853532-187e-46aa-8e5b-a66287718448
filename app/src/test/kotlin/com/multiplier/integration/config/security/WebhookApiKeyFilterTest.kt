package com.multiplier.integration.config.security

import com.multiplier.integration.rest.RestEndpoint
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder

/**
 * Test-specific subclass that exposes the protected doFilterInternal method
 */
class TestableWebhookApiKeyFilter(apiKey: String, headerName: String) : WebhookApiKeyFilter(apiKey, headerName) {
    public override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        super.doFilterInternal(request, response, filterChain)
    }
}

@ExtendWith(MockitoExtension::class)
class WebhookApiKeyFilterTest {

    @Mock
    private lateinit var request: HttpServletRequest

    @Mock
    private lateinit var response: HttpServletResponse

    @Mock
    private lateinit var filterChain: FilterChain

    @Captor
    private lateinit var authenticationCaptor: ArgumentCaptor<Authentication>

    private val apiKey = "test-api-key"
    private val headerName = "X-API-KEY"

    @Nested
    inner class DoFilterInternal {

        @org.junit.jupiter.api.BeforeEach
        fun setUp() {
            // Clear security context before each test
            SecurityContextHolder.clearContext()
        }

        @org.junit.jupiter.api.AfterEach
        fun tearDown() {
            // Clear security context after each test
            SecurityContextHolder.clearContext()
        }

        @Test
        fun `should skip filter for non-webhook paths`() {
            // Given
            val filter = TestableWebhookApiKeyFilter(apiKey, headerName)
            Mockito.`when`(request.requestURI).thenReturn("/some-other-path")

            // When
            filter.doFilterInternal(request, response, filterChain)

            // Then
            Mockito.verify(filterChain).doFilter(request, response)
        }

        @Test
        fun `should authenticate request with valid API key`() {
            // Given
            val filter = TestableWebhookApiKeyFilter(apiKey, headerName)
            Mockito.`when`(request.requestURI).thenReturn(RestEndpoint.WEBHOOK_SFTP_V1)
            Mockito.`when`(request.getHeader(headerName)).thenReturn(apiKey)

            // When
            filter.doFilterInternal(request, response, filterChain)

            // Then
            Mockito.verify(filterChain).doFilter(request, response)
        }

        @Test
        fun `should reject request with invalid API key`() {
            // Given
            val filter = TestableWebhookApiKeyFilter(apiKey, headerName)
            Mockito.`when`(request.requestURI).thenReturn(RestEndpoint.WEBHOOK_SFTP_V1)
            Mockito.`when`(request.getHeader(headerName)).thenReturn("invalid-api-key")

            // When
            filter.doFilterInternal(request, response, filterChain)

            // Then
            Mockito.verify(response).setStatus(HttpServletResponse.SC_UNAUTHORIZED)
            Mockito.verify(filterChain, Mockito.never()).doFilter(request, response)
            Assertions.assertThat(SecurityContextHolder.getContext().authentication).isNull()
        }

        @Test
        fun `should reject request with missing API key`() {
            // Given
            val filter = TestableWebhookApiKeyFilter(apiKey, headerName)
            Mockito.`when`(request.requestURI).thenReturn(RestEndpoint.WEBHOOK_SFTP_V1)
            Mockito.`when`(request.getHeader(headerName)).thenReturn(null)

            // When
            filter.doFilterInternal(request, response, filterChain)

            // Then
            Mockito.verify(response).setStatus(HttpServletResponse.SC_UNAUTHORIZED)
            Mockito.verify(filterChain, Mockito.never()).doFilter(request, response)
        }
    }
}