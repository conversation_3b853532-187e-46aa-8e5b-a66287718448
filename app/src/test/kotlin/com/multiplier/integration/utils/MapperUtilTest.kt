package com.multiplier.integration.utils

import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha2CountryCode
import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha3CountryCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class MapperUtilTest {

    @Nested
    inner class MapToAlpha3CountryCode {

        @Test
        fun `map to Alpha3 CountryCode should return expected value`() {
            assertEquals("CAN", mapToAlpha3CountryCode("CA"))
            assertEquals("CAN", mapToAlpha3CountryCode("ca"))
            assertEquals("CAN", mapToAlpha3CountryCode("CAN"))
            assertEquals("CAN", mapToAlpha3CountryCode("can"))
            assertEquals("CAN", mapToAlpha3CountryCode("Canada"))
            assertEquals("CAN", mapToAlpha3CountryCode("canada"))
            assertEquals("Random", mapToAlpha3CountryCode("Random"))
        }

        @Test
        fun `map to Alpha2 CountryCode should return expected value`() {
            assertEquals("CA", mapToAlpha2CountryCode("CA"))
            assertEquals("CA", mapToAlpha2CountryCode("ca"))
            assertEquals("CA", mapToAlpha2CountryCode("CAN"))
            assertEquals("CA", mapToAlpha2CountryCode("can"))
            assertEquals("CA", mapToAlpha2CountryCode("Canada"))
            assertEquals("CA", mapToAlpha2CountryCode("canada"))
            assertEquals("GB", mapToAlpha2CountryCode("UK"))
            assertEquals("GB", mapToAlpha2CountryCode("United Kingdom"))
            assertEquals("US", mapToAlpha2CountryCode("United States of America"))
            assertEquals("Random", mapToAlpha2CountryCode("Random"))
        }

        @Test
        fun `should handle Paychex specific country code mapping`() {
            // Test SEN -> SGP mapping
            assertEquals("SGP", mapToAlpha3CountryCode("SEN", "Paychex"))
            assertEquals("SGP", mapToAlpha3CountryCode("sen", "Paychex"))
            assertEquals("SGP", mapToAlpha3CountryCode("Senegal", "Paychex"))
            
            // Test SGP -> SEN mapping
            assertEquals("SEN", mapToAlpha3CountryCode("SGP", "Paychex"))
            assertEquals("SEN", mapToAlpha3CountryCode("sgp", "Paychex"))
            assertEquals("SEN", mapToAlpha3CountryCode("Singapore", "Paychex"))
            
            // Test other country codes remain unchanged
            assertEquals("USA", mapToAlpha3CountryCode("USA", "Paychex"))
            assertEquals("USA", mapToAlpha3CountryCode("us", "Paychex"))
            assertEquals("USA", mapToAlpha3CountryCode("United States", "Paychex"))
            assertEquals("IND", mapToAlpha3CountryCode("IND", "Paychex"))
            assertEquals("IND", mapToAlpha3CountryCode("in", "Paychex"))
            assertEquals("IND", mapToAlpha3CountryCode("India", "Paychex"))
            
            // Test case insensitivity
            assertEquals("SGP", mapToAlpha3CountryCode("SEN", "PAYCHEX"))
            assertEquals("SEN", mapToAlpha3CountryCode("SGP", "paychex"))
            assertEquals("SGP", mapToAlpha3CountryCode("SEN", "PayChex"))
            
            // Test with empty platform name (should not apply Paychex mapping)
            assertEquals("SEN", mapToAlpha3CountryCode("SEN", ""))
            assertEquals("SGP", mapToAlpha3CountryCode("SGP", ""))
            
            // Test with different platform names (should not apply Paychex mapping)
            assertEquals("SEN", mapToAlpha3CountryCode("SEN", "Workday"))
            assertEquals("SGP", mapToAlpha3CountryCode("SGP", "BambooHR"))
        }
    }
}