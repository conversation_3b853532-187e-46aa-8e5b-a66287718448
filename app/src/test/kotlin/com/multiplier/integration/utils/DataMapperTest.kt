package com.multiplier.integration.utils

import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity.LegalEntityStatus
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.Capability
import io.mockk.impl.annotations.InjectMockKs
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals
import kotlin.test.assertNull

@ExtendWith(SpringExtension::class)
class DataMapperTest {
    @InjectMockKs
    private lateinit var dataMapper: DataMapper

    @Test
    fun `test map to legal entity successfully`() {
        val mockLegalEntity = CompanyOuterClass.LegalEntity.newBuilder()
                .setId(1L)
                .setCompanyId(100)
                .setStatus(LegalEntityStatus.ACTIVE)
                .setAddress(
                    CompanyOuterClass.Address.newBuilder()
                        .setCountry("USA")
                        .build()
                )
                .setLegalName("Test USA")
                .setCurrencyCode("USD")
                .setPhone("00000000")
                .setRegistrationNo("0000000")
                .addCapabilities(Capability.GROSS_TO_NET.name)
                .build()

        val resp = dataMapper.map(mockLegalEntity)

        assertEquals(1L, resp?.id)
        assertEquals("Test USA", resp?.legalName)
        assertEquals(com.multiplier.integration.types.LegalEntityStatus.ACTIVE, resp?.status)
    }

    @Test
    fun `test map to legal entity return null`() {
        val resp = dataMapper.map(grpcLegalEntity=null)

        assertNull(resp)
    }

    @Test
    fun `test map document folder`() {
        val mockId = "test"
        val mockLabel = "test"

        val resp = dataMapper.map(mockId, mockLabel)

        assertEquals(mockId, resp.id)
        assertEquals(mockLabel, resp.label)
    }
}