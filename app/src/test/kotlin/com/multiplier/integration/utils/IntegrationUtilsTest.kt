package com.multiplier.integration.utils

import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.types.PlatformCategory
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.function.Executable
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class IntegrationUtilsTest {

    @Test
    fun `checkOutgoingSyncEnabled should throw IntegrationIllegalStateException when outgoingSync is disabled`() {
        // Arrange
        val integrationMock: JpaCompanyIntegration = mock()
        whenever(integrationMock.outgoingSyncEnabled).thenReturn(false)
        whenever(integrationMock.id).thenReturn(123L)
        whenever(integrationMock.platform).thenReturn(JpaPlatform(123L, PlatformCategory.HRIS, "TestPlatform", true))

        // Act and Assert
        val exception = Assertions.assertThrows(IntegrationIllegalStateException::class.java) {
            checkOutgoingSyncEnabled(integrationMock)
        }

        exception.message?.let { Assertions.assertTrue(it.contains("Outgoing sync is disabled for integration id: 123 with platform: TestPlatform")) }
    }

    @Test
    fun `checkOutgoingSyncEnabled should not throw exception when outgoingSync is enabled`() {
        // Arrange
        val integrationMock: JpaCompanyIntegration = mock()
        whenever(integrationMock.outgoingSyncEnabled).thenReturn(true)

        // Act and Assert
        Assertions.assertDoesNotThrow(Executable { checkOutgoingSyncEnabled(integrationMock) })
    }
}
