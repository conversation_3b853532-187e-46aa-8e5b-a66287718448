package com.multiplier.integration.utils

import io.mockk.every
import io.mockk.mockkStatic
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.LocalDateTime

class FTPDirectoryUtilTest {

    @Nested
    inner class ExtractMainDirectoryPath {

        @Test
        fun `should extract main directory path excluding upload directory`() {
            // Given
            val fileURI = "c_aira/2253166/ukg/timesheets/upload/file.xlsx"

            // When
            val result = FTPDirectoryUtil.extractMainDirectoryPath(fileURI)

            // Then
            assertThat(result).isEqualTo("c_aira/2253166/ukg/timesheets/")
        }

        @Test
        fun `should handle paths without any subdirectory`() {
            // Given
            val fileURI = "c_aira/2253166/ukg/timesheets/file.xlsx"

            // When
            val result = FTPDirectoryUtil.extractMainDirectoryPath(fileURI)

            // Then
            // With the new implementation, we get the parent directory of the file's directory
            assertThat(result).isEqualTo("c_aira/2253166/ukg/")
        }

        @Test
        fun `should handle paths with any subdirectory`() {
            // Given
            val fileURI = "c_aira/2253166/ukg/timesheets/unknown/file.xlsx"

            // When
            val result = FTPDirectoryUtil.extractMainDirectoryPath(fileURI)

            // Then
            // With the new implementation, we get the parent directory of the file's directory
            assertThat(result).isEqualTo("c_aira/2253166/ukg/timesheets/")
        }

        @Test
        fun `should remove leading slash if present`() {
            // Given
            val fileURI = "/c_aira/2253166/ukg/timesheets/unknown/file.xlsx"

            // When
            val result = FTPDirectoryUtil.extractMainDirectoryPath(fileURI)

            // Then
            // With the new implementation, we get the parent directory of the file's directory
            assertThat(result).isEqualTo("c_aira/2253166/ukg/timesheets/")
        }
    }

    @Nested
    inner class GetFileName {

        @Test
        fun `should extract file name from URI`() {
            // Given
            val uri = "c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx"

            // When
            val result = FTPDirectoryUtil.getFileName(uri)

            // Then
            assertThat(result).isEqualTo("UKG TS Export Example.xlsx")
        }
    }

    @Nested
    inner class NormalizeDirectoryPath {

        @Test
        fun `should add trailing slash if missing`() {
            // Given
            val directoryPath = "sftp/company123/entity456"

            // When
            val result = FTPDirectoryUtil.normalizeDirectoryPath(directoryPath)

            // Then
            assertThat(result).isEqualTo("sftp/company123/entity456/")
        }

        @Test
        fun `should preserve trailing slash if already present`() {
            // Given
            val directoryPath = "sftp/company123/entity456/"

            // When
            val result = FTPDirectoryUtil.normalizeDirectoryPath(directoryPath)

            // Then
            assertThat(result).isEqualTo("sftp/company123/entity456/")
        }

        @Test
        fun `should remove leading slash if present`() {
            // Given
            val directoryPath = "/sftp/company123/entity456"

            // When
            val result = FTPDirectoryUtil.normalizeDirectoryPath(directoryPath)

            // Then
            assertThat(result).isEqualTo("sftp/company123/entity456/")
        }

        @Test
        fun `should remove leading slash and preserve trailing slash`() {
            // Given
            val directoryPath = "/sftp/company123/entity456/"

            // When
            val result = FTPDirectoryUtil.normalizeDirectoryPath(directoryPath)

            // Then
            assertThat(result).isEqualTo("sftp/company123/entity456/")
        }
    }

    @Nested
    inner class GetFilePath {

        @Test
        fun `should extract file path from URI`() {
            // Given
            val uri = "c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx"

            // When
            val result = FTPDirectoryUtil.getFilePath(uri)

            // Then
            assertThat(result).isEqualTo("c_aira/ukg/timesheets/upload")
        }
    }

    @Nested
    inner class GenerateProcessingFileName {

        @ParameterizedTest
        @ValueSource(
            strings = [
                "c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx",
                "c_aira/ukg/timesheets/upload/UKG TS Export Example.xls",
                "c_aira/ukg/timesheets/upload/UKG TS Export Example.csv",
            ]
        )
        fun `should generate processing file name`(originalFileURI: String) {
            // Mock LocalDateTime.now() to have fixed time for testing check
            mockkStatic(LocalDateTime::class)

            val fixedDateTime = LocalDateTime.of(2025, 4, 1, 9, 1, 2)
            every { LocalDateTime.now() } returns fixedDateTime

            // When
            val result = FTPDirectoryUtil.generateProcessingFileName(originalFileURI)

            // Then
            assertThat(result).startsWith("UKG TS Export Example_2025-04-01T09-01-02").containsAnyOf(".xlsx", ".xls", ".csv")
        }
    }

    @Nested
    inner class GenerateReportFileName {

        @ParameterizedTest
        @ValueSource(
            strings = [
                "c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-30T15-30-21.xlsx",
                "c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-30T15-30-21.xls",
                "c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-30T15-30-21.csv",
            ]
        )
        fun `should generate report file name`(originalFileURI: String) {
            // When
            val result = FTPDirectoryUtil.generateReportFileName(originalFileURI)

            // Then
            assertThat(result).startsWith("UKG TS Export Example_Report_2025-03-30T15-30-21.xlsx")
        }
    }

}
