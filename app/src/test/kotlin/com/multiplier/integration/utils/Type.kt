package com.multiplier.integration.utils

import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.adapter.api.resources.knit.FilterData
import com.multiplier.member.schema.CountryCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class Type {

    @Nested
    inner class CountryCodeFullName {

        @Test
        fun `should return full name for country`() {
            val result = CountryCode.SGP.getFullName()

            assertEquals("Singapore", result)
        }
    }

    @Nested
    inner class ToLocalDateTime {
        @Test
        fun `should handle datetime format`() {
            val date = "2022-11-02T00:00"
            val result = date.toLocalDateTime()

            assertEquals(2, result?.dayOfMonth)
        }
        @Test
        fun `should handle date format`() {
            val date = "2022-11-02"
            val result = date.toLocalDateTime()

            assertEquals(2, result?.dayOfMonth)
        }

        @Test
        fun `should return null`() {
            val date = ""
            val result = date.toLocalDateTime()

            assertNull(result)
        }
    }

    @Nested
    inner class ToPlatformId {
        @Test
        fun `should handle Hibob`() {
            val platform = "Hibob"
            val result = mapPlatformIdToKnitAppId(platform)

            assertEquals("hibob", result)
        }
        @Test
        fun `should handle Workday`() {
            val platform = "Workday"
            val result = mapPlatformIdToKnitAppId(platform)

            assertEquals("workday", result)
        }

        @Test
        fun `should handle BambooHR`() {
            val platform = "BambooHR"
            val result = mapPlatformIdToKnitAppId(platform)

            assertEquals("bamboohr", result)
        }

        @Test
        fun `should handle Oracle HCM`() {
            val platform = "Oracle HCM"
            val result = mapPlatformIdToKnitAppId(platform)

            assertEquals("oracle-hcm", result)
        }

        @Test
        fun `should handle Zoho People`() {
            val platform = "Zoho People"
            val result = mapPlatformIdToKnitAppId(platform)

            assertEquals("zoho-people", result)
        }
    }

    @Nested
    inner class ToCompensationData {
        @Test
        fun  `should return successfully`() {
            val mockRequest = "{\n" +
                    "      \"fixed\": [\n" +
                    "        {\n" +
                    "          \"type\": \"SALARY\",\n" +
                    "          \"amount\": 15000,\n" +
                    "          \"planId\": \"SALARY\",\n" +
                    "          \"endDate\": null,\n" +
                    "          \"currency\": \"INR\",\n" +
                    "          \"frequency\": \"MONTHLY\",\n" +
                    "          \"payPeriod\": \"ANNUAL\",\n" +
                    "          \"startDate\": \"2024-07-06T00:00:00Z\",\n" +
                    "          \"percentage\": null\n" +
                    "        }\n" +
                    "      ],\n" +
                    "      \"stock\": null,\n" +
                    "      \"variable\": null\n" +
                    "    }"

            val resp = mockRequest.toEmployeeCompensationData()

            assertEquals("SALARY", resp.fixed?.get(0)?.type)
            assertEquals("15000", resp.fixed?.get(0)?.amount)
        }
    }

    @Nested
    inner class FormatKnitKeys {
        @Test
        fun testReplaceAllExceptLast_singleOccurrence() {
            val input = "identificationData[]"
            val expected = "identificationData[0]"
            val result = replaceAllExceptLast(input, "0", "0")
            assertEquals(expected, result)
        }

        @Test
        fun testReplaceAllExceptLast_multipleOccurrences() {
            val input = "identificationData[].id.test[]"
            val expected = "identificationData[type=NATIONAL_ID].id.test[0]"
            val result = replaceAllExceptLast(input, "type=NATIONAL_ID", "0")
            assertEquals(expected, result)
        }

        @Test
        fun testReplaceAllExceptLast_noOccurrences() {
            val input = "identificationData"
            val expected = "identificationData"
            val result = replaceAllExceptLast(input, "one", "two")
            assertEquals(expected, result)
        }
    }
    @Nested
    inner class PopulateKeyFromLabel {
        @Test
        fun testPopulateKeyFromLabel_success() {
            val input = null
            val label = "Test Label"
            val expected = "customFields.fields.testLabel"
            val result = input.populateKeyFromLabel(label)
            assertEquals(expected, result)
        }

        @Test
        fun testPopulateKeyFromLabel_null() {
            val input = null
            val label = null
            val expected = null
            val result = input.populateKeyFromLabel(label)
            assertEquals(expected, result)
        }

        @Test
        fun testPopulateKeyFromLabel_notNull() {
            val input = "testLabel1"
            val label = "Test Label"
            val expected = "testLabel1"
            val result = input.populateKeyFromLabel(label)
            assertEquals(expected, result)
        }
    }

    @Nested
    inner class PopulateLabelFromKey {
        @Test
        fun testPopulateLabelFromKey_success() {
            val input = null
            val key = "test.testLabel"
            val expected = "Test Test Label"
            val result = input.populateLabelFromKey(key)
            assertEquals(expected, result)
        }

        @Test
        fun testPopulateLabelFromKey_null() {
            val input = null
            val label = null
            val expected = null
            val result = input.populateLabelFromKey(label)
            assertEquals(expected, result)
        }

        @Test
        fun testPopulateLabelFromKey_withIndex() {
            val input = null
            val key = "test.testLabel[0]"
            val expected = "Test Test Label"
            val result = input.populateLabelFromKey(key)
            assertEquals(expected, result)
        }

        @Test
        fun testPopulateLabelFromKey_withoutDot() {
            val input = null
            val key = "testLabel"
            val expected = "Test Label"
            val result = input.populateLabelFromKey(key)
            assertEquals(expected, result)
        }
    }

    @Nested
    inner class FormatExternalKey {
        @Test
        fun testFormatExternalKey_successfully() {
            val input = FieldData(
                mappedKey = "employeeIdentificationData[].number",
                isCustomField = false,
                filters = listOf(
                    FilterData(
                        key = "type",
                        value = "NATIONAL_ID"
                    )
                )
            )
            val expected = "employeeIdentificationData[type=NATIONAL_ID].number"

            val resp = formatExternalKey(input, true)

            assertEquals(expected, resp)
        }

        @Test
        fun testFormatExternalKey_isNotMapped() {
            val input = FieldData(
                mappedKey = "employeeIdentificationData[].number",
                isCustomField = false,
                filters = listOf(
                    FilterData(
                        key = "type",
                        value = "NATIONAL_ID"
                    )
                )
            )
            val expected = null

            val resp = formatExternalKey(input, false)

            assertEquals(expected, resp)
        }
    }
}
