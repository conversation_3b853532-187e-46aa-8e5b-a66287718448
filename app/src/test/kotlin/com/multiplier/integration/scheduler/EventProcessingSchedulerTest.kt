package com.multiplier.integration.scheduler

import com.multiplier.integration.accounting.domain.FinancialTransactionAmountUpdatedHandlerService
import com.multiplier.integration.accounting.domain.FinancialTransactionHandlerService
import com.multiplier.integration.adapter.api.BenefitServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocGenServiceAdapterV2
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.handlers.company.PayableEventHandler
import com.multiplier.integration.handlers.contract.*
import com.multiplier.integration.handlers.member.MemberDetailUpdateHandler
import com.multiplier.integration.mock.getEmptyFailedEvents
import com.multiplier.integration.mock.getFailedEvents
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class EventProcessingSchedulerTest {
    @MockK
    lateinit var eventLogService: EventLogService

    @MockK
    lateinit var employeeService: EmployeeService
    @MockK
    lateinit var memberServiceAdapter: MemberServiceAdapter
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK
    lateinit var benefitServiceAdapter: BenefitServiceAdapter
    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter
    @MockK
    lateinit var docgenServiceAdapterV2: DocGenServiceAdapterV2
    @MockK
    lateinit var notificationsService: NotificationsService

    @MockK
    lateinit var contractOnboardingStatusUpdateHandler: ContractOnboardingStatusUpdateEventHandler

    @MockK
    lateinit var memberDetailUpdateHandler: MemberDetailUpdateHandler

    @MockK
    lateinit var contractDocumentUpdateHandler: ContractDocumentUpdateEventHandler

    @MockK
    lateinit var payrollDocumentEventHandler: PayrollDocumentEventHandler

    @MockK
    lateinit var salaryReviewDocumentEventHandler: SalaryReviewDocumentEventHandler

    @MockK
    lateinit var insuranceOnboardingKitUpdateEventHandler: InsuranceOnboardingKitUpdateEventHandler

    @MockK
    lateinit var insuranceFactsheetKitUpdateEventHandler: InsuranceFactsheetKitUpdateEventHandler

    @MockK
    lateinit var compensationCreationEventHandler: CompensationCreationEventHandler

    @MockK
    lateinit var contractOffboardingStatusUpdateHandler: ContractOffboardingStatusUpdateEventHandler

    @MockK
    lateinit var eventLogRepository: EventLogRepository

    @MockK
    lateinit var financialTransactionHandlerService: FinancialTransactionHandlerService

    @MockK
    lateinit var payableEventHandler: PayableEventHandler

    @MockK
    lateinit var financialTransactionAmountUpdatedHandlerService: FinancialTransactionAmountUpdatedHandlerService

    @InjectMockKs
    lateinit var testScheduler: EventProcessingScheduler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should retry failed events (no failed event)`() {
        every { eventLogService.fetchToBeProcessedEvents() } returns getEmptyFailedEvents()
        testScheduler.processEvents()
        verify(exactly = 0) { eventLogService.processEvent(any()) }
    }

    @Test
    fun `should retry failed events`() {
        val mockFailedEvents =
            getFailedEvents(eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE) +
            getFailedEvents(eventType = EventType.INCOMING_OFFBOARDING_STATUS_UPDATE) +
            getFailedEvents(eventType = EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED) +
            getFailedEvents(eventType = EventType.INCOMING_MEMBER_ADDRESS_UPDATED) +
            getFailedEvents(eventType = EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED) +
            getFailedEvents(eventType = EventType.INCOMING_SALARY_REVIEW_ACTIVATED) +
            getFailedEvents(eventType = EventType.SERVICE_INTERNAL_CREATE_COMPENSATION) +
            getFailedEvents(eventType = EventType.INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE) +
            getFailedEvents(eventType = EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED) +
            getFailedEvents(eventType = EventType.INCOMING_PAYROLL_PAYSLIP_PUBLISHED) +
            getFailedEvents(eventType = EventType.INCOMING_SALARY_REVIEW_SIGNED) +
            getFailedEvents(eventType = EventType.SERVICE_INTERNAL_CREATE_INSURANCE_FACTSHEET) +
            getFailedEvents(eventType = EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT) +
            getFailedEvents(eventType = EventType.INCOMING_PAYABLE_UPDATE)

        testScheduler.setPrivateField("contractOnboardingStatusUpdateHandler", contractOnboardingStatusUpdateHandler);
        testScheduler.setPrivateField("contractOnboardingStatusUpdateHandler", contractOnboardingStatusUpdateHandler)
        testScheduler.setPrivateField("contractOffboardingStatusUpdateHandler", contractOffboardingStatusUpdateHandler)
        testScheduler.setPrivateField("memberDetailUpdateHandler", memberDetailUpdateHandler)
        testScheduler.setPrivateField("compensationCreationEventHandler", compensationCreationEventHandler)
        testScheduler.setPrivateField("contractDocumentUpdateHandler", contractDocumentUpdateHandler)
        testScheduler.setPrivateField("payrollDocumentEventHandler", payrollDocumentEventHandler)
        testScheduler.setPrivateField("salaryReviewDocumentEventHandler", salaryReviewDocumentEventHandler)
        testScheduler.setPrivateField(
            "insuranceFactsheetKitUpdateEventHandler", insuranceFactsheetKitUpdateEventHandler)
        testScheduler.setPrivateField(
            "insuranceOnboardingKitUpdateEventHandler", insuranceOnboardingKitUpdateEventHandler)
        testScheduler.setPrivateField("payableEventHandler", payableEventHandler)

        every { contractOnboardingStatusUpdateHandler.handleContractOnboardingStatusUpdateEvent(any()) } returns Unit
        every { contractOffboardingStatusUpdateHandler.handleContractOffboardingStatusUpdateEvent(any()) } returns Unit
        every { memberDetailUpdateHandler.handleBasicDetailsUpdate(any()) } returns Unit
        every { compensationCreationEventHandler.handleCompensationUpdationEvent(any()) } returns Unit
        every { compensationCreationEventHandler.handleCompensationCreationEvent(any()) } returns Unit
        every { contractDocumentUpdateHandler.handleContractDocumentUpdateEvent(any()) } returns Unit
        every { payrollDocumentEventHandler.handleUploadPayslipEvent(any()) } returns Unit
        every { payrollDocumentEventHandler.handlePublishPayslipEvent(any()) } returns Unit
        every { salaryReviewDocumentEventHandler.handleSalaryReviewDocumentUpdateEvent(any()) } returns Unit
        every { insuranceFactsheetKitUpdateEventHandler.handleInsuranceFactsheetUpdateEvent(any()) } returns Unit
        every { insuranceOnboardingKitUpdateEventHandler.handleInsuranceOnboardingKitUpdateEvent(any()) } returns Unit
        every { payableEventHandler.handlePayableEvent(any()) } returns Unit

        every { eventLogService.fetchToBeProcessedEvents() } returns mockFailedEvents

        testScheduler.processEvents()

        verify(exactly = 1) { contractOnboardingStatusUpdateHandler.handleContractOnboardingStatusUpdateEvent(any()) }
        verify(exactly = 1) { contractOffboardingStatusUpdateHandler.handleContractOffboardingStatusUpdateEvent(any()) }
        verify(exactly = 3) { memberDetailUpdateHandler.handleBasicDetailsUpdate(any()) }
        verify(exactly = 1) { compensationCreationEventHandler.handleCompensationUpdationEvent(any()) }
        verify(exactly = 1) { compensationCreationEventHandler.handleCompensationCreationEvent(any()) }
        verify(exactly = 1) { contractDocumentUpdateHandler.handleContractDocumentUpdateEvent(any()) }
        verify(exactly = 1) { payrollDocumentEventHandler.handleUploadPayslipEvent(any()) }
        verify(exactly = 1) { payrollDocumentEventHandler.handlePublishPayslipEvent(any()) }
        verify(exactly = 1) { salaryReviewDocumentEventHandler.handleSalaryReviewDocumentUpdateEvent(any()) }
        verify(exactly = 1) { insuranceFactsheetKitUpdateEventHandler.handleInsuranceFactsheetUpdateEvent(any()) }
        verify(exactly = 1) { insuranceOnboardingKitUpdateEventHandler.handleInsuranceOnboardingKitUpdateEvent(any()) }
        verify(exactly = 1) { payableEventHandler.handlePayableEvent(any()) }
    }

}