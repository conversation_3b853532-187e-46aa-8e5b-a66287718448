package com.multiplier.integration.mock

import UpdateEmployeeResponse
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.type.Date
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.kafka.onboarding.ContractOnboardingEventMessageOuterClass
import com.multiplier.contract.offboarding.schema.ContractOffboarding
import com.multiplier.contract.offboarding.schema.ContractOffboardingStatus
import com.multiplier.contract.offboarding.schema.ContractOffboardingType
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractStatus
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractType
import com.multiplier.contract.schema.currency.Currency.CurrencyCode
import com.multiplier.contract.schema.onboarding.Onboarding
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.core.schema.grpc.benefit.Benefit.ContractBenefitDocumentsResponse
import com.multiplier.country.schema.currency.Currency
import com.multiplier.expense.schema.BulkCreateExpensesResponse
import com.multiplier.expense.schema.CreateExpensesRequest
import com.multiplier.expense.schema.GrpcBulkExpenseRequestInput
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.*
import com.multiplier.integration.adapter.api.resources.knit.hibob.*
import com.multiplier.integration.adapter.api.resources.knit.successfactors.*
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategoriesData
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategoriesResponse
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategory
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.adapter.model.knit.Compensation
import com.multiplier.integration.adapter.model.knit.Profile
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.repository.type.EntityType
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.CreateExpensesRequestWithReportId
import com.multiplier.integration.sync.model.*
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.utils.toDate
import com.multiplier.integration.utils.toGrpcTimestamp
import com.multiplier.member.schema.*
import com.multiplier.member.schema.Address
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member.MemberStatus
import dev.merge.client.hris.models.RemoteResponse
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import com.multiplier.integration.adapter.model.knit.CompensationData as knitCompensationData
import com.multiplier.integration.adapter.model.knit.EmployeeData as knitEmployeeData
import com.multiplier.integration.repository.model.EventType as modelEventType
import com.multiplier.integration.repository.type.EventType as typeEventType

fun getMockReceivedEventsWithCustomCountry(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_NEW,
): List<JpaReceivedEvent> {
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = """
                {
                    "eventId": "ev_5t0qc2z7kVRbaJH5UL4aAg",
                    "recordId": "3277135280330507201",
                    "syncType": "delta_sync",
                    "eventData": {
                        "profile": {
                            "id": "3277135280330507201",
                            "gender": $gender,
                            "lastName": $lastName,
                            "birthDate": $birthDate,
                            "firstName": $firstName,
                            "startDate": "1979-12-25T00:00:00Z",
                            "workEmail": "<EMAIL>",
                            "maritalStatus": $maritalStatus,
                            "employeeNumber": "789",
                            "employmentType": "FULL_TIME",
                            "terminationDate": $terminationDate,
                            "employmentStatus": $status
                        },
                        "terminationDate": $terminationDate,
                        "locations": {
                            "workAddress": {
                                "city": null,
                                "state": null,
                                "country": null,
                                "zipCode": null,
                                "addressType": "WORK",
                                "addressLine1": "Canada",
                                "addressLine2": null
                            },
                            "presentAddress": null,
                            "permanentAddress": null
                        },
                        "rawValues": {
                            "profile": {
                                "gender": "Female",
                                "maritalStatus": "Divorced",
                                "employmentType": "Full time",
                                "employmentStatus": "Active"
                            }
                        },
                        "dependents": [{
                                "firstName": "test"
                            }],
                        "contactInfo": {
                            "phones": [
                                {
                                    "type": "WORK",
                                    "number": "+****************"
                                }
                            ],
                            "personalEmails": [
                                "<EMAIL>"
                            ]
                        },
                        "employeeKYC": null,
                        "bankAccounts": null,
                        "compensation": {
                            "fixed": [
                                {
                                    "type": "SALARY",
                                    "amount": $compensationAmount,
                                    "planId": "SALARY",
                                    "endDate": null,
                                    "currency": "NZD",
                                    "frequency": "WEEKLY",
                                    "payPeriod": "WEEKLY",
                                    "startDate": "2024-01-10T00:00:00Z",
                                    "percentage": null
                                }
                            ],
                            "stock": null,
                            "variable": $variable
                        },
                        "customFields": {
                            "fields": {
                                "country": "CA"
                            }
                        },
                        "orgStructure": {
                            "manager": null,
                            "department": "Accounting",
                            "designation": "Account Manager"
                        },
                        "bankAccountDetails": null,
                        "employeeProfilePicture": {
                            "pictureURL": "https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary",
                            "pictureName": null
                        },
                        "employeeIdentificationData": [
                            {
                                "type": "NATIONAL_ID",
                                "subType": "SSN",
                                "identificationNumber": "***********"
                            },
                            {
                                "type": "NATIONAL_ID",
                                "subType": null,
                                "identificationNumber": "3898820462"
                            },
                            {
                                "type": "PASSPORT",
                                "subType": null,
                                "identificationNumber": "P7894834"
                            }
                        ]
                    },
                    "eventType": "record.modified",
                    "syncJobId": "sj_sFJC78RTPyB9vvpH0m83hk",
                    "syncRunId": "sr_oNnWVOz8NRRlN1mGZqx1uy",
                    "triggeredAt": "*************",
                    "syncDataType": "employee"
                }
                """.trimIndent(),
            errors = null,
        )
    )
}

fun getMockReceivedEvents(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
): List<JpaReceivedEvent> {
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = """
                {
                    "eventId": "ev_5t0qc2z7kVRbaJH5UL4aAg",
                    "recordId": "3277135280330507201",
                    "syncType": "delta_sync",
                    "eventData": {
                        "profile": {
                            "id": "3277135280330507201",
                            "gender": $gender,
                            "lastName": $lastName,
                            "birthDate": $birthDate,
                            "firstName": $firstName,
                            "startDate": "1979-12-25T00:00:00Z",
                            "workEmail": "<EMAIL>",
                            "maritalStatus": $maritalStatus,
                            "employeeNumber": "789",
                            "employmentType": "FULL_TIME",
                            "terminationDate": $terminationDate,
                            "employmentStatus": $status
                        },
                        "terminationDate": $terminationDate,
                        "locations": {
                            "workAddress": {
                                "city": null,
                                "state": null,
                                "country": "US",
                                "zipCode": null,
                                "addressType": "WORK",
                                "addressLine1": null,
                                "addressLine2": null
                            },
                            "presentAddress": null,
                            "permanentAddress": null
                        },
                        "rawValues": {
                            "profile": {
                                "gender": "Female",
                                "maritalStatus": "Divorced",
                                "employmentType": "Full time",
                                "employmentStatus": "Active"
                            }
                        },
                        "dependents": [{
                                "firstName": "test"
                            }],
                        "contactInfo": {
                            "phones": [
                                {
                                    "type": "WORK",
                                    "number": "+****************"
                                }
                            ],
                            "personalEmails": [
                                "<EMAIL>"
                            ]
                        },
                        "employeeKYC": null,
                        "bankAccounts": null,
                        "compensation": {
                            "fixed": [
                                {
                                    "type": "SALARY",
                                    "amount": $compensationAmount,
                                    "planId": "SALARY",
                                    "endDate": null,
                                    "currency": "NZD",
                                    "frequency": "WEEKLY",
                                    "payPeriod": "WEEKLY",
                                    "startDate": "2024-01-10T00:00:00Z",
                                    "percentage": null
                                }
                            ],
                            "stock": null,
                            "variable": $variable
                        },
                        "customFields": null,
                        "orgStructure": {
                            "manager": null,
                            "department": "Accounting",
                            "designation": "Account Manager"
                        },
                        "bankAccountDetails": null,
                        "employeeProfilePicture": {
                            "pictureURL": "https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary",
                            "pictureName": null
                        },
                        "employeeIdentificationData": [
                            {
                                "type": "NATIONAL_ID",
                                "subType": "SSN",
                                "identificationNumber": "***********"
                            },
                            {
                                "type": "NATIONAL_ID",
                                "subType": null,
                                "identificationNumber": "3898820462"
                            },
                            {
                                "type": "PASSPORT",
                                "subType": null,
                                "identificationNumber": "P7894834"
                            }
                        ]
                    },
                    "eventType": "record.modified",
                    "syncJobId": "sj_sFJC78RTPyB9vvpH0m83hk",
                    "syncRunId": "sr_oNnWVOz8NRRlN1mGZqx1uy",
                    "triggeredAt": "*************",
                    "syncDataType": "employee"
                }
                """.trimIndent(),
            errors = null,
        )
    )
}

fun getMockReceivedEventsWithBankAccountsCustom(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
    customFields: String? = null,
    dependents: String? = null,
    email: String? = null,
    workEmail: String? = null,
    locations: String? = """
                {
                    "workAddress": {
                        "city": null,
                        "state": "CA",
                        "country": "US",
                        "zipCode": null,
                        "addressType": "WORK",
                        "addressLine1": null,
                        "addressLine2": null
                    },
                    "presentAddress": null,
                    "permanentAddress": null
                }
            """.trimIndent(),
): List<JpaReceivedEvent> {
    val bankAccounts = """
                    [
                        {
                            "bankName": "Bank of Montreal",
                            "payTypes": [
                                "ALL"
                            ],
                            "accountType": "SAVINGS",
                            "routingInfo": [
                                {
                                    "type": "SWIFT_CODE",
                                    "number": "BOFMCAM2XXX"
                                }
                            ],
                            "accountNumber": "3960112"
                        }
                    ]
                    """.trimIndent()
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = """
                {
                    "eventId": "ev_5t0qc2z7kVRbaJH5UL4aAg",
                    "recordId": "3277135280330507201",
                    "syncType": "delta_sync",
                    "eventData": {
                        "profile": {
                            "id": "3277135280330507201",
                            "gender": $gender,
                            "lastName": $lastName,
                            "birthDate": $birthDate,
                            "firstName": $firstName,
                            "startDate": "1979-12-25T00:00:00Z",
                            "workEmail": $workEmail,
                            "maritalStatus": $maritalStatus,
                            "employeeNumber": "789",
                            "employmentType": "FULL_TIME",
                            "terminationDate": $terminationDate,
                            "employmentStatus": $status
                        },
                        "terminationDate": $terminationDate,
                        "locations": $locations,
                        "rawValues": {
                            "profile": {
                                "gender": "Female",
                                "maritalStatus": "Divorced",
                                "employmentType": "Full time",
                                "employmentStatus": "Active"
                            }
                        },
                        "dependents": $dependents,
                        "contactInfo": {
                            "phones": [
                                {
                                    "type": "WORK",
                                    "number": "+****************"
                                }
                            ],
                            "personalEmails": [
                                $email
                            ]
                        },
                        "employeeKYC": null,
                        "bankAccounts": $bankAccounts,
                        "compensation": {
                            "fixed": [
                                {
                                    "type": "SALARY",
                                    "amount": $compensationAmount,
                                    "planId": "SALARY",
                                    "endDate": null,
                                    "currency": "NZD",
                                    "frequency": "WEEKLY",
                                    "payPeriod": "WEEKLY",
                                    "startDate": "2024-01-10T00:00:00Z",
                                    "percentage": null
                                }
                            ],
                            "stock": null,
                            "variable": $variable
                        },
                        "customFields": $customFields,
                        "orgStructure": {
                            "manager": null,
                            "department": "Accounting",
                            "designation": "Account Manager"
                        },
                        "bankAccountDetails": null,
                        "employeeProfilePicture": {
                            "pictureURL": "https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary",
                            "pictureName": null
                        },
                        "employeeIdentificationData": null
                    },
                    "eventType": "record.modified",
                    "syncJobId": "sj_sFJC78RTPyB9vvpH0m83hk",
                    "syncRunId": "sr_oNnWVOz8NRRlN1mGZqx1uy",
                    "triggeredAt": "*************",
                    "syncDataType": "employee"
                }
                """.trimIndent(),
            errors = null,
        )
    )
}

fun getMockReceivedEventsWithBankAccounts(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
): List<JpaReceivedEvent> {
    val bankAccounts = """
                    [
                        {
                            "bankName": "Bank of Montreal",
                            "payTypes": [
                                "ALL"
                            ],
                            "accountType": "SAVINGS",
                            "routingInfo": [
                                {
                                    "type": "SWIFT_CODE",
                                    "number": "BOFMCAM2XXX"
                                }
                            ],
                            "accountNumber": "3960112"
                        }
                    ]
                    """.trimIndent()
    val customFields = """
        {
            "fields": {
                    "beneficiaryName": "customTest",
                    "maritalStatus": "MARRIED",
                    "nationality": "USA"
                }
        }
    """.trimIndent()
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = """
                {
                    "eventId": "ev_5t0qc2z7kVRbaJH5UL4aAg",
                    "recordId": "3277135280330507201",
                    "syncType": "delta_sync",
                    "eventData": {
                        "profile": {
                            "id": "3277135280330507201",
                            "gender": $gender,
                            "lastName": $lastName,
                            "birthDate": $birthDate,
                            "firstName": $firstName,
                            "startDate": "1979-12-25T00:00:00Z",
                            "workEmail": "<EMAIL>",
                            "maritalStatus": $maritalStatus,
                            "employeeNumber": "789",
                            "employmentType": "FULL_TIME",
                            "terminationDate": $terminationDate,
                            "employmentStatus": $status
                        },
                        "terminationDate": $terminationDate,
                        "locations": {
                            "workAddress": {
                                "city": null,
                                "state": null,
                                "country": "US",
                                "zipCode": null,
                                "addressType": "WORK",
                                "addressLine1": null,
                                "addressLine2": null
                            },
                            "presentAddress": null,
                            "permanentAddress": null
                        },
                        "rawValues": {
                            "profile": {
                                "gender": "Female",
                                "maritalStatus": "Divorced",
                                "employmentType": "Full time",
                                "employmentStatus": "Active"
                            }
                        },
                        "dependents": [
                            {
                                "firstName": "test"
                            }
                        ],
                        "contactInfo": {
                            "phones": [
                                {
                                    "type": "WORK",
                                    "number": "+****************"
                                }
                            ],
                            "personalEmails": [
                                "<EMAIL>"
                            ]
                        },
                        "employeeKYC": null,
                        "bankAccounts": $bankAccounts,
                        "compensation": {
                            "fixed": [
                                {
                                    "type": "SALARY",
                                    "amount": $compensationAmount,
                                    "planId": "SALARY",
                                    "endDate": null,
                                    "currency": "NZD",
                                    "frequency": "WEEKLY",
                                    "payPeriod": "WEEKLY",
                                    "startDate": "2024-01-10T00:00:00Z",
                                    "percentage": null
                                }
                            ],
                            "stock": null,
                            "variable": $variable
                        },
                        "customFields": $customFields,
                        "orgStructure": {
                            "manager": null,
                            "department": "Accounting",
                            "designation": "Account Manager"
                        },
                        "bankAccountDetails": null,
                        "employeeProfilePicture": {
                            "pictureURL": "https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary",
                            "pictureName": null
                        },
                        "employeeIdentificationData": null
                    },
                    "eventType": "record.modified",
                    "syncJobId": "sj_sFJC78RTPyB9vvpH0m83hk",
                    "syncRunId": "sr_oNnWVOz8NRRlN1mGZqx1uy",
                    "triggeredAt": "*************",
                    "syncDataType": "employee"
                }
                """.trimIndent(),
            errors = null,
        )
    )
}

fun getMockReceivedEventsWithBlankBankAccounts(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
): List<JpaReceivedEvent> {
    val bankAccounts = """
                    [
                        {
                            "bankName": null,
                            "payTypes": [
                                "ALL"
                            ],
                            "accountType": null,
                            "routingInfo": [
                                {
                                    "type": null,
                                    "number": null
                                }
                            ],
                            "accountNumber": null
                        }
                    ]
                    """.trimIndent()
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = "{\"eventId\": \"ev_5t0qc2z7kVRbaJH5UL4aAg\", \"recordId\": \"3277135280330507201\", \"syncType\": \"delta_sync\", \"eventData\": {\"profile\": {\"id\": \"3277135280330507201\", \"gender\": $gender, \"lastName\": $lastName, \"birthDate\": $birthDate, \"firstName\": $firstName, \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": $maritalStatus, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": $terminationDate, \"employmentStatus\": $status}, \"terminationDate\": $terminationDate, \"locations\": {\"workAddress\": {\"city\": null, \"state\": null, \"country\": \"US\", \"zipCode\": null, \"addressType\": \"WORK\", \"addressLine1\": null, \"addressLine2\": null}, \"presentAddress\": null, \"permanentAddress\": null}, \"rawValues\": {\"profile\": {\"gender\": \"Female\", \"maritalStatus\": \"Divorced\", \"employmentType\": \"Full time\", \"employmentStatus\": \"Active\"}}, \"dependents\": null, \"contactInfo\": {\"phones\": [{\"type\": \"WORK\", \"number\": \"+****************\"}], \"personalEmails\": [\"<EMAIL>\"]}, \"employeeKYC\": null, \"bankAccounts\": $bankAccounts, \"compensation\": {\"fixed\": [{\"type\": \"SALARY\", \"amount\": $compensationAmount, \"planId\": \"SALARY\", \"endDate\": null, \"currency\": \"NZD\", \"frequency\": \"WEEKLY\", \"payPeriod\": \"WEEKLY\", \"startDate\": \"2024-01-10T00:00:00Z\", \"percentage\": null}], \"stock\": null, \"variable\": $variable}, \"customFields\": null, \"orgStructure\": {\"manager\": null, \"department\": \"Accounting\", \"designation\": \"Account Manager\"}, \"bankAccountDetails\": null, \"employeeProfilePicture\": {\"pictureURL\": \"https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary\", \"pictureName\": null}, \"employeeIdentificationData\": null}, \"eventType\": \"record.modified\", \"syncJobId\": \"sj_sFJC78RTPyB9vvpH0m83hk\", \"syncRunId\": \"sr_oNnWVOz8NRRlN1mGZqx1uy\", \"triggeredAt\": \"*************\", \"syncDataType\": \"employee\"}",
            errors = null,
        )
    )
}

fun getMockReceivedEventsWithBankAccountsEmpty(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
): List<JpaReceivedEvent> {
    val bankAccounts = "[]"
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = "{\"eventId\": \"ev_5t0qc2z7kVRbaJH5UL4aAg\", \"recordId\": \"3277135280330507201\", \"syncType\": \"delta_sync\", \"eventData\": {\"profile\": {\"id\": \"3277135280330507201\", \"gender\": $gender, \"lastName\": $lastName, \"birthDate\": $birthDate, \"firstName\": $firstName, \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": $maritalStatus, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": $terminationDate, \"employmentStatus\": $status}, \"terminationDate\": $terminationDate, \"locations\": {\"workAddress\": {\"city\": null, \"state\": null, \"country\": \"US\", \"zipCode\": null, \"addressType\": \"WORK\", \"addressLine1\": null, \"addressLine2\": null}, \"presentAddress\": null, \"permanentAddress\": null}, \"rawValues\": {\"profile\": {\"gender\": \"Female\", \"maritalStatus\": \"Divorced\", \"employmentType\": \"Full time\", \"employmentStatus\": \"Active\"}}, \"dependents\": null, \"contactInfo\": {\"phones\": [{\"type\": \"WORK\", \"number\": \"+****************\"}], \"personalEmails\": [\"<EMAIL>\"]}, \"employeeKYC\": null, \"bankAccounts\": $bankAccounts, \"compensation\": {\"fixed\": null, \"stock\": null, \"variable\": $variable}, \"customFields\": null, \"orgStructure\": {\"manager\": null, \"department\": \"Accounting\", \"designation\": \"Account Manager\"}, \"bankAccountDetails\": null, \"employeeProfilePicture\": {\"pictureURL\": \"https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary\", \"pictureName\": null}, \"employeeIdentificationData\": null}, \"eventType\": \"record.modified\", \"syncJobId\": \"sj_sFJC78RTPyB9vvpH0m83hk\", \"syncRunId\": \"sr_oNnWVOz8NRRlN1mGZqx1uy\", \"triggeredAt\": \"*************\", \"syncDataType\": \"employee\"}",
            errors = null,
        )
    )
}

fun getMockReceivedEventsWithBankAccountsNullAccountType(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
): List<JpaReceivedEvent> {
    val bankAccounts = "[\n" +
            "      {\n" +
            "        \"bankName\": \"Bank of Montreal\",\n" +
            "        \"payTypes\": [\n" +
            "          \"ALL\"\n" +
            "        ],\n" +
            "        \"accountType\": null,\n" +
            "        \"routingInfo\": [\n" +
            "          {\n" +
            "            \"type\": \"SWIFT_CODE\",\n" +
            "            \"number\": \"BOFMCAM2XXX\"\n" +
            "          }\n" +
            "        ],\n" +
            "        \"accountNumber\": \"3960112\"\n" +
            "      }\n" +
            "    ]"
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = "{\"eventId\": \"ev_5t0qc2z7kVRbaJH5UL4aAg\", \"recordId\": \"3277135280330507201\", \"syncType\": \"delta_sync\", \"eventData\": {\"profile\": {\"id\": \"3277135280330507201\", \"gender\": $gender, \"lastName\": $lastName, \"birthDate\": $birthDate, \"firstName\": $firstName, \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": $maritalStatus, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": $terminationDate, \"employmentStatus\": $status}, \"terminationDate\": $terminationDate, \"locations\": {\"workAddress\": {\"city\": null, \"state\": null, \"country\": \"US\", \"zipCode\": null, \"addressType\": \"WORK\", \"addressLine1\": null, \"addressLine2\": null}, \"presentAddress\": null, \"permanentAddress\": null}, \"rawValues\": {\"profile\": {\"gender\": \"Female\", \"maritalStatus\": \"Divorced\", \"employmentType\": \"Full time\", \"employmentStatus\": \"Active\"}}, \"dependents\": null, \"contactInfo\": {\"phones\": [{\"type\": \"WORK\", \"number\": \"+****************\"}], \"personalEmails\": [\"<EMAIL>\"]}, \"employeeKYC\": null, \"bankAccounts\": $bankAccounts, \"compensation\": {\"fixed\": [{\"type\": \"SALARY\", \"amount\": $compensationAmount, \"planId\": \"SALARY\", \"endDate\": null, \"currency\": \"NZD\", \"frequency\": \"WEEKLY\", \"payPeriod\": \"WEEKLY\", \"startDate\": \"2024-01-10T00:00:00Z\", \"percentage\": null}], \"stock\": null, \"variable\": $variable}, \"customFields\": null, \"orgStructure\": {\"manager\": null, \"department\": \"Accounting\", \"designation\": \"Account Manager\"}, \"bankAccountDetails\": null, \"employeeProfilePicture\": {\"pictureURL\": \"https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary\", \"pictureName\": null}, \"employeeIdentificationData\": null}, \"eventType\": \"record.modified\", \"syncJobId\": \"sj_sFJC78RTPyB9vvpH0m83hk\", \"syncRunId\": \"sr_oNnWVOz8NRRlN1mGZqx1uy\", \"triggeredAt\": \"*************\", \"syncDataType\": \"employee\"}",
            errors = null,
        )
    )
}

fun getMockReceivedEventsNoCompensation(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
): List<JpaReceivedEvent> {
    val bankAccounts = null
    return listOf(
        JpaReceivedEvent(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
            integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
            eventType = eventType,
            confirmedByUser = true,
            processed = false,
            id = 1L,
            receivedTime = LocalDateTime.now(),
            identifiervalue = "3277135280330507201",
            syncDataType = "employer",
            data = "{\"eventId\": \"ev_5t0qc2z7kVRbaJH5UL4aAg\", \"recordId\": \"3277135280330507201\", \"syncType\": \"delta_sync\", \"eventData\": {\"profile\": {\"id\": \"3277135280330507201\", \"gender\": $gender, \"lastName\": $lastName, \"birthDate\": $birthDate, \"firstName\": $firstName, \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": $maritalStatus, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": $terminationDate, \"employmentStatus\": $status}, \"terminationDate\": $terminationDate, \"locations\": {\"workAddress\": {\"city\": null, \"state\": null, \"country\": \"US\", \"zipCode\": null, \"addressType\": \"WORK\", \"addressLine1\": null, \"addressLine2\": null}, \"presentAddress\": null, \"permanentAddress\": null}, \"rawValues\": {\"profile\": {\"gender\": \"Female\", \"maritalStatus\": \"Divorced\", \"employmentType\": \"Full time\", \"employmentStatus\": \"Active\"}}, \"dependents\": null, \"contactInfo\": {\"phones\": [{\"type\": \"WORK\", \"number\": \"+****************\"}], \"personalEmails\": [\"<EMAIL>\"]}, \"employeeKYC\": null, \"bankAccounts\": $bankAccounts, \"compensation\": null, \"customFields\": null, \"orgStructure\": {\"manager\": null, \"department\": \"Accounting\", \"designation\": \"Account Manager\"}, \"bankAccountDetails\": null, \"employeeProfilePicture\": {\"pictureURL\": \"https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary\", \"pictureName\": null}, \"employeeIdentificationData\": null}, \"eventType\": \"record.modified\", \"syncJobId\": \"sj_sFJC78RTPyB9vvpH0m83hk\", \"syncRunId\": \"sr_oNnWVOz8NRRlN1mGZqx1uy\", \"triggeredAt\": \"*************\", \"syncDataType\": \"employee\"}",
            errors = null,
        )
    )
}

fun getMockKnitEmployeeData(firstName: String, lastName: String, compensationData: knitCompensationData? = null): knitEmployeeData {
    return knitEmployeeData(
        profile = Profile(
            firstName = firstName,
            lastName = lastName,
            id = "3277135280330507201",
            workEmail = "<EMAIL>",
        ),
        compensation = compensationData
    )
}

fun getMockEmployeeData(): EmployeeData {
    return EmployeeData(
        firstName = "Test",
        lastName = "Test",
        id = "Test",
        endDate = LocalDateTime.now(),
        startDate = LocalDateTime.now(),
        maritalStatus = Member.MaritalStatus.SINGLE,
        position = "Test",
        gender = Gender.FEMALE,
        dateOfBirth = LocalDateTime.now(),
        fullName = "Test",
        username = "Test",
        workEmail = "Test",
        employmentActive = true,
        personalEmail = "Test",
        phoneNumber = "Test",
        contactDetails = null
    )
}
fun getMockEventData(
    firstName: String,
    lastName: String,
    status: String,
    terminationDate: String?,
    compensation: Compensation? = null,
): EventData {
    return EventData(
        profile = EmployeeProfile(
            firstName = firstName,
            lastName = lastName,
            id = "3277135280330507201",
            workEmail = "<EMAIL>",
            birthDate = null,
            terminationDate = terminationDate.toDate(),
            startDate = null,
            employmentType = null,
            gender = null,
            employmentStatus = EmploymentStatus.valueOf(status),
            maritalStatus = null,
            employeeNumber = "111"
        ),
        bankAccounts = emptyList(),
        compensation = compensation,
        contactInfo = null,
        customFields = null,
        locations = null,
        integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
        orgStructure = null,
        rawValues = null,
        isRecordMissing = false,
        employeeId = "3277135280330507201",
    )
}

fun getMockContractIntegration(contractId: Long): Pair<JpaCompanyIntegration, JpaPlatformContractIntegration> {
    val platform = JpaPlatform(
        id = 1L,
        isPositionDropdownEnabled = false
    )
    val provider = JpaProvider(
        id = 1L,
        name = ProviderName.KNIT
    )
    val companyIntegration = JpaCompanyIntegration(
        id = 1L,
        companyId = 100L,
        provider = provider,
        accountToken = "Test",
        enabled = true,
        lastIncomingSyncTime = null,
        lastOutgoingSyncTime = null,
        incomingSyncEnabled = true,
        platform = platform,
        lastOutgoingSyncTimeToggleOffTime = null,
        lastOutgoingSyncTimeToggleOnTime = null
    )
    val platformContractIntegration = JpaPlatformContractIntegration(
        id = 1L,
        contractId = contractId,
        platform = platform,
        provider = provider,
        providerId = provider.id!!,
        platformId = platform.id!!,
        platformEmployeeId = "Test",
        remoteId = "remoteId",
        integrationId = companyIntegration.id
    )
    return Pair(companyIntegration, platformContractIntegration)
}

fun getMockContract(
    contractId: Long = 1,
    memberId: Long = 1,
    currency: CurrencyCode? = CurrencyCode.VND,
    status: ContractOuterClass.ContractStatus? = ContractOuterClass.ContractStatus.ACTIVE,
    contractType: ContractType? = ContractType.EMPLOYEE,
    workEmail: String? = ""
): ContractOuterClass.Contract {
    val currentDate = LocalDate.now()
    val startOn = Date.newBuilder()
        .setYear(currentDate.year)
        .setMonth(currentDate.monthValue)
        .setDay(currentDate.dayOfMonth)
        .build()
    return ContractOuterClass.Contract.newBuilder()
        .setId(contractId)
        .setMemberId(memberId)
        .setCompanyId(1L)
        .setStatus(status)
        .setCurrency(currency)
        .setPosition("test")
        .setStartOn(startOn)
        .setType(contractType)
        .setWorkEmail(workEmail)
        .setCountry("Test")
        .build()
}

fun getMockPlatformEmployeeData(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
    variable: String? = null,
): List<JpaPlatformEmployeeData> {
    return listOf(
        JpaPlatformEmployeeData(
            employeeId = "3277135280330507201",
            integrationId = 1L,
            employeeData = "{\"profile\": {\"id\": \"3277135280330507201\", \"gender\": $gender, \"lastName\": $lastName, \"birthDate\": $birthDate, \"firstName\": $firstName, \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": $maritalStatus, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": $terminationDate, \"employmentStatus\": $status}, \"compensation\": {\"fixed\": [{\"type\": \"SALARY\", \"amount\": $compensationAmount, \"planId\": \"SALARY\", \"endDate\": null, \"currency\": \"NZD\", \"frequency\": \"WEEKLY\", \"payPeriod\": \"WEEKLY\", \"startDate\": \"2024-01-10T00:00:00Z\", \"percentage\": null}], \"variable\": $variable}}",
            origin = null
        )
    )
}

fun getMockMemberWithoutEmail(memberId: Long): Member {
    val member = Member.newBuilder()
        .setId(memberId)
        .setFirstName("Britanni Test First Name")
        .setLastName("Buchanan HHH Ahihi")
        .setGender(Gender.FEMALE)
        .setMartialStatus(Member.MaritalStatus.DIVORCED)
        .setStatus(Member.MemberStatus.DELETED)
        .build()
    return member
}
fun getMockMember(memberId: Long): Member {
    return Member.newBuilder()
        .setId(memberId)
        .setFirstName("Britanni Test First Name")
        .setLastName("Buchanan HHH Ahihi")
        .setGender(Gender.FEMALE)
        .setMartialStatus(Member.MaritalStatus.DIVORCED)
        .setStatus(Member.MemberStatus.DELETED)
        .addEmails(
            EmailAddress.newBuilder()
                .setType("primary")
                .setEmail("test")
                .build()
        )
        .addEmails(
            EmailAddress.newBuilder()
                .setType("non-primary")
                .setEmail("test")
                .build()
        )
        .addAddresses(
            Address.newBuilder()
                .setCountry(CountryCode.USA)
                .setPostalCode("99705")
                .setLine1("Test Address")
                .setCity("HN")
                .setState("HN")
                .build()
        )
        .addPhoneNos(
            PhoneNumer.newBuilder()
                .setPhoneNo("1234567")
                .build()
        )
        .build()
}

fun getMockUpdatedReceivedEvents(
    firstName: String,
    lastName: String,
    birthDate: String,
    gender: String,
    maritalStatus: String,
    status: String,
    terminationDate: String,
    compensationAmount: Double? = 9000.0,
): JpaReceivedEvent {
    return JpaReceivedEvent(
        eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
        syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
        integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
        eventType = modelEventType.RECORD_UPDATE,
        confirmedByUser = true,
        processed = true,
        id = 1L,
        receivedTime = LocalDateTime.now(),
        identifiervalue = "3277135280330507201",
        syncDataType = "employer",
        data = "{\"eventId\": \"ev_5t0qc2z7kVRbaJH5UL4aAg\", \"recordId\": \"3277135280330507201\", \"syncType\": \"delta_sync\", \"eventData\": {\"profile\": {\"id\": \"3277135280330507201\", \"gender\": $gender, \"lastName\": $lastName, \"birthDate\": $birthDate, \"firstName\": $firstName, \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": $maritalStatus, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": $terminationDate, \"employmentStatus\": $status}, \"locations\": {\"workAddress\": {\"city\": null, \"state\": null, \"country\": \"US\", \"zipCode\": null, \"addressType\": \"WORK\", \"addressLine1\": null, \"addressLine2\": null}, \"presentAddress\": null, \"permanentAddress\": null}, \"rawValues\": {\"profile\": {\"gender\": \"Female\", \"maritalStatus\": \"Divorced\", \"employmentType\": \"Full time\", \"employmentStatus\": \"Active\"}}, \"dependents\": null, \"contactInfo\": {\"phones\": [{\"type\": \"WORK\", \"number\": \"+****************\"}], \"personalEmails\": [\"<EMAIL>\"]}, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": {\"fixed\": [{\"type\": \"SALARY\", \"amount\": $compensationAmount, \"planId\": \"SALARY\", \"endDate\": null, \"currency\": \"NZD\", \"frequency\": \"WEEKLY\", \"payPeriod\": \"WEEKLY\", \"startDate\": \"2024-01-10T00:00:00Z\", \"percentage\": null}], \"stock\": null, \"variable\": null}, \"customFields\": null, \"orgStructure\": {\"manager\": null, \"department\": \"Accounting\", \"designation\": \"Account Manager\"}, \"bankAccountDetails\": null, \"employeeProfilePicture\": {\"pictureURL\": \"https://media-process.hibob.com/image/upload/b_rgb:589C5C/co_white,l_text:Arial_50_bold:BH,g_center/hibob/default-avatar/transparent_avatar.png?token=****************************************************************************************************************************************************************************************************************&vendor=cloudinary\", \"pictureName\": null}, \"employeeIdentificationData\": null}, \"eventType\": \"record.modified\", \"syncJobId\": \"sj_sFJC78RTPyB9vvpH0m83hk\", \"syncRunId\": \"sr_oNnWVOz8NRRlN1mGZqx1uy\", \"triggeredAt\": \"*************\", \"syncDataType\": \"employee\"}",
        errors = ""
    )
}

fun getMockUpdatedTerminationDatePlatformEmployeeData(): JpaPlatformEmployeeData {
    return JpaPlatformEmployeeData(
        employeeId = "3277135280330507201",
        integrationId = 1L,
        employeeData = "{\"profile\": {\"id\": \"3277135280330507201\", \"gender\": null, \"lastName\": \"Buchanan\", \"birthDate\": null, \"firstName\": \"Britanni\", \"startDate\": \"1979-12-25T00:00:00Z\", \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": \"789\", \"employmentType\": \"FULL_TIME\", \"terminationDate\": \"2024-01-30T00:00:00Z\", \"employmentStatus\": null}",
        origin = null
    )
}

fun getMockContractOffboarding(
    contractId: Long,
    lastWorkingDay: LocalDate,
    status: ContractOffboardingStatus = ContractOffboardingStatus.INITIATED,
): ContractOffboarding {
    return ContractOffboarding.newBuilder()
        .setContractId(contractId)
        .setId(2L)
        .setLastWorkingDay(lastWorkingDay.toGrpcTimestamp())
        .setContractOffboardingType(ContractOffboardingType.RESIGNATION)
        .setContractOffBoardingStatus(status)
        .build()
}

fun getMockGrpcBulkExpenseRequestInput(): GrpcBulkExpenseRequestInput {
    return GrpcBulkExpenseRequestInput.newBuilder().build()
}

fun getEmptyFailedEvents(): List<JpaEventLog> {
    return listOf()
}

fun getEmptyMockReceivedEvents(): List<JpaReceivedEvent> {
    return listOf()
}

fun getFailedEvents(
    eventType: typeEventType,
    contractId: Long? = 1L
): List<JpaEventLog> {
    return listOf(
        JpaEventLog(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            eventType = eventType,
            status = EventStatus.FAILED,
            eventPayload = "{\"contractId\": \"100\"}",
            contractId = contractId
        )
    )
}

fun getEventsWithEmptyPayload(
    eventType: typeEventType,
): List<JpaEventLog> {
    return listOf(
        JpaEventLog(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            eventType = eventType,
            status = EventStatus.FAILED,
            eventPayload = ""
        )
    )
}

fun getEventsWithExperiencePayload(
    eventType: typeEventType,
    eventStatus: EventStatus? = EventStatus.FAILED,
    contractId: Long? = 1L
): List<JpaEventLog> {
    return listOf(
        JpaEventLog(
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            eventType = eventType,
            status = eventStatus!!,
            eventPayload = "event: {experience:\"company\"}",
            contractId = contractId
        )
    )
}

fun getGetOnboardingStatusResponse(): Onboarding.GetOnboardingStatusResponse{
    return Onboarding.GetOnboardingStatusResponse.newBuilder().setStatus(Onboarding.ContractOnboardingStatus.MEMBER_VERIFICATION_COMPLETED).build()
}
fun getContractOnboardingEventMessage(): ContractOnboardingEventMessageOuterClass.ContractOnboardingEventMessage {
    return ContractOnboardingEventMessageOuterClass.ContractOnboardingEventMessage.newBuilder().build();
}

fun createMockCreateExpensesRequestWithReportId(
    contractId: Long,
    externalId: String,
    isApproved: Boolean
): CreateExpensesRequestWithReportId {
    val createExpensesRequest = CreateExpensesRequest.newBuilder()
        .setContractId(contractId)
        .setExternalId(externalId)
        .build()
    return Triple(externalId, createExpensesRequest, isApproved)
}

fun getMockEntityIntegration(
    externalId: String,
    internalId: Long
): JpaEntityIntegration {
    return JpaEntityIntegration(
        id = 1L,
        contractId = 1L,
        entityType = "EXPENSE",
        externalId = externalId,
        internalId = internalId,
        integrationId = "TestIntegrationId",
        paid = false,
        processed = false,
    )
}


fun getMockEntityIntegration(
    entityType: EntityType,
    internalId: Long,
    externalId: String,
    paid: Boolean = false,
    process: Boolean = false,
    id: Long = 1L,
    integrationId: String? = "Test"
): JpaEntityIntegration {
    return JpaEntityIntegration(
        id = id,
        contractId = 1L,
        entityType = entityType.name,
        externalId = externalId,
        internalId = internalId,
        integrationId = integrationId,
        paid = paid,
        processed = process,
    )
}

fun getBasicDetails(): BasicDetails {
    return BasicDetails(
        gender = Gender.FEMALE,
        lastName = "Test",
        firstName = "Test",
        fullLegalName = "Test"
    )
}

fun getContactDetails(): ContactDetails {
    return ContactDetails(
        phoneNumber = "test",
        countryName = "test",
        city = "test",
        state = "test",
        zipCode = "12",
        addressLine1 = "test",
        addressLine2 = "test",
        countryCode = CountryCode.ABW
    )
}

fun getMockCompanyIntegration(platformName: String = "Hibob"): Optional<JpaCompanyIntegration> {
    val provider = JpaProvider(
        id = 1L,
        name = ProviderName.KNIT
    )
    val platform = JpaPlatform(
        id = 1L,
        isPositionDropdownEnabled = false,
        isSpecialEnum = true,
        name = platformName
    )
    val companyIntegration = JpaCompanyIntegration(
        id = 1L,
        companyId = 100L,
        provider = provider,
        accountToken = "Test",
        enabled = true,
        lastIncomingSyncTime = null,
        lastOutgoingSyncTime = null,
        incomingSyncEnabled = true,
        platform = platform,
        lastOutgoingSyncTimeToggleOffTime = null,
        lastOutgoingSyncTimeToggleOnTime = null,
        outgoingSyncEnabled = true
    )
    return Optional.of(companyIntegration)
}

fun getMockJPASync(): Optional<JpaSync> {
    return Optional.of(
        JpaSync(
            syncId = "Test",
            integrationId = "Test",
            clientSyncId = "Test",
            inProgress = false,
            startTime = LocalDateTime.now(),
            endTime = LocalDateTime.now()
        )
    )
}

fun getGrpcCurrencyCode(): Set<Currency.GrpcCurrencyCode> {
    return Currency.GrpcCurrencyCode.entries.toSet()
}

fun getJpaPendingEmployee(): JpaPendingEmployee {
    return JpaPendingEmployee(
        integrationId = "Test",
        syncId = "Test",
        id = 1,
        contractId = 100L,
        pendingImport = true,
        pendingInvite = true,
        lastName = "Test",
        firstName = "Test",
        identifier = "Test",
        inviteRequested = true
    )
}

fun getEventAsJSONNode(eventType: String): JsonNode {
    val mapper = jacksonObjectMapper()
    val fullJsonContent = """
    [{
        "eventId":"Test",
        "eventType":"$eventType",
        "syncType":"delta_sync",
        "syncDataType":"employee",
        "syncJobId":"Test",
        "syncRunId":"Test",
        "triggeredAt":1709265767028,
        "eventData":[{
            "processed":"0",
            "numRestarts":3
         }]
    }]        
    """.trimIndent()

    // Traverse to get the node of interest
    val node: JsonNode = mapper.readTree(fullJsonContent).get(0)
    return node
}

fun getMockExpenseDataReceivedEvents(
    eventType: modelEventType = modelEventType.RECORD_UPDATE,
    status: String = "APPROVED"
): JpaReceivedEvent {
    return JpaReceivedEvent(
        eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
        syncId = "sj_sFJC78RTPyB9vvpH0m83hk",
        integrationId = "b29fY01CTm1ENDBGUm9TQTUweXZrUTUxVTpoaWJvYg==",
        eventType = eventType,
        confirmedByUser = true,
        processed = false,
        id = 1L,
        receivedTime = LocalDateTime.now(),
        identifiervalue = "3277135280330507201",
        syncDataType = "employer",
        data = "{\"eventId\": \"ev_5t0qc2z7kVRbaJH5UL4aAg\", \"recordId\": \"3277135280330507201\", \"syncType\": \"delta_sync\", \"eventData\": {\"details\": {\"rate\": null, \"unit\": null, \"count\": null,\"amount\": 50,\"status\": \"$status\",\"category\": \"Uncategorized\",\"currency\": \"INR\",\"merchant\": \"flight02\",\"reportId\": \"R00DfWoZDDwb\",\"expenseID\": \"6424856025210172449\",\"isBillable\": \"FALSE\",\"reportName\": \"Expense Report 2024-03-11\",\"expenseDate\": \"2024-03-11T08:36:25Z\",\"expenseType\": \"EXPENSE\",\"approvalDate\": \"2024-03-11T08:47:50Z\",\"creationDate\": \"2024-03-11T08:32:46Z\",\"creatorEmail\": \"<EMAIL>\",\"isReimbursable\": \"TRUE\",\"submissionDate\": \"2024-03-11T08:43:57Z\",\"isACHReimbursed\": \"FALSE\",\"reimbursementDate\": null }, \"payment\": null, \"tax\": null, \"policy\": null, \"attendees\": null, \"receipts\": [{\"id\": \"*********\", \"link\": \"https://www.expensify.com/receipts/w_5e8e459df13711506c0c4efede7190a7df122f6f.pdf?encryptedCredentials=hUZlLAT844jk945nQ8UdZWeW1eZTrvpjmnGebPJ6TYIg7cIoSnkpPxcBDjut%2FI4eHUxhWDiYQymosW%2F9gRTOdFVsgnSwutNOEmB9jbX1DuIKjpegN3XCHquyULTjm%2Bl%2Blv1LcB8R%2BruC78N331BsoHmSPNgk45nkN3X42zGb9ah13qT%2FZ6shabp8UJM%3D\", \"fileName\": \"w_5e8e459df13711506c0c4efede7190a7df122f6f.pdf\", \"fileType\": \"pdf\"}]}, \"syncJobId\": \"sj_sFJC78RTPyB9vvpH0m83hk\", \"syncRunId\": \"sr_oNnWVOz8NRRlN1mGZqx1uy\", \"triggeredAt\": \"*************\", \"syncDataType\": \"expense\", \"eventType\": \"record.modified\"}",
        errors = "",
    )
}

fun getMockExpenseData(status: ExpenseStatus? = ExpenseStatus.APPROVED, expenseId: String? = null): ExpenseData {
    return ExpenseData(
        details = ExpenseDetails(
            amount = 50.0,
            creatorEmail = "<EMAIL>",
            approvalDate = null,
            status = status,
            category = null,
            count = null,
            creationDate = null,
            currency = "INR",
            expenseDate = null,
            expenseID = expenseId,
            expenseType = null,
            isACHReimbursed = null,
            isBillable = null,
            isReimbursable = null,
            merchant = null,
            rate = null,
            receipts = null,
            reportId = "report#123",
            reportName = null,
            reimbursementDate = null,
            submissionDate = null,
            unit = null,
            description = null
        ),
        attendees = null,
        payment = null,
        policy = null,
        receipts = null,
        tax = null
    )
}

fun createMockCreateExpensesRequestWithReportId(expenseId: Long, contractId: Long, externalId: String?): CreateExpensesRequestWithReportId {
    val createExpensesRequest = CreateExpensesRequest.newBuilder()
        .setExpenseId(expenseId)
        .setContractId(contractId)
        .setExternalId(externalId ?: "123")
        .build()
    val approved = true
    return Triple(externalId, createExpensesRequest, approved)
}

fun getDocumentResponse(): DocumentResponse{
    return DocumentResponse()
}

fun getBankData(): BankData{
    return BankData(
        bankName = "Test",
        accountNumber = "1234",
        accountType = "Test"
    )
}


fun getCreateDocumentResponse(status: Boolean): CreateDocumentResponse {
    return CreateDocumentResponse(success = status)
}

fun getDocumentCategoriesResponse(status: Boolean, name: String? = null): GetDocumentCategoriesResponse {
    return GetDocumentCategoriesResponse(
        success = status,
        categories = listOf(Category(
            id = "1",
            name = name
        ))
    )
}

fun getDocumentCategoriesResponse(status: Boolean, documentCategory: DocumentCategory): DocumentCategoriesResponse {
    return DocumentCategoriesResponse(
        success = status,
        data = DocumentCategoriesData(
            categories = listOf(documentCategory)
        )
    )
}
fun getBenefitDocumentResponse(): ContractBenefitDocumentsResponse{
    return ContractBenefitDocumentsResponse.newBuilder()
        .setFactSheetId("1")
        .setOnboardingKitId("1")
        .build()
}

fun getPositionDetail(status: Boolean, position: Position?): GetPositionDetailResponse{
    var positions:List<Position>? = null
    if(position!=null)
        positions = listOf(position)
    return GetPositionDetailResponse(
        success = status,
        data = Data(
            positions = positions,
            workShifts = listOf()
        )
    )
}

fun getEmploeeRecord(status: Boolean): CreateEmployeeResponse {
    return CreateEmployeeResponse(
        success = status,
        data = com.multiplier.integration.adapter.api.resources.knit.EmployeeData(
            employeeId = "1"
        )
    )
}

fun getWorkLocations(status: Boolean, workList: List<ListData>?): GetWorkLocationsResponse{

    return GetWorkLocationsResponse(
        success = status,
        data = WorkLocationResponse(
            parsedData = Lists(
                list = workList
            )
        )
    )
}

fun getWorksitesResponse(status: Boolean, workSite: WorkSite?): GetWorksitesResponse {
    var workSites: List<WorkSite>? = null
    if (workSite!=null){
        workSites = listOf(workSite)
    }
    return GetWorksitesResponse(
        success = status,
        data = WorkSiteResponse(
            responseJson = WorkSiteResponseBodySerialized(
                body = WorkSiteData(
                    name = "Test",
                    values = workSites
                )
            )
        )
    )
}

fun getUpdateEmployeeDetailsResponse(status: Boolean, msg:String): UpdateEmployeeDetailsResponse {
    return UpdateEmployeeDetailsResponse(
        success = status,
        error = ErrorResponse(
            msg = msg
        )
    )
}

fun getCompensationData(): CompensationData {
    return CompensationData(
        amount = 1.0,
        currency = CurrencyCode.CUC,
        frequency = null
    )
}

fun getCompensation(): CompensationOuterClass.Compensation{
    val basePay: CompensationOuterClass.CompensationPayComponent = CompensationOuterClass.CompensationPayComponent.newBuilder().build()
    return CompensationOuterClass.Compensation.newBuilder()
        .setBasePay(basePay)
        .addAdditionalPays(CompensationOuterClass.CompensationPayComponent.newBuilder().setAmount(0.0)
            .build())
        .build()
}

fun getCompensationHappy(): CompensationOuterClass.Compensation{
    val basePay: CompensationOuterClass.CompensationPayComponent = CompensationOuterClass.CompensationPayComponent.newBuilder().build()
    return CompensationOuterClass.Compensation.newBuilder()
        .setBasePay(basePay)
        .addAdditionalPays(CompensationOuterClass.CompensationPayComponent.newBuilder().setAmount(1000.0)
            .build())
        .build()
}

fun getUpdateCompensationResponse(status: Boolean): UpdateCompensationResponse {
    return UpdateCompensationResponse(success = status)
}

fun getCompensationPlanResponse(): GetCompensationPlanResponse {
    return GetCompensationPlanResponse(success = true)
}

fun getCompensationPlanHappyResponse(): GetCompensationPlanResponse {
    return GetCompensationPlanResponse(
        success = true,
        data = CompensationPlanData(
            fixed = listOf(CompensationPlanDetail()),
            stock = listOf(CompensationPlanDetail()),
            variable = listOf(CompensationPlanDetail(type = "BONUS", amount = 10000.0), CompensationPlanDetail(type = "BONUS2", amount = 0.0))
        )
    )
}

fun getUpdateEmployeeResponse():UpdateEmployeeResponse{
    return UpdateEmployeeResponse(
        success = true
    )
}
fun getRemoteResponse(): RemoteResponse {
    return RemoteResponse(
        method = "Test",
        path = "Test",
        status = 1,
        response = "Test"
    )
}

fun getTerminationReasonResponse(status: Boolean,reason:String): GetTerminationReasonResponse {
    return GetTerminationReasonResponse(
        success = status,
        data = TerminationReasonResponse(
            reasons = listOf(
                TerminationReason(reason,"Test")
            )
        )
    )
}

fun getSAPWorkLocationsResponse(status: Boolean, workLocationData: List<WorkLocationData>): GetSAPWorkLocationsResponse{
    return GetSAPWorkLocationsResponse(
        success = status,
        data = SAPWorkLocationResponse(
            response = SAPWorkLocationResponseBody(
                body = SAPWorkLocationNestedResult(
                    d = SAPWorkLocationResponseData(
                        workLocationData
                    )
                )
            )
        )
    )
}

fun getSAPBusinessUnitsResponse(status: Boolean, businessUnitData: List<BusinessUnitData> ): GetSAPBusinessUnitsResponse{
    return GetSAPBusinessUnitsResponse(
        success = status,
        data = SAPBusinessUnitsResponse(
            response = SAPBusinessUnitsResponseBody(
                body = SAPBusinessUnitsNestedResult(
                    d = SAPBusinessUnitsResponseData(
                        businessUnitData
                    )
                )
            )
        )
    )
}
fun getTerminateEmployeeResponse(status: Boolean): TerminateEmployeeResponse {
    return TerminateEmployeeResponse(
        success = status
    )
}
// Helper function to create a mock response from ExpenseServiceAdapter
fun createMockBulkCreateExpensesResponse(expenseIds: List<Long>): BulkCreateExpensesResponse {
    return BulkCreateExpensesResponse.newBuilder()
        .addAllExpenseIds(expenseIds)
        .build()
}

fun getMockPlatformEmployeeDataWithTriNetData(employeeId: String? = "Test"): List<JpaPlatformEmployeeData> {
    return listOf(
        JpaPlatformEmployeeData(
            employeeId = employeeId!!,
            integrationId = 1L,
            employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
            origin = null
        )
    )
}



fun getMockDocumentFolder(integration: JpaCompanyIntegration): JpaDocumentFolder {
    return JpaDocumentFolder(
        companyIntegration = integration,
        folderId = "shared",
        folderLabel = "Shared",
        folderType = DocumentFolderType.PAYSLIP
    )
}

fun getMockLegalEntity(entityId: Long, companyId: Long): CompanyOuterClass.GetLegalEntitiesResponse =
    CompanyOuterClass.GetLegalEntitiesResponse.newBuilder()
        .addAllEntities(
            listOf(
                CompanyOuterClass.LegalEntity.newBuilder()
                    .setId(entityId)
                    .setCompanyId(companyId)
                    .setAddress(
                        CompanyOuterClass.Address.newBuilder()
                            .setCountry("USA")
                            .build()
                    )
                    .setCurrencyCode("USD")
                    .setStatus(CompanyOuterClass.LegalEntity.LegalEntityStatus.ACTIVE)
                    .build()
            )
        )
        .build()


fun getMockKnitFields() = GetAllFieldsResponse(
    success = true,
    data = FieldDataList(
        default = listOf(
            FieldData(
                fieldId = "firstName",
                fieldFromApp = "firstName",
                mappedKey = "firstName",
                label = "First Name"
            ),
            FieldData(
                fieldId = "compensationType",
                fieldFromApp = "compensationType",
                mappedKey = "compensation.variable[].type",
                label = "Compensation Type"
            ),
            FieldData(
                fieldId = "compensationPlanId",
                fieldFromApp = "compensationPlanId",
                mappedKey = "compensation.variable[].planId",
                label = "Compensation Plan Id"
            ),
            FieldData(
                fieldId = "compensationAmount",
                fieldFromApp = "compensationAmount",
                mappedKey = "compensation.variable[].amount",
                label = "Amount"
            )
        )
    )
)

fun getEmployeeDirectoryResponse(status: Boolean, employeeId: String): GetEmployeeDirectoryResponse {
    return GetEmployeeDirectoryResponse(
        success = status,
        employees = listOf(Employee(
            id = employeeId
        ))
    )
}

fun getMemberDetailUpdateEvent(
    eventId: String,
    contractId: Long? = 1L,
    memberId: Long? = 1L,
    status: MemberStatus? = MemberStatus.ACTIVE
): JpaEventLog {
    return JpaEventLog(
            eventId = eventId,
            eventType = EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
            status = EventStatus.TO_BE_PROCESSED,
            eventPayload = "event {\n" +
                    "  member_id: ${memberId}\n" +
                    "  contract_id: ${contractId}\n" +
                    "  status: ${status}\n" +
                    "}\n",
            contractId = contractId
        )
}

fun getContractWorkEmailUpdateEvent(
    eventId: String,
    contractId: Long? = 1L,
    status: ContractStatus? = ContractStatus.ACTIVE
): JpaEventLog {
    return JpaEventLog(
        eventId = eventId,
        eventType = EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
        status = EventStatus.TO_BE_PROCESSED,
        eventPayload = "event_type: CONTRACT_WORK_EMAIL_CHANGED\n" +
                        "event {\n" +
                        "  contract_id: ${contractId}\n" +
                        "  contract_status: ${status}\n" +
                        "}\n",
        contractId = contractId
    )
}

fun getEventsWithPayablePayload(): List<JpaEventLog>{
    return listOf(
        JpaEventLog(
            id = 1,
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            eventType = EventType.INCOMING_PAYABLE_UPDATE,
            status = EventStatus.FAILED,
            eventPayload = "event_id: \"evt123\"\n" +
                    "company_id: 456\n" +
                    "timestamp {\n" +
                    "  seconds: 1672531200\n" +
                    "}\n" +
                    "company_payable_id: 789\n" +
                    "event_type: STATUS_UPDATED\n" +
                    "status: AUTHORIZED",
            contractId = -1
        )
    )
}

fun getEventsWithPayablePayloadForAmountUpdated(): List<JpaEventLog>{
    return listOf(
        JpaEventLog(
            id = 1,
            eventId = "ev_5t0qc2z7kVRbaJH5UL4aAg",
            eventType = EventType.INCOMING_PAYABLE_UPDATE,
            status = EventStatus.FAILED,
            eventPayload = "event_id: \"evt123\"\n" +
                    "company_id: 456\n" +
                    "timestamp {\n" +
                    "  seconds: 1672531200\n" +
                    "}\n" +
                    "company_payable_id: 789\n" +
                    "event_type: DUE_AMOUNT_UPDATED\n" +
                    "status: AUTHORIZED",
            contractId = -1
        )
    )
}

val lwd = LocalDate.of(2024, 1, 30)
val today = LocalDate.now()
