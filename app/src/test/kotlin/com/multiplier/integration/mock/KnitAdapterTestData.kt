package com.multiplier.integration.mock

import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.EmployeeData
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress

fun createEmployeeRequest(
    employment: Employment? = null,
    workAddress: WorkAddress? = null,
    firstName: String? = null,
    lastName: String? = null,
    workEmail: String? = null,
    personalEmails: List<String>? = null,
    metadata: Map<String, String>? = null
): CreateEmployeeRequest {
    return CreateEmployeeRequest(
        employment = employment,
        workAddress = workAddress,
        firstName = firstName,
        lastName = lastName,
        workEmail = workEmail,
        personalEmails = personalEmails,
        metadata = metadata
    )
}

fun createEmployment(
    positionId: String? = null,
    designation: String? = null,
    workShiftId: String? = null
): Employment {
    return Employment(
        positionId = positionId,
        designation = designation,
        workShiftId = workShiftId
    )
}

fun createWorkAddress(
    id: String? = null,
    addressLine1: String? = null,
    addressLine2: String? = null,
    city: String? = null,
    state: String? = null,
    country: String? = null,
    zipCode: String? = null
): WorkAddress {
    return WorkAddress(
        id = id,
        addressLine1 = addressLine1,
        addressLine2 = addressLine2,
        city = city,
        state = state,
        country = country,
        zipCode = zipCode
    )
}

fun createEmployeeResponse(
    success: Boolean? = null,
    data: EmployeeData? = null,
    errors: List<String>? = null,
    responseCode: Int? = null
): CreateEmployeeResponse {
    return CreateEmployeeResponse(
        success = success,
        data = data,
        errors = errors,
        responseCode = responseCode
    )
}

fun createEmployeeData(
    employeeId: String? = null
): EmployeeData {
    return EmployeeData(employeeId = employeeId)
}


