package com.multiplier.integration.adapter.api.resources.knit.bamboo

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class GetEmployeeDirectoryResponseTest {

    @Test
    fun `should find an employee by email`() {
        val response = GetEmployeeDirectoryResponse(
            success = true,
            employees = listOf(
                Employee(id = "1", workEmail = "<EMAIL>"),
                Employee(id = "2", workEmail = "<EMAIL>")
            )
        )

        response.findByEmail("<EMAIL>")?.id shouldBe "1"
    }

    @Test
    fun `should ignore case`() {
        val response = GetEmployeeDirectoryResponse(
            success = true,
            employees = listOf(
                Employee(id = "1", workEmail = "<EMAIL>")
            )
        )

        response.findByEmail("<EMAIL>")?.id shouldBe "1"
    }

    @Test
    fun `should return null if no match`() {
        val response = GetEmployeeDirectoryResponse(
            success = true,
            employees = listOf(
                Employee(id = "1", workEmail = "<EMAIL>")
            )
        )

        response.findByEmail("<EMAIL>") shouldBe null
    }
}