package com.multiplier.integration.adapter.api

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpecsResponse
import com.multiplier.contract.onboarding.schema.BulkOnboardInput
import com.multiplier.contract.onboarding.schema.BulkOnboardOption
import com.multiplier.contract.onboarding.schema.BulkOnboardRequest
import com.multiplier.contract.onboarding.schema.BulkOnboardValidationResponse
import com.multiplier.contract.onboarding.schema.BulkOnboardValidationResult
import com.multiplier.contract.onboarding.schema.ContractOnboardingServiceGrpc.ContractOnboardingServiceBlockingStub
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirement
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class ContractOnboardingServiceAdapterTest {
    @MockK
    lateinit var stub: ContractOnboardingServiceBlockingStub

    @InjectMockKs
    lateinit var contractOnboardingServiceClient: ContractOnboardingServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should validate bulk onboarding employee successfully`() {
        val companyId = 1L
        val entityId = 2L

        val option = BulkOnboardOption.newBuilder()
            .setCompanyId(companyId)
            .setEntityId(entityId)
            .setCountryCode(Country.CountryCode.COUNTRY_CODE_VNM)
            .setContractType(Contract.ContractType.CONTRACT_TYPE_HR_MEMBER)
            .setContext(OnboardingType.GLOBAL_PAYROLL.name)
            .build()
        val inputs = BulkOnboardInput.newBuilder()
            .setRequestId(0)
            .putAllProperties(mapOf("firstName" to "Test"))
        val mockValidateBulkOnboardReq = BulkOnboardRequest.newBuilder()
            .setOption(option)
            .addInputs(inputs)
            .build()
        val mockValidateBulkOnboardResp = BulkOnboardValidationResponse.newBuilder()
            .addValidationResults(BulkOnboardValidationResult.newBuilder()
                .setRequestId(0)
                .setSuccess(true)
                .build())
            .build()

        every { stub.validateBulkOnboarding(mockValidateBulkOnboardReq) } returns mockValidateBulkOnboardResp

        val request = BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = entityId,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.COUNTRY_CODE_VNM,
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = mapOf(
                "firstName" to "Test"
            ))
        )
        val result = contractOnboardingServiceClient.validateBulkOnboarding(request)

        Assertions.assertEquals(0, result.size)
    }

    @Test
    fun `should get onboarding data specs successfully`() {
        val companyId = 1L
        val entityId = 2L
        val option = BulkOnboardOption.newBuilder()
            .setCompanyId(companyId)
            .setEntityId(entityId)
            .setCountryCode(Country.CountryCode.COUNTRY_CODE_VNM)
            .setContractType(Contract.ContractType.CONTRACT_TYPE_HR_MEMBER)
            .setContext(OnboardingType.GLOBAL_PAYROLL.name)
            .build()
        val mockBulkOnboardResp = BulkOnboardDataSpecsResponse.newBuilder()
            .addAllSpecs(listOf(
                BulkOnboardDataSpec.newBuilder()
                    .setKey("firstName")
                    .setLabel("First Name")
                    .build()
            ))
            .build()

        every { stub.getBulkOnboardDataSpecs(option) } returns mockBulkOnboardResp

        val request = BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = entityId,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.COUNTRY_CODE_VNM,
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(emptyMap())
        )
        val result = contractOnboardingServiceClient.getBulkOnboardDataSpecs(request)

        Assertions.assertEquals("firstName", result.first().key)
    }

    @Test
    fun `should get field requirements successfully`() {
        val companyId = 1L
        val useCase = "USE_CASE_EXAMPLE"
        val mockFieldRequirementsRequest = FieldRequirementsRequest.newBuilder()
            .setCompanyId(companyId)
            .setUseCase(useCase)
            .build()
        val mockFieldRequirementsResponse = FieldRequirementsResponse.newBuilder()
            .addFieldRequirements(
                FieldRequirement.newBuilder()
                .setKey("fieldKey")
                .setMandatory(true)
                .setLabel("Field Label")
                .setGroup("Field Group")
                .build())
            .build()

        every { stub.getFieldRequirements(mockFieldRequirementsRequest) } returns mockFieldRequirementsResponse

        val request = FieldRequirementsRequest.newBuilder()
            .setCompanyId(companyId)
            .setUseCase(useCase)
            .build()
        val result = contractOnboardingServiceClient.getFieldRequirements(request)

        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals("fieldKey", result.first().key)
        Assertions.assertTrue(result.first().required)
        Assertions.assertEquals("Field Label", result.first().label)
        Assertions.assertEquals("Field Group", result.first().group)
    }

    @Test
    fun `should return empty list when no field requirements are found`() {
        val companyId = 1L
        val useCase = "USE_CASE_EXAMPLE"
        val mockFieldRequirementsRequest = FieldRequirementsRequest.newBuilder()
            .setCompanyId(companyId)
            .setUseCase(useCase)
            .build()
        val mockFieldRequirementsResponse = FieldRequirementsResponse.newBuilder().build()

        every { stub.getFieldRequirements(mockFieldRequirementsRequest) } returns mockFieldRequirementsResponse

        val request = FieldRequirementsRequest.newBuilder()
            .setCompanyId(companyId)
            .setUseCase(useCase)
            .build()
        val result = contractOnboardingServiceClient.getFieldRequirements(request)

        Assertions.assertTrue(result.isEmpty())
    }
}

