package com.multiplier.integration.adapter.api

import com.multiplier.integration.TestHttpClientConfig
import com.multiplier.integration.adapter.api.resources.knit.AddCustomFieldMappingRequest
import com.multiplier.integration.adapter.api.resources.knit.CompensationDetail
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequest
import com.multiplier.integration.adapter.api.resources.knit.StockDetail
import com.multiplier.integration.adapter.api.resources.knit.TerminateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooTimeOffRequest
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.mock.createEmployeeData
import com.multiplier.integration.mock.createEmployeeRequest
import com.multiplier.integration.mock.createEmployeeResponse
import com.multiplier.integration.mock.createEmployment
import com.multiplier.integration.mock.createWorkAddress
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.model.JpaSync
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Gender
import io.ktor.client.*
import io.ktor.http.*
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.util.ReflectionTestUtils
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@ExtendWith(SpringExtension::class)
class KnitAdapterTest {
    @Nested
    inner class KnitIntegrationTestSuccessCreate201 {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.success201KnitHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test createEmployee successfully creates an employee`() = runBlocking {
            val request = createEmployeeRequest(
                employment = createEmployment(positionId = "12345", designation = "Software Engineer"),
                workAddress = createWorkAddress(city = "New York", state = "NY", country = "USA"),
                firstName = "John",
                lastName = "Doe",
                workEmail = "<EMAIL>"
            )

            val expectedResponse = createEmployeeResponse(
                success = true,
                data = createEmployeeData(employeeId = "emp123456"),
                errors = null,
                responseCode = 201
            )

            val actualResponse = knitAdapter.createEmployeeRecord(1, 1, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `test create document successfully`() = runBlocking {
            val documentData = CreateDocumentRequest(
                employeeId = "emp001",
                fileName = "resume.pdf",
                fileUrl = "http://example.com/resume.pdf",
                contentType = "application/pdf",
                category = "Resume",
                fileContent = "PDF_CONTENT",
                comment = "Initial submission"
            )

            val actualResponse = knitAdapter.createDocument(1, 1, documentData)
            assertTrue(actualResponse.success)
            assertEquals(201, actualResponse.responseCode)
            assertNotNull(actualResponse.data)
            assertEquals("doc123", actualResponse.data?.documentId)
        }
    }

    @Nested
    inner class KnitIntegrationTestSuccess {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.successKnitHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test createEmployee successfully creates an employee`() = runBlocking {
            val request = createEmployeeRequest(
                employment = createEmployment(positionId = "12345", designation = "Software Engineer"),
                workAddress = createWorkAddress(city = "New York", state = "NY", country = "USA"),
                firstName = "John",
                lastName = "Doe",
                workEmail = "<EMAIL>"
            )

            val expectedResponse = createEmployeeResponse(
                success = true,
                data = createEmployeeData(employeeId = "emp123456"),
                errors = null,
                responseCode = 200
            )

            val actualResponse = knitAdapter.createEmployeeRecord(1, 1, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `test fetch position details successfully`() = runBlocking {
            val actualResponse = knitAdapter.getPositionsDetails(1, 1)
            assertNotNull(actualResponse)
            assertTrue(actualResponse?.success ?: false)
            assertEquals(200, actualResponse?.responseCode)
            assertNotNull(actualResponse?.data)
            assertEquals("Software Engineer", actualResponse?.data?.positions?.first()?.designation)
            assertEquals("Morning Shift", actualResponse?.data?.workShifts?.first()?.workShiftName)
        }

        @Test
        fun `test fetch compensation plans successfully`() = runBlocking {
            val actualResponse = knitAdapter.getCompensationPlan(1, 1)
            assertTrue(actualResponse.success)
            assertEquals(200, actualResponse.responseCode)
            assertNotNull(actualResponse.data)
            assertNotNull(actualResponse.data?.fixed)
            assertEquals("Basic Salary", actualResponse.data?.fixed?.first()?.planName)
            assertEquals("Performance Bonus", actualResponse.data?.variable?.first()?.planName)
            assertEquals("Stock Options", actualResponse.data?.stock?.first()?.planName)
        }

        @Test
        fun `test create document successfully`() = runBlocking {
            val documentData = CreateDocumentRequest(
                employeeId = "emp001",
                fileName = "resume.pdf",
                fileUrl = "http://example.com/resume.pdf",
                contentType = "application/pdf",
                category = "Resume",
                fileContent = "PDF_CONTENT",
                comment = "Initial submission"
            )

            val actualResponse = knitAdapter.createDocument(1, 1, documentData)
            assertTrue(actualResponse.success)
            assertEquals(200, actualResponse.responseCode)
            assertNotNull(actualResponse.data)
            assertEquals("doc123", actualResponse.data?.documentId)
        }

        @Test
        fun `test update compensation successfully`() = runBlocking {
            val compensationData = UpdateCompensationRequest(
                employeeId = "emp123",
                effectiveDate = "2023-01-01",
                fixed = listOf(CompensationDetail(type = "Salary", amount = BigDecimal(5000), currency = "USD")),
                variable = listOf(CompensationDetail(type = "Bonus", percentage = 10)),
                stocks = listOf(StockDetail(planId = "stock001", targetShares = 100))
            )

            val actualResponse = knitAdapter.updateCompensation(1, 1, compensationData)
            assertTrue(actualResponse.success ?: false)
            assertEquals(200, actualResponse.responseCode)
            assertNotNull(actualResponse.data)
            assertEquals("comp123", actualResponse.data?.compensationId)
        }

        @Test
        fun `test update employee details successfully`() = runBlocking {
            val employeeData = UpdateEmployeeDetailsRequest(
                employeeId = "emp123",
                firstName = "John",
                lastName = "Doe",
                workEmail = "<EMAIL>",
                personalEmails = listOf("<EMAIL>")
            )



            val actualResponse = knitAdapter.updateEmployeeDetails(1, 1, 1, employeeData)
            assertTrue(actualResponse.success ?: false)
            assertEquals(200, actualResponse.responseCode)
        }

        @Test
        fun `test fetch document categories successfully`() = runBlocking {
            val actualResponse = knitAdapter.getDocCategories(1, 1)
            assertTrue(actualResponse.success)
            assertNotNull(actualResponse.data)
            assertEquals(2, actualResponse.data?.categories?.size)
            assertEquals(200, actualResponse.responseCode)
        }

        @Test
        fun `test fetch leave balance successfully`() = runBlocking {
            val actualResponse = knitAdapter.getEmployeeLeaveBalance("integrationId", "emp123")
            assertTrue(actualResponse.success ?: false)
            assertNotNull(actualResponse.data)
            assertEquals(200, actualResponse.responseCode)
            assertEquals(1, actualResponse.data?.size)
        }

        @Test
        fun `test terminate employee successfully`() = runBlocking {
            val request = TerminateEmployeeRequest(employeeId = "123", terminationDate = "2024-10-10", terminationReason = "Retirement")
            val actualResponse = knitAdapter.terminateEmployee(1, 1, request)
            assertTrue(actualResponse.success)
            assertNotNull(actualResponse.data)
            assertEquals(200, actualResponse.responseCode)
        }

        @Test
        fun `test fetch termination reasons successfully`() = runBlocking {
            val actualResponse = knitAdapter.getTerminationReason(1, 1)
            assertTrue(actualResponse.success)
            assertNotNull(actualResponse.data)
            assertEquals(200, actualResponse.responseCode)
            assertEquals(1, actualResponse.data?.reasons?.size)
        }

        @Test
        fun `test successful employee update`() = runBlocking {
            val basicDetails = BasicDetails(firstName = "John", lastName = "Doe", fullLegalName = "", gender = Gender.MALE)
            val response = knitAdapter.updateEmployeeRecord(1L, 1L, "ext123", basicDetails)
            assertTrue(response.success, "Employee update should be successful")
            assertNull(response.errorMessage, "No error message expected on success")
        }

        @Test
        fun `update employee contact details with success`() = runBlocking {
            val response = knitAdapter.updateEmployeeContactDetails(1L, 1L, "ext-123", ContactDetails("000-000-0000", "123 Main St", null, "Metropolis", "NY", "12345", CountryCode.USA, "USA"))
            assertTrue(response.success)
            assertNull(response.errorMessage)
        }

        @Test
        fun `getAllFields successfully`() {
            val companyId = 100L
            val platformId = 1L
            val platformName = "hibob"
            val response = runBlocking {
                knitAdapter.getAllFields(companyId, platformId, platformName)
            }

            assertEquals(true, response.success)
            assertEquals(1, response.data?.default?.size)
        }

        @Test
        fun `getFieldValues successfully`() {
            val companyId = 100L
            val platformId = 1L
            val platformName = "hibob"
            val fieldType = "employmentType"
            val response = runBlocking {
                knitAdapter.getFieldValues(companyId, platformId, platformName, fieldType)
            }
            assertEquals(true, response.success)
            assertEquals(5, response.data?.fields?.size)
        }

        @Test
        fun `addCustomFieldMapping successfully`() {
            val companyId = 100L
            val platformId = 1L
            val mockAddFieldRequest = AddCustomFieldMappingRequest(
                fieldFromApp = "employeeId",
                fieldId = "employeeId",
                mappedKey = "employeeId",
                dataType = "STRING"
            )
            val response = runBlocking {
                knitAdapter.addCustomFieldMapping(companyId, platformId, mockAddFieldRequest)
            }
            assertEquals(true, response.success)
        }

        @Test
        fun `test fetch leave types success`() = runBlocking {
            val actualResponse = knitAdapter.getLeaveTypes(1L, 1L)
            assertNotNull(actualResponse)
            assertTrue(actualResponse.success!!)
        }
        @Test
        fun `createLeaveRequest create leave successfully`() = runBlocking {
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
            val leaveRequest = LeaveCreateRequest(
                employeeId = "emp001",
                leaveTypeId = "leaveType001",
                from = LocalDateTime.now().format(formatter),
                to = LocalDateTime.now().plusDays(5).format(formatter),
                unit = com.multiplier.integration.sync.model.Unit.DAYS,
                amount = 5.0,
            )
            val actualResponse = knitAdapter.createLeaveRequest(1L, 1L,"emp001",leaveRequest)
            assertNotNull(actualResponse)
            assertTrue(actualResponse.success)
        }

        @Test
        fun `test fetch departments list successfully`() = runBlocking {
            val actualResponse = knitAdapter.getDepartmentsList(1L, 1L, "paychex")
            assertNotNull(actualResponse)
            assertTrue(actualResponse.success)
            assertEquals(200, actualResponse.responseCode)
            assertNotNull(actualResponse.data)
            assertNotNull(actualResponse.data?.departments)
            assertEquals(2, actualResponse.data?.departments?.size)
            assertEquals("dept1", actualResponse.data?.departments?.get(0)?.id)
            assertEquals("Engineering", actualResponse.data?.departments?.get(0)?.name)
        }
    }

    @Nested
    inner class KnitIntegrationTestException {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.exceptionKnitHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
        }

        @Test
        fun `test handle runtime exception error on creating employee`(): Unit = runBlocking {
            // Create a request with specific details
            val request = createEmployeeRequest(
                firstName = "Jane",
                lastName = "Smith",
                workEmail = "<EMAIL>"
            )

            val expectedResponse = CreateEmployeeResponse(
                success = false,
                errors = listOf("Simulated server error"),
                responseCode = null
            )

            val actualResponse = knitAdapter.createEmployeeRecord(1, 1, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `test handle exception fetch position details`() = runBlocking {
            val actualResponse = knitAdapter.getPositionsDetails(1, 1)
            assertNotNull(actualResponse)
            assertFalse(actualResponse?.success ?: false)
            assertEquals(null, actualResponse?.responseCode)
        }

        @Test
        fun `test handle runtime exception during fetching compensation plans`() = runBlocking {
            val actualResponse = knitAdapter.getCompensationPlan(1, 1)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.responseCode)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
        }

        @Test
        fun `test handle runtime exception during document creation`() = runBlocking {
            val documentData = CreateDocumentRequest(
                employeeId = "emp003",
                fileName = "photo.png"
            )

            val actualResponse = knitAdapter.createDocument(1, 1, documentData)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.responseCode)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
        }

        @Test
        fun `test handle runtime exception during updating compensation`() = runBlocking {
            val compensationData = UpdateCompensationRequest(employeeId = "emp125")

            val actualResponse = knitAdapter.updateCompensation(1, 1, compensationData)
            assertFalse(actualResponse.success ?: true)
            assertNull(actualResponse.responseCode)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
        }

        @Test
        fun `test handle runtime exception during updating employee details`() = runBlocking {
            val employeeData = UpdateEmployeeDetailsRequest(employeeId = "emp125")

            val actualResponse = knitAdapter.updateEmployeeDetails(1, 1, 1, employeeData)
            assertFalse(actualResponse.success ?: true)
            assertNull(actualResponse.responseCode)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
        }

        @Test
        fun `test handle runtime exception during fetching document categories`() = runBlocking {
            val actualResponse = knitAdapter.getDocCategories(1, 1)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle runtime exception during fetching leave balance`() = runBlocking {
            val actualResponse = knitAdapter.getEmployeeLeaveBalance("integrationId", "emp125")
            assertFalse(actualResponse.success ?: true)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle runtime exception during terminating employee`() = runBlocking {
            val request = TerminateEmployeeRequest(employeeId = "125", terminationDate = "2024-10-12", terminationReason = "Downsizing")
            val actualResponse = knitAdapter.terminateEmployee(1, 1, request)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle runtime exception during fetching termination reasons`() = runBlocking {
            val actualResponse = knitAdapter.getTerminationReason(1, 1)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle exception on fetching work sites`() = runBlocking {
            val actualResponse = knitAdapter.getWorksitesResponse(1, 1)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle exception on fetching bamboo work locations`() = runBlocking {
            val actualResponse = knitAdapter.getBambooWorkLocations(1, 1)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle exception on fetching FO locations`() = runBlocking {
            val actualResponse = knitAdapter.getSuccessFactorsWorkLocations(1, 1)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle exception on fetching FOBusinessUnit`() = runBlocking {
            val actualResponse = knitAdapter.getSuccessFactorsBusinessUnits(1, 1)
            assertNull(actualResponse.data)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle exception on fetching document categories passthrough`() = runBlocking {
            val actualResponse = knitAdapter.getDocumentCategories(1, 1, "1")
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
            assertNull(actualResponse.responseCode)
        }

        @Test
        fun `test handle exception on sync start`() = runBlocking {
            every { syncRepository.findByIntegrationId(any()) } returns listOf()
            val actualResponse = knitAdapter.startSync("integrationId123", PlatformCategory.HRIS)
            assertNull(actualResponse)
        }

        @Test
        fun `test exception handling during employee update`() = runBlocking {
            val basicDetails = BasicDetails(firstName = "Exception", lastName = "Case", fullLegalName = "", gender = Gender.MALE)
            val response = knitAdapter.updateEmployeeRecord(1L, 1L, "invalid_id", basicDetails)
            assertFalse(response.success, "Employee update should handle exceptions")
            assertNotNull(response.errorMessage, "Error message expected when an exception occurs")
        }

        @Test
        fun `getAllFields exception`() {
            val companyId = 100L
            val platformId = 1L
            val platformName = "hibob"
            val response = runBlocking {
                knitAdapter.getAllFields(companyId, platformId, platformName)
            }
            assertEquals(false, response.success)
        }

        @Test
        fun `getFieldValues exception`() {
            val companyId = 100L
            val platformId = 1L
            val platformName = "hibob"
            val fieldType = "employmentType"
            val response = runBlocking {
                knitAdapter.getFieldValues(companyId, platformId, platformName, fieldType)
            }
            assertEquals(false, response.success)
        }

        @Test
        fun `addCustomFieldMapping exception`() {
            val companyId = 100L
            val platformId = 1L
            val mockAddFieldRequest = AddCustomFieldMappingRequest(
                fieldFromApp = "employeeId",
                fieldId = "employeeId",
                mappedKey = "employeeId",
                dataType = "STRING"
            )
            val response = runBlocking {
                knitAdapter.addCustomFieldMapping(companyId, platformId, mockAddFieldRequest)
            }
            assertEquals(false, response.success)
        }

        @Test
        fun `test fetch leave types exception`() = runBlocking {
            val actualResponse = knitAdapter.getLeaveTypes(1L, 1L)
            assertNotNull(actualResponse)
            assertFalse(actualResponse.success!!)
        }

        @Test
        fun `test getLegalEntitiesOracleHCM exception`() = runBlocking {
            val response = knitAdapter.getLegalEntitiesOracleHCM(1L, 1L)

            assertTrue(response.success == false)
            assertNull(response.data)
            assertNull(response.responseCode)
            assertNotNull(response.error)
            assertTrue(response.error?.msg?.contains("Simulated server error") ?: false)
        }

        @Test
        fun `createLeaveRequest create leave exception`() = runBlocking {
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
            val leaveRequest = LeaveCreateRequest(
                employeeId = "emp001",
                leaveTypeId = "leaveType001",
                from = LocalDateTime.now().format(formatter),
                to = LocalDateTime.now().plusDays(5).format(formatter),
                unit = com.multiplier.integration.sync.model.Unit.DAYS,
                amount = 5.0,
            )
            val actualResponse = knitAdapter.createLeaveRequest(1L, 1L,"emp001",leaveRequest)
            assertNotNull(actualResponse)
            assertFalse(actualResponse.success)
        }

        @Test
        fun `test handle runtime exception during fetching departments list`() = runBlocking {
            val actualResponse = knitAdapter.getDepartmentsList(1L, 1L, "paychex")
            assertFalse(actualResponse.success)
            assertTrue(actualResponse.responseCode == 500)
            assertTrue(actualResponse.error?.msg?.contains("Simulated server error") ?: false)
        }
    }

    @Nested
    inner class KnitIntegrationTestErrorResponse {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.errorResponseKnitHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
        }

        @Test
        fun `test handle API error on creating employee`(): Unit = runBlocking {
            // Create a request with specific details
            val request = createEmployeeRequest(
                firstName = "Jane",
                lastName = "Smith",
                workEmail = "<EMAIL>"
            )

            val expectedResponse = CreateEmployeeResponse(
                success = false,
                errors = listOf(HttpStatusCode.BadRequest.description),
                responseCode = 400
            )

            val actualResponse = knitAdapter.createEmployeeRecord(1, 1, request)

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `test handle api error fetch position details`() = runBlocking {
            val actualResponse = knitAdapter.getPositionsDetails(1, 1)
            assertNotNull(actualResponse)
            assertFalse(actualResponse?.success ?: false)
            assertEquals(400, actualResponse?.responseCode)
        }

        @Test
        fun `test handle API error on fetching compensation plans`() = runBlocking {
            val actualResponse = knitAdapter.getCompensationPlan(1, 1)
            assertFalse(actualResponse.success)
            assertEquals(400, actualResponse.responseCode)
            assertEquals("Bad Request", actualResponse.error?.msg)
        }

        @Test
        fun `test handle API error on creating document`() = runBlocking {
            val documentData = CreateDocumentRequest(
                employeeId = "emp002",
                fileName = "contract.docx"
            )

            val actualResponse = knitAdapter.createDocument(1, 1, documentData)
            assertFalse(actualResponse.success)
            assertEquals(400, actualResponse.responseCode)
            assertEquals("Bad Request", actualResponse.error?.msg)
        }

        @Test
        fun `test handle API error on updating compensation`() = runBlocking {
            val compensationData = UpdateCompensationRequest(employeeId = "emp124")

            val actualResponse = knitAdapter.updateCompensation(1, 1, compensationData)
            assertFalse(actualResponse.success ?: true)
            assertEquals(400, actualResponse.responseCode)
            assertEquals("Bad Request", actualResponse.error?.msg)
        }

        @Test
        fun `test handle API error on updating employee details`() = runBlocking {
            val employeeData = UpdateEmployeeDetailsRequest(employeeId = "emp124")

            val actualResponse = knitAdapter.updateEmployeeDetails(1, 1, 1, employeeData)
            assertFalse(actualResponse.success ?: true)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching document categories`() = runBlocking {
            val actualResponse = knitAdapter.getDocCategories(1, 1)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.data)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching leave balance`() = runBlocking {
            val actualResponse = knitAdapter.getEmployeeLeaveBalance("integrationId", "emp124")
            assertFalse(actualResponse.success ?: true)
            assertNull(actualResponse.data)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on terminating employee`() = runBlocking {
            val request = TerminateEmployeeRequest(employeeId = "124", terminationDate = "2024-10-11", terminationReason = "No Longer Needed")
            val actualResponse = knitAdapter.terminateEmployee(1, 1, request)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.data)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching termination reasons`() = runBlocking {
            val actualResponse = knitAdapter.getTerminationReason(1, 1)
            assertFalse(actualResponse.success)
            assertNull(actualResponse.data)
            assertNotNull(actualResponse.error)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching work sites`() = runBlocking {
            val actualResponse = knitAdapter.getWorksitesResponse(1, 1)
            assertNotNull(actualResponse.error)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching bamboo work location`() = runBlocking {
            val actualResponse = knitAdapter.getBambooWorkLocations(1, 1)
            assertNotNull(actualResponse.error)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching FO location`() = runBlocking {
            val actualResponse = knitAdapter.getSuccessFactorsWorkLocations(1, 1)
            assertNotNull(actualResponse.error)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching FOBusinessUnit`() = runBlocking {
            val actualResponse = knitAdapter.getSuccessFactorsBusinessUnits(1, 1)
            assertNotNull(actualResponse.error)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test handle API error on fetching document categories passthrough`() = runBlocking {
            val actualResponse = knitAdapter.getDocumentCategories(1, 1, "1")
            assertNotNull(actualResponse.error)
            assertEquals(400, actualResponse.responseCode)
        }

        @Test
        fun `test failed sync start due to server error`() = runBlocking {
            every { syncRepository.findByIntegrationId(any()) } returns listOf()
            val result = knitAdapter.startSync("integrationId123", PlatformCategory.EXPENSES)
            assertNull(result, "Sync job ID should be null on sync failure.")
        }

        @Test
        fun `test failed employee update due to server error`() = runBlocking {
            val basicDetails = BasicDetails(firstName = "Error", lastName = "Case", fullLegalName = "", gender = Gender.MALE)
            val response = knitAdapter.updateEmployeeRecord(1L, 1L, "ext123", basicDetails)
            assertFalse(response.success, "Employee update should fail")
            assertNotNull(response.errorMessage, "Error message expected on failure")
        }

        @Test
        fun `getAllFields error`() {
            val companyId = 100L
            val platformId = 1L
            val platformName = "hibob"
            val response = runBlocking {
                knitAdapter.getAllFields(companyId, platformId, platformName)
            }
            assertEquals(false, response.success)
        }
        @Test
        fun `getFieldValues error`() {
            val companyId = 100L
            val platformId = 1L
            val platformName = "hibob"
            val fieldType = "employmentType"
            val response = runBlocking {
                knitAdapter.getFieldValues(companyId, platformId, platformName, fieldType)
            }
            assertEquals(false, response.success)
        }

        @Test
        fun `addCustomFieldMapping error`() {
            val companyId = 100L
            val platformId = 1L
            val mockAddFieldRequest = AddCustomFieldMappingRequest(
                fieldFromApp = "employeeId",
                fieldId = "employeeId",
                mappedKey = "employeeId",
                dataType = "STRING"
            )
            val response = runBlocking {
                knitAdapter.addCustomFieldMapping(companyId, platformId, mockAddFieldRequest)
            }
            assertEquals(false, response.success)
        }


        @Test
        fun `test fetch leave types failed`() = runBlocking {
            val actualResponse = knitAdapter.getLeaveTypes(1L, 1L)
            assertNotNull(actualResponse)
            assertFalse(actualResponse.success!!)
        }

        @Test
        fun `test getLegalEntitiesOracleHCM error response`() = runBlocking {
            val response = knitAdapter.getLegalEntitiesOracleHCM(1L, 1L)

            assertTrue(response.success == false)
            assertNull(response.data)
            assertEquals(400, response.responseCode)
            assertNotNull(response.error)
            assertEquals("Bad Request", response.error?.msg)
        }

        @Test
        fun `createLeaveRequest create leave exception`() = runBlocking {
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
            val leaveRequest = LeaveCreateRequest(
                employeeId = "emp001",
                leaveTypeId = "leaveType001",
                from = LocalDateTime.now().format(formatter),
                to = LocalDateTime.now().plusDays(5).format(formatter),
                unit = com.multiplier.integration.sync.model.Unit.DAYS,
                amount = 5.0,
            )
            val actualResponse = knitAdapter.createLeaveRequest(1L, 1L,"emp001",leaveRequest)
            assertNotNull(actualResponse)
            assertFalse(actualResponse.success)
        }

        @Test
        fun `test handle API error on fetching departments list`() = runBlocking {
            val actualResponse = knitAdapter.getDepartmentsList(1L, 1L, "paychex")
            assertFalse(actualResponse.success)
            assertNotNull(actualResponse)
            assertFalse(actualResponse.success)
        }
    }

    @Nested
    inner class PassthroughTestSuccess {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.successPassthroughHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test fetching work sites successfully`() = runBlocking {
            val response = knitAdapter.getWorksitesResponse(1, 1)
            response.success?.let { assertTrue(it) }
            assertNotNull(response.data)
            assertEquals("Main Office", response.data?.responseJson?.body?.name)
        }

        @Test
        fun `test fetching work locations successfully`() = runBlocking {
            val response = knitAdapter.getBambooWorkLocations(1, 1)
            assertTrue(response.success ?: false)
            assertNotNull(response.data)
            assertEquals("Main Office", response.data?.parsedData?.list?.first()?.name)
        }

        @Test
        fun `test fetching SAP work locations successfully`() = runBlocking {
            val response = knitAdapter.getSuccessFactorsWorkLocations(1L, 1L)
            assertTrue(response.success ?: false)
            assertNotNull(response.data)
            assertEquals("Main Office", response.data?.response?.body?.d?.results?.first()?.name)
        }

        @Test
        fun `test fetching SAP business units successfully`() = runBlocking {
            val response = knitAdapter.getSuccessFactorsBusinessUnits(1L, 1L)
            assertTrue(response.success ?: false)
            assertNotNull(response.data)
            assertEquals("Human Resources", response.data?.response?.body?.d?.results?.first()?.name)
        }

        @Test
        fun `test fetching document categories successfully`() = runBlocking {
            val response = knitAdapter.getDocumentCategories(1L, 1L, "1")
            assertTrue(response.success)
            assertNotNull(response.categories)
            assertEquals(2, response.categories?.size)
            assertEquals("HR Documents", response.categories?.first()?.name)
            assertEquals(true, response.categories?.first()?.canUploadFiles)
        }

        @Test
        fun `test fetching employee directory successfully`() = runBlocking {
            val response = knitAdapter.getEmployeeDirectory(1L, 1L)
            assertTrue(response.success)
            assertNotNull(response.employees)
            assertEquals(13, response.employees?.size)
            assertEquals("117", response.employees?.first()?.id)
        }

        @Test
        fun `test lookup employee hibob successfully`() = runBlocking {
            val response = knitAdapter.lookupHibobEmployeeByEmail(1L, 1L, "test")
            assertTrue(response.success)
            assertNotNull(response.data)
            assertEquals(1, response.data!!.employees!!.size)
        }

        @Test
        fun `test getLegalEntitiesOracleHCM success`() = runBlocking {
            // Act
            val response = knitAdapter.getLegalEntitiesOracleHCM(1L, 1L)

            // Assert
            assertTrue(response.success ?: false)
            assertNotNull(response.data)
            assertEquals(200, response.responseCode)

            // Verify response data structure
            assertNotNull(response.data?.response)
            assertNotNull(response.data?.response?.body)

            // Verify legal entities data
            val legalEntities = response.getLegalEntities()
            assertNotNull(legalEntities)
            assertTrue(legalEntities!!.isNotEmpty())

            // Verify first legal entity
            val firstEntity = legalEntities.first()
            assertEquals(300100037952498, firstEntity.OrganizationId)
            assertEquals("Test Legal Entity", firstEntity.Name)
            assertEquals("US", firstEntity.LegislationCode)
            assertEquals("2023-01-01", firstEntity.EffectiveStartDate)
            assertEquals("2023-12-31", firstEntity.EffectiveEndDate)
            assertEquals(123456789, firstEntity.PayrollStatutoryUnitId)
        }

        @Test
        fun `test add bamboo timeoff successfully`() = runBlocking {
            val response = knitAdapter.addBambooHrTimeOffRequest(
                1L, 1L, "1", BambooTimeOffRequest(
                    status = "requested",
                    start = "2024-12-12",
                    end = "2024-12-12",
                    timeOffTypeId = 88,
                    amount = 1.0,
                )
            )
            assertTrue(response.success)
            assertNotNull(response.data)
            assertEquals("1", response.data!!.id)
            assertEquals("150", response.data!!.employeeId)
            assertEquals("2024-12-03", response.data!!.start)
            assertEquals("2024-12-04", response.data!!.end)
            assertEquals("2025-01-07", response.data!!.created)
            assertEquals("requested", response.data!!.status.status)
            assertEquals("2025-01-07 11:34:32", response.data!!.status.lastChanged)
            assertEquals("2503", response.data!!.status.lastChangedByUserId)
            assertEquals("Blossom Fletcher", response.data!!.name)
            assertEquals("87", response.data!!.type?.id)
            assertEquals("Casual Leave", response.data!!.type?.name)
            assertEquals("days", response.data!!.amount?.unit)
            assertEquals("2", response.data!!.amount?.amount)
            assertEquals(0, response.data!!.notes!!.size)
            assertEquals(mapOf("2024-12-03" to "1", "2024-12-04" to "1"), response.data!!.dates)
            assertEquals(1, response.data!!.comments?.size)
            assertEquals("114", response.data!!.comments?.get(0)?.employeeId)
            assertEquals("let's go", response.data!!.comments?.get(0)?.comment)
            assertEquals("2025-01-07", response.data!!.comments?.get(0)?.commentDate)
            assertEquals("Pradeep Sundaram", response.data!!.comments?.get(0)?.commenterName)
            assertEquals(6, response.data!!.approvers?.size)
            assertEquals("2516", response.data!!.approvers?.get(0)?.userId)
            assertEquals("Ashwin PT", response.data!!.approvers?.get(0)?.displayName)
            assertEquals("190", response.data!!.approvers?.get(0)?.employeeId)
            assertEquals("https://resources.bamboohr.com/images/photo_person_160x160.png", response.data!!.approvers?.get(0)?.photoUrl)
            assertTrue(response.data!!.actions?.view == true)
            assertTrue(response.data!!.actions?.edit == true)
            assertTrue(response.data!!.actions?.cancel == true)
            assertTrue(response.data!!.actions?.approve == true)
            assertTrue(response.data!!.actions?.deny == true)
            assertTrue(response.data!!.actions?.bypass == true)
            assertEquals("discretionary", response.data!!.policyType)
            assertEquals(0, response.data!!.usedYearToDate)
            assertEquals(0, response.data!!.balanceOnDateOfRequest)

        }

        @Test
        fun `test approve timeoff bamboo failed exception`() = runBlocking {
            val response = knitAdapter.approveBambooHrTimeOffRequest(
                1L, 1L, "1", "approved", ""
            )
            assertTrue(response.success)
        }
    }

    @Nested
    inner class PassthroughTestSuccessEdgeCase {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.successEdgeCasePassthroughHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test fetching work sites successfully`() = runBlocking {
            val response = knitAdapter.getWorksitesResponse(1, 1)
            response.success?.let { assertTrue(it) }
            assertNotNull(response.data)
            assertEquals("Main Office", response.data?.responseJson?.body?.name)
        }
    }

    @Nested
    inner class PassthroughResponseException {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.exceptionPassthroughHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test fetching work sites deserialize exception`() = runBlocking {
            val response = knitAdapter.getWorksitesResponse(1, 1)
            assertTrue(response.error?.msg?.contains("Error deserializing response") ?: false)
        }

        @Test
        fun `test fetching doc categories deserialize exception`() = runBlocking {
            val response = knitAdapter.getDocumentCategories(1, 1, "1")
            assertTrue(response.error?.msg?.contains("Error parsing XML") ?: false)
        }

        @Test
        fun `test fetching employee directory failed`() = runBlocking {
            val response = knitAdapter.getEmployeeDirectory(1L, 1L)
            assertFalse(response.success)
            assertNull(response.employees)
            assertEquals(404, response.responseCode)
        }

        @Test
        fun `test lookup employee hibob failed`() = runBlocking {
            val response = knitAdapter.lookupHibobEmployeeByEmail(1L, 1L, "test")
            assertFalse(response.success)
            assertNull(response.data)
            assertEquals(404, response.responseCode)
        }

        @Test
        fun `test add timeoff bamboo failed exception`() = runBlocking {
            val response = knitAdapter.addBambooHrTimeOffRequest(
                1L, 1L, "1", BambooTimeOffRequest(
                    status = "requested",
                    start = "2024-12-12",
                    end = "2024-12-12",
                    timeOffTypeId = 88,
                    amount = 1.0,
                )
            )
            assertFalse(response.success)
            assertNull(response.data)
            assertEquals(400, response.responseCode)
        }

        @Test
        fun `test add timeoff bamboo failed not found`() = runBlocking {
            val response = knitAdapter.addBambooHrTimeOffRequest(
                1L, 1L, "15", BambooTimeOffRequest(
                    status = "requested",
                    start = "2024-12-12",
                    end = "2024-12-12",
                    timeOffTypeId = 88,
                    amount = 1.0,
                )
            )
            assertFalse(response.success)
            assertNull(response.data)
            assertEquals(404, response.responseCode)
        }

        @Test
        fun `test approve timeoff bamboo failed exception`() = runBlocking {
            val response = knitAdapter.approveBambooHrTimeOffRequest(
                1L, 1L, "1", "approved", ""
            )
            assertFalse(response.success)
            assertEquals(response.error?.msg, "Error approving time off request: Knit server error with code: 500 Internal Server Error")
            assertEquals(400, response.responseCode)
        }
    }

    @Nested
    inner class PassthroughEmptyResponse {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.emptyResponsePassthroughHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test fetching work sites empty response`() = runBlocking {
            val response = knitAdapter.getWorksitesResponse(1, 1)
            assertEquals("Invalid response format", response.error?.msg)
        }

        @Test
        fun `test fetching doc categories empty response`() = runBlocking {
            val response = knitAdapter.getDocumentCategories(1, 1, "1")
            assertEquals("Invalid response format", response.error?.msg)
        }

        @Test
        fun `test fetching employee directory parse exception`() = runBlocking {
            val response = knitAdapter.getEmployeeDirectory(1L, 1L)
            assertFalse(response.success)
            assertNull(response.employees)
            assertEquals("Error parsing XML: XML document structures must start and end within the same entity.", response.error?.msg)
        }

        @Test
        fun `test lookup empty employee error`() = runBlocking {
            val response = knitAdapter.lookupHibobEmployeeByEmail(1L, 1L, "test")
            assertFalse(response.success)
            assertNull(response.data)
        }

        @Test
        fun `test getLegalEntitiesOracleHCM empty response`() = runBlocking {
            // Act
            val response = knitAdapter.getLegalEntitiesOracleHCM(1L, 1L)

            // Assert
            assertTrue(response.success ?: false)
            assertNotNull(response.data)
            assertEquals(200, response.responseCode)

            // Verify legal entities data
            val legalEntities = response.getLegalEntities()
            assertNotNull(legalEntities)
            assertTrue(legalEntities!!.isEmpty())
        }
    }

    @Nested
    inner class TestAuthKnit {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.authKnitHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test successful token retrieval`() = runBlocking {
            val response = knitAdapter.requestAuthToken("1", "Test Company", "<EMAIL>", "Test User", "app_id", true, "successCategory")
            assertTrue(response?.success == true)
            assertEquals("valid_token", response?.msg?.token)
        }

        @Test
        fun `test failed token retrieval due to invalid category`() = runBlocking {
            val response = knitAdapter.requestAuthToken("1", "Test Company", "<EMAIL>", "Test User", "app_id", true, "invalidCategory")
            assertTrue(response?.success == false)
            assertNotNull(response?.error)
            assertEquals("invalid_category", response?.error?.msg)
        }
    }

    @Nested
    inner class TestExpenseReportKnit {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.syncKnitHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
            every { syncRepository.findByIntegrationId(any()) } returns listOf()
        }

        @Test
        fun `test update expense report successfully`() = runBlocking {
            val result = knitAdapter.updateExpenseReport("integrationId123", "validReportId")
            assertTrue(result, "Expense report should be updated successfully.")
        }

        @Test
        fun `test update expense report with invalid ID`() = runBlocking {
            val result = knitAdapter.updateExpenseReport("integrationId123", "invalidReportId")
            assertFalse(result, "Expense report update should fail due to invalid report ID.")
        }

        @Test
        fun `test update expense report with server error`() = runBlocking {
            val result = knitAdapter.updateExpenseReport("integrationId123", "errorTriggeringId")
            assertFalse(result, "Expense report update should fail due to a simulated server error.")
        }

        @Test
        fun `test successful initial sync start`() = runBlocking {
            val result = knitAdapter.startSync("integrationId123", PlatformCategory.EXPENSES)
            assertNotNull(result, "Sync job ID should not be null for successful sync.")
            assertEquals("12345", result, "Expected sync job ID should match.")
        }

        @Test
        fun `test successful not-initial sync start`() = runBlocking {
            every { syncRepository.findByIntegrationId(any()) } returns listOf(mock<JpaSync>())
            val result = knitAdapter.startSync("integrationId123", PlatformCategory.EXPENSES)
            assertNotNull(result, "Sync job ID should not be null for successful sync.")
            assertEquals("12345", result, "Expected sync job ID should match.")
        }

        @Test
        fun `test unsuccessful response sync start`() = runBlocking {
            every { syncRepository.findByIntegrationId(any()) } returns listOf(mock<JpaSync>())
            val result = knitAdapter.startSync("integrationId123", PlatformCategory.HRIS)
            assertNull(result)
        }
    }

    @Nested
    inner class HibobEmployeeBankAccountsTest {
        @MockK
        private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

        @MockK
        private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

        @MockK
        private lateinit var syncRepository: SyncRepository

        private var httpClient: HttpClient = TestHttpClientConfig.successPassthroughHttpClient()

        @InjectMockKs
        private lateinit var knitAdapter: DefaultKnitAdapter

        @BeforeEach
        fun setUp() {
            ReflectionTestUtils.setField(knitAdapter, "knitBaseApiUrl", "http://localhost:8080/")
            MockKAnnotations.init(this)
            every { companyIntegrationRepository.findEnabledIntegration(any(), any()) } returns listOf(
                JpaCompanyIntegration(
                    id = null,
                    accountToken = "token",
                    companyId = 1,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    platform = JpaPlatform(id = null, isPositionDropdownEnabled = false),
                    provider = JpaProvider(id = null, name = ProviderName.KNIT),
                )
            )
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(any(), any()) } returns listOf(
                JpaPlatformEmployeeData(
                    id = null,
                    integrationId = 1,
                    employeeId = "1",
                    employeeData = "{\"profile\": {\"id\": \"3342979250784306159\", \"gender\": null, \"lastName\": \"Perkins\", \"birthDate\": null, \"firstName\": \"Alexander\", \"startDate\": null, \"workEmail\": \"<EMAIL>\", \"maritalStatus\": null, \"employeeNumber\": null, \"employmentType\": null, \"terminationDate\": null, \"employmentStatus\": null}, \"locations\": null, \"rawValues\": null, \"dependents\": null, \"employeeDetailData\": {\"city\": \"HN\", \"country\": \"VN\", \"lastName\": \"Test\", \"firstName\": \"Test1\", \"locationId\": \"A123\", \"postalCode\": \"100\", \"addressLine1\": \"Test1\", \"addressLine2\": \"Test\", \"emailAddress\": \"<EMAIL>\", \"contactNumber\": \"Test\", \"workEmailAddress\": \"Test\"}, \"contactInfo\": null, \"employeeKYC\": null, \"bankAccounts\": null, \"compensation\": null, \"customFields\": null, \"orgStructure\": null, \"employeeProfilePicture\": null, \"employeeIdentificationData\": null}",
                    origin = "",
                    isDeleted = false
                )
            )
            every { platformEmployeeDataRepository.save(any()) } returns mock<JpaPlatformEmployeeData>()
        }

        @Test
        fun `test fetching hibob employee bank accounts successfully`() = runBlocking {
            val response = knitAdapter.getHibobEmployeeBankAccounts(1L, 1L, "1")
            assertTrue(response.isNotEmpty())
            assertNotNull(response.first())
            assertEquals("Checking", response.first().bankAccountType)
        }

        @Test
        fun `test fetching hibob employee bank accounts failed`() = runBlocking {
            val response = knitAdapter.getHibobEmployeeBankAccounts(1L, 1L, "2")
            assertTrue(response.isEmpty())
        }
    }
}
