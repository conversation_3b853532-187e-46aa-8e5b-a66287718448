package com.multiplier.integration.adapter.api.resources.financial.mapping

import com.merge.api.resources.accounting.types.InvoiceRequest
import com.merge.api.resources.accounting.types.InvoiceStatusEnum
import com.multiplier.integration.accounting.domain.mapping.*
import com.multiplier.integration.adapter.api.resources.financial.MergeDevAccountApi
import com.multiplier.integration.adapter.api.resources.financial.MergeDevInvoiceRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mapstruct.factory.Mappers
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.*

@ExtendWith(SpringExtension::class)
class MergeDevInvoiceRequestMapperTest {
    private val mapper: MergeDevInvoiceRequestMapper = Mappers.getMapper(MergeDevInvoiceRequestMapper::class.java)

    @Test
    fun `test mapping from MergeDevInvoiceRequest to InvoiceRequest`() {
        val accountingInvoice = getMergeDevAccountingTransactionInvoice()

        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )
        val offset: ZoneOffset = ZoneOffset.UTC
        val invoiceRequest: InvoiceRequest = mapper.toInvoiceRequest(mergeDevInvoiceRequest)

        assertEquals(accountingInvoice.number, invoiceRequest.number.get())
        assertEquals(
            accountingInvoice.dueDate?.atStartOfDay(offset)?.year,
            invoiceRequest.issueDate.get().year,
        )
        assertEquals(
            accountingInvoice.issueDate?.atStartOfDay(offset)?.year,
            invoiceRequest.issueDate.get().year,
        )
        assertEquals(InvoiceStatusEnum.SUBMITTED.toString(), invoiceRequest.status.get().toString())
        assertEquals(accountingInvoice.commonFields?.exchangeRate, invoiceRequest.exchangeRate.get())
        assertEquals(accountingInvoice.totalAmount, invoiceRequest.totalAmount.get())
        assertEquals(accountingInvoice.memo, invoiceRequest.memo.get())
    }

    @Test
    fun `test mapping from MergeDevInvoiceRequest to InvoiceRequest lineItems`() {
        val accountingInvoice = getMergeDevAccountingTransactionInvoice()

        val mergeDevLineItem = getMergeDevLineItemObj()
        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )

        val offset: ZoneOffset = ZoneOffset.UTC

        val invoiceRequest: InvoiceRequest = mapper.toInvoiceRequest(mergeDevInvoiceRequest)

        assertEquals(accountingInvoice.number, invoiceRequest.number.get())
        assertEquals(
            accountingInvoice.dueDate?.atStartOfDay(offset)?.year,
            invoiceRequest.issueDate.get().year,
        )
        assertEquals(
            accountingInvoice.issueDate?.atStartOfDay(offset)?.year,
            invoiceRequest.issueDate.get().year,
        )
        assertEquals(InvoiceStatusEnum.SUBMITTED.toString(), invoiceRequest.status.get().toString())
        assertEquals(accountingInvoice.commonFields?.exchangeRate, invoiceRequest.exchangeRate.get())
        assertEquals(accountingInvoice.totalAmount, invoiceRequest.totalAmount.get())
        assertEquals(accountingInvoice.memo, invoiceRequest.memo.get())
        assertEquals(1, invoiceRequest.lineItems.get().size)

        val invoiceLineItem = invoiceRequest.lineItems.get()[0]
        assertEquals(mergeDevLineItem.description, invoiceLineItem.description.get())
        assertEquals(mergeDevLineItem.quantity, invoiceLineItem.quantity.get())
        assertEquals(mergeDevLineItem.totalAmount, invoiceRequest.totalAmount.get())
        assertEquals(
            mergeDevLineItem.commonFields?.currency,
            invoiceLineItem.currency
                .get()
                .get()
                .toString(),
        )
        assertEquals(mergeDevLineItem.commonFields?.exchangeRate, invoiceLineItem.exchangeRate.get())
        assertEquals(
            mergeDevLineItem.account,
            invoiceLineItem.account
                .get()
                .get()
                .toString(),
        )
        assertEquals(
            mergeDevLineItem.commonFields?.trackingCategories,
            invoiceLineItem.trackingCategories.get().map { it.get().toString() },
        )
    }

    private fun getMergeDevAccountingTransactionInvoice(): AccountingTransactionInvoice =
        AccountingTransactionInvoice(
            contact = "Test Contact",
            number = "INV-123",
            issueDate = LocalDate.now(),
            dueDate = LocalDate.now(),
            status = "AUTHORIZED",
            totalAmount = 100.0,
            memo = "Test Memo",
            lineItems = listOf(getMergeDevLineItemObj()),
            appliedVendorCredits = emptyList(),
            commonFields =
                AccountingTransactionCommonFields(
                    company = "Test Company",
                    trackingCategories = listOf("Category1", "Category2"),
                    exchangeRate = "1.0",
                    currency = "USD",
                ),
        )

    private fun getMergeDevLineItemObj(): AccountingTransactionLineItem =
        AccountingTransactionLineItem(
            quantity = 1.0,
            unitPrice = 100.0,
            totalAmount = 100.0,
            item = "Test Item",
            account = "Test Account",
            description = "Test Description",
            commonFields =
                AccountingTransactionCommonFields(
                    remoteId = "1",
                    company = "Test Company",
                    trackingCategories = listOf("Category1", "Category2"),
                    exchangeRate = "1.0",
                    currency = "USD",
                ),
        )
}
