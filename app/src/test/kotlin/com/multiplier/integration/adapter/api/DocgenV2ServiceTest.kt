package com.multiplier.integration.adapter.api

import com.google.protobuf.ByteString
import com.google.protobuf.kotlin.toByteString
import com.multiplier.documentgeneration.v2.grpc.schema.DocumentServiceGrpc
import com.multiplier.documentgeneration.v2.grpc.schema.downloadDocumentResponse
import io.mockk.called
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class DocgenV2ServiceTest {
    @MockK private lateinit var docgenServiceAdapterV1: DocgenServiceAdapter
    @MockK private lateinit var documentClient: DocumentServiceGrpc.DocumentServiceBlockingStub

    @InjectMockKs private lateinit var underTest: DocgenV2Service

    @Test
    fun `should get document from v1`() {
        every { docgenServiceAdapterV1.getDocument(any()) } returns mockk()

        underTest.getDocument("123456")

        verify(exactly = 1) { docgenServiceAdapterV1.getDocument(any()) }
        verify { documentClient wasNot called }
    }

    @Test
    fun `should get document from v2`() {
        every { documentClient.downloadDocument(any()) } returns mockk()

        underTest.getDocument("123e4567-e89b-12d3-a456-426614174000")

        verify(exactly = 1) { documentClient.downloadDocument(any()) }
        verify { docgenServiceAdapterV1 wasNot called }
    }

    @Test
    fun `should build data url from file content`() {
        every { documentClient.downloadDocument(any()) } returns downloadDocumentResponse {
            fileContent = "Hello World".toByteArray().toByteString()
        }

        val result = underTest.getDocument("123e4567-e89b-12d3-a456-426614174000")
        assertThat(result?.internalDownloadURL().toString()).isEqualTo("http://base64-data/SGVsbG8gV29ybGQ=")
    }

    @Test
    fun `can handle data url with special characters`() {
        every { documentClient.downloadDocument(any()) } returns downloadDocumentResponse {
            fileContent = ByteString.fromHex("FBFF")
        }

        val result = underTest.getDocument("123e4567-e89b-12d3-a456-426614174000")
        assertThat(result?.internalDownloadURL().toString()).isEqualTo("http://base64-data/+/8=")
    }
}