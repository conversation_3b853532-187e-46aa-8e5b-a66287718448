package com.multiplier.platform.utility

import com.multiplier.integration.repository.SFTPAccessRequestRepository
import com.multiplier.integration.repository.model.BulkUploadModule
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.util.Optional

@ExtendWith(MockKExtension::class)
class SFTPAccessRequestUpdaterTest {

    @MockK
    private lateinit var sftpAccessRequestRepository: SFTPAccessRequestRepository

    @InjectMockKs
    private lateinit var sftpAccessRequestUpdater: SFTPAccessRequestUpdater

    @Nested
    inner class Validation {
        @Test
        fun `should validate valid input with APPROVED status and directory`() {
            // Given
            val input = SFTPAccessRequestUpdateInput(
                requestId = 1L,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = "/sftp/company123/entity456"
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(1L) } returns Optional.of(
                JpaSFTPAccessRequest(
                    id = 1L,
                    companyId = 123L,
                    entityId = 456L,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                    status = SftpAccessRequestStatus.PENDING,
                    mainSFTPDirectory = null
                )
            )

            // Mock that no other request is using this directory
            every { sftpAccessRequestRepository.findByMainSFTPDirectory("sftp/company123/entity456/") } returns null

            // When/Then - No exception should be thrown
            sftpAccessRequestUpdater.validate(input)
        }

        @Test
        fun `should validate valid input with non-APPROVED status`() {
            // Given
            val input = SFTPAccessRequestUpdateInput(
                requestId = 1L,
                status = SftpAccessRequestStatus.REJECTED,
                mainSftpDirectory = null
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(1L) } returns Optional.of(
                JpaSFTPAccessRequest(
                    id = 1L,
                    companyId = 123L,
                    entityId = 456L,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                    status = SftpAccessRequestStatus.PENDING,
                    mainSFTPDirectory = null
                )
            )

            // No need to mock findByStatusAndMainSftpDirectory since it won't be called for non-APPROVED status

            // When/Then - No exception should be thrown
            sftpAccessRequestUpdater.validate(input)
        }

        @Test
        fun `should throw exception when request ID is invalid`() {
            // Given
            val input = SFTPAccessRequestUpdateInput(
                requestId = 0L,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = "/sftp/company123/entity456"
            )

            // No need to mock repository behavior as validation should fail before checking existence

            // When/Then
            assertThatThrownBy { sftpAccessRequestUpdater.validate(input) }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("Request ID must be greater than 0")
        }

        @Test
        fun `should throw exception when APPROVED status has no directory`() {
            // Given
            val input = SFTPAccessRequestUpdateInput(
                requestId = 1L,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = null
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(any()) } returns Optional.of(
                JpaSFTPAccessRequest(
                    id = 1L,
                    companyId = 123L,
                    entityId = 456L,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                    status = SftpAccessRequestStatus.PENDING,
                    mainSFTPDirectory = null
                )
            )

            // When/Then
            assertThatThrownBy { sftpAccessRequestUpdater.validate(input) }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("Main SFTP directory is required when status is APPROVED")
        }

        @Test
        fun `should throw exception when directory is already in use by another request`() {
            // Given
            val requestId = 1L
            val directoryPath = "/sftp/company123/entity456"
            val normalizedPath = "$directoryPath/"

            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = directoryPath
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = 2L, // Different ID than the request being updated
                companyId = 789L,
                entityId = 101L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.APPROVED,
                mainSFTPDirectory = normalizedPath
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(
                JpaSFTPAccessRequest(
                    id = requestId,
                    companyId = 123L,
                    entityId = 456L,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                    status = SftpAccessRequestStatus.PENDING,
                    mainSFTPDirectory = null
                )
            )

            // Mock that another request is already using this directory
            every { sftpAccessRequestRepository.findByMainSFTPDirectory("sftp/company123/entity456/") } returns existingRequest

            // When/Then
            assertThatThrownBy { sftpAccessRequestUpdater.validate(input) }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("Main SFTP directory 'sftp/company123/entity456/' is already in use by another request")
        }

        @Test
        fun `should allow updating a request with the same directory it already has`() {
            // Given
            val requestId = 1L
            val directoryPath = "/sftp/company123/entity456/"

            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = directoryPath
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = requestId, // Same ID as the request being updated
                companyId = 123L,
                entityId = 456L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.APPROVED,
                mainSFTPDirectory = directoryPath
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(existingRequest)

            // Mock that the same request is already using this directory
            every { sftpAccessRequestRepository.findByMainSFTPDirectory("sftp/company123/entity456/") } returns existingRequest

            // When/Then - No exception should be thrown
            sftpAccessRequestUpdater.validate(input)
        }

        @Test
        fun `should throw exception when APPROVED status has empty directory`() {
            // Given
            val input = SFTPAccessRequestUpdateInput(
                requestId = 1L,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = ""
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(any()) } returns Optional.of(
                JpaSFTPAccessRequest(
                    id = 1L,
                    companyId = 123L,
                    entityId = 456L,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                    status = SftpAccessRequestStatus.PENDING,
                    mainSFTPDirectory = null
                )
            )

            // When/Then
            assertThatThrownBy { sftpAccessRequestUpdater.validate(input) }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("Main SFTP directory is required when status is APPROVED")
        }
    }

    @Nested
    inner class Execution {
        @Test
        fun `should update request status to APPROVED with directory and add trailing slash`() {
            // Given
            val requestId = 1L
            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = "/sftp/company123/entity456"
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = requestId,
                companyId = 123L,
                entityId = 456L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.PENDING,
                mainSFTPDirectory = null
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(existingRequest)
            every { sftpAccessRequestRepository.findByMainSFTPDirectory("sftp/company123/entity456/") } returns null
            every { sftpAccessRequestRepository.save(any()) } returns existingRequest

            // When
            sftpAccessRequestUpdater.execute(input)

            // Then
            verify { sftpAccessRequestRepository.findById(requestId) }
            verify { sftpAccessRequestRepository.save(existingRequest) }

            // Verify the request was updated correctly
            assertThat(existingRequest.status).isEqualTo(SftpAccessRequestStatus.APPROVED)
            assertThat(existingRequest.mainSFTPDirectory).isEqualTo("sftp/company123/entity456/")
        }

        @Test
        fun `should update request status to REJECTED without changing directory`() {
            // Given
            val requestId = 1L
            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.REJECTED,
                mainSftpDirectory = null
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = requestId,
                companyId = 123L,
                entityId = 456L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.PENDING,
                mainSFTPDirectory = null
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(existingRequest)
            // No need to mock findByStatusAndMainSftpDirectory for non-APPROVED status
            every { sftpAccessRequestRepository.save(any()) } returns existingRequest

            // When
            sftpAccessRequestUpdater.execute(input)

            // Then
            verify { sftpAccessRequestRepository.findById(requestId) }
            verify { sftpAccessRequestRepository.save(existingRequest) }

            // Verify the request was updated correctly
            assertThat(existingRequest.status).isEqualTo(SftpAccessRequestStatus.REJECTED)
            assertThat(existingRequest.mainSFTPDirectory).isNull()
        }

        @Test
        fun `should preserve existing directory when updating to non-APPROVED status`() {
            // Given
            val requestId = 1L
            val existingDirectory = "/existing/directory/"
            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.REJECTED,
                mainSftpDirectory = null
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = requestId,
                companyId = 123L,
                entityId = 456L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.APPROVED,
                mainSFTPDirectory = existingDirectory
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(existingRequest)
            // No need to mock findByStatusAndMainSftpDirectory for non-APPROVED status
            every { sftpAccessRequestRepository.save(any()) } returns existingRequest

            // When
            sftpAccessRequestUpdater.execute(input)

            // Then
            verify { sftpAccessRequestRepository.findById(requestId) }
            verify { sftpAccessRequestRepository.save(existingRequest) }

            // Verify the request was updated correctly - status changed but directory remained the same
            assertThat(existingRequest.status).isEqualTo(SftpAccessRequestStatus.REJECTED)
            assertThat(existingRequest.mainSFTPDirectory).isEqualTo(existingDirectory)
        }

        @Test
        fun `should update directory when it is provided for APPROVED status and add trailing slash`() {
            // Given
            val requestId = 1L
            val existingDirectory = "/existing/directory/"
            val newDirectory = "/new/directory"
            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = newDirectory
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = requestId,
                companyId = 123L,
                entityId = 456L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.PENDING,
                mainSFTPDirectory = existingDirectory
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(existingRequest)
            every { sftpAccessRequestRepository.findByMainSFTPDirectory("new/directory/") } returns null
            every { sftpAccessRequestRepository.save(any()) } returns existingRequest

            // When
            sftpAccessRequestUpdater.execute(input)

            // Then
            verify { sftpAccessRequestRepository.findById(requestId) }
            verify { sftpAccessRequestRepository.save(existingRequest) }

            // Verify the request was updated correctly - status and directory changed
            assertThat(existingRequest.status).isEqualTo(SftpAccessRequestStatus.APPROVED)
            assertThat(existingRequest.mainSFTPDirectory).isEqualTo("new/directory/")
        }

        @Test
        fun `should preserve trailing slash if directory already has one`() {
            // Given
            val requestId = 1L
            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = "/sftp/company123/entity456/"
            )

            val existingRequest = JpaSFTPAccessRequest(
                id = requestId,
                companyId = 123L,
                entityId = 456L,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.PENDING,
                mainSFTPDirectory = null
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.of(existingRequest)
            every { sftpAccessRequestRepository.findByMainSFTPDirectory("sftp/company123/entity456/") } returns null
            every { sftpAccessRequestRepository.save(any()) } returns existingRequest

            // When
            sftpAccessRequestUpdater.execute(input)

            // Then
            verify { sftpAccessRequestRepository.findById(requestId) }
            verify { sftpAccessRequestRepository.save(existingRequest) }

            // Verify the request was updated correctly
            assertThat(existingRequest.status).isEqualTo(SftpAccessRequestStatus.APPROVED)
            assertThat(existingRequest.mainSFTPDirectory).isEqualTo("sftp/company123/entity456/")
        }

        @Test
        fun `should throw exception when request not found`() {
            // Given
            val requestId = 999L
            val input = SFTPAccessRequestUpdateInput(
                requestId = requestId,
                status = SftpAccessRequestStatus.APPROVED,
                mainSftpDirectory = "/sftp/company123/entity456"
            )

            // Mock repository behavior
            every { sftpAccessRequestRepository.findById(requestId) } returns Optional.empty()
            // We need to mock this method even though it won't be called because the validation will fail earlier
            every { sftpAccessRequestRepository.findByMainSFTPDirectory(any()) } returns null

            // When/Then
            assertThatThrownBy { sftpAccessRequestUpdater.validate(input) }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessageContaining("SFTP access request not found with ID: $requestId")
        }
    }
}
