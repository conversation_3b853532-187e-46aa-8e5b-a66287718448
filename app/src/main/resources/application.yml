server:
  max-http-request-header-size: 1MB
spring:
  application:
    name: customer-integration-service
  main:
    banner-mode: OFF
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      poolName: Hikari
      auto-commit: false
      max-lifetime: 1800000
      idle-timeout: 300000
      connection-timeout: 30000
      minimum-idle: 2
      maximum-pool-size: 10
      keepalive-time: 60000
      registerMbeans: true
  jpa:
    show-sql: false
    open-in-view: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate.jdbc.time_zone: UTC

platform:
  base-url: https://app.usemultiplier.com
  kafka:
    auto-startup: false
    group-id: customer-integration-2
    bootstrap-servers: localhost:9092
  merge-dev:
    api-key: ${PLATFORM_MERGE-DEV_API-KEY}
    enabled: true
    api-url: https://api.merge.dev
  userservice:
    system.notification.email: <EMAIL>
  pigeon-service:
    enableKeepAlive: true
    keepAliveWithoutCalls: true
    negotiationType: TLS
  knit:
    api-key: ${PLATFORM_KNIT_API_KEY}
    api-url: https://api.getknit.dev/v1.0/
  maximum-retry: 3
  trinet:
    api-url: https://apiqe1.trinet.com

cloud:
  aws:
    s3:
      enabled: true
      bucket: ${AWS_S3_BUCKET}
    region:
      static: ${AWS_REGION}

kafka:
  group-id: customer-integration
  bootstrap-servers: ${PLATFORM_CUSTOMER_INTEGRATION_KAFKA_BOOTSTRAPSERVERS}

grpc:
  client:
    bulk-upload-service:
      address: ${GRPC_CLIENT_BULK_UPLOAD_SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    core-service:
      address: ${GRPC_CLIENT_CORESERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    member-service:
      address: ${GRPC_CLIENT_MEMBERSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    pigeon-service:
      address: ${GRPC_CLIENT_PIGEONSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-service:
      address: ${GRPC_CLIENT_CONTRACTSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    country-service:
      address: ${GRPC_CLIENT_COUNTRYSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    expense-service:
      address: ${GRPC_CLIENT_EXPENSESERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-offboarding-service:
      address: ${GRPC_CLIENT_CONTRACT_OFFBOARDING_SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    company-service:
      address: ${GRPC_CLIENT_COMPANY_SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-onboarding-service:
      address: ${GRPC_CLIENT_CONTRACT_ONBOARDING_SERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    timeoff-service:
      address: ${GRPC_CLIENT_TIMEOFFSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    pay-se:
      address: ${GRPC_CLIENT_PAYSE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    authority-service:
      address: ${GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    org-management-service:
        address: ${GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS}
        enableKeepAlive: true
        keepAliveWithoutCalls: true
        negotiationType: TLS
    payable-service:
        address: ${GRPC_CLIENT_PAYABLESERVICE_ADDRESS}
        enableKeepAlive: true
        keepAliveWithoutCalls: true
        negotiationType: TLS
    field-mapping-service:
        address: ${GRPC_CLIENT_FIELD_MAPPING_SERVICE_ADDRESS}
        enableKeepAlive: true
        keepAliveWithoutCalls: true
        negotiationType: TLS
    docgen-service:
        address: ${GRPC_CLIENT_DOCGENSERVICE_ADDRESS}
        enableKeepAlive: true
        keepAliveWithoutCalls: true
        negotiationType: TLS
  server:
    port: 9090
    max-inbound-metadata-size: 2097152
    security:
      enabled: true
      certificate-chain: classpath:certificates/server.crt
      private-key: classpath:certificates/server.key

scheduler:
  cron:
    member-update: "0 */1 * * * *"

positions:
  cache.ttl: 86400 # 24 hours cache TTL
  batch.size: 3
  scheduler:
    enabled: true
    cron: "0 0 0 * * *"

pigeon:
  client:
    kafka:
      bootstrap-servers: ${PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS}

jwt:
  public-key: MIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAmzU6eIvgeuPL9Rd/NHjqyatae8pg4AeJxMo/judIMA3n4MAxPbI6/2VboB+0sdzG+Bc13AHXNExqEFBcyMdKV8oB+yoR4uTc/qrDeTZhoDvETBLVHwfRbX4ecTA2+I0DD5huI/CAcbKdrIiLqs4h+A4wi50CJ+D+I6TBkB3jTGJsRrY=
mpl:
  graphql:
    scalar:
      enabled: false
    error-resolver:
      enabled: true
    instrumentation:
      enabled: true
  grpc:
    coroutine:
      enabled: false
    instrumentation:
      enabled: true
    error-mapping:
      enabled: true

growthbook:
  base-url: ${GROWTHBOOK_BASEURL}
  env-key: ${GROWTHBOOK_ENVKEY}
  refresh-frequency-ms: 15000

ops:
  support-email: <EMAIL>

common:
  acl:
    enabled: true
    strict-mode: true # Enable strict mode for company and entity access check

integration:
  webhook:
    sftp:
      api-key: ${INTEGRATION_WEBHOOK_SFTP_API_KEY}
