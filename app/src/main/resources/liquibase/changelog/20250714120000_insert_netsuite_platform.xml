<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="**************-1" author="madhupmamodia" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'ACCOUNTING' and name = 'NetSuite'
            </sqlCheck>
        </preConditions>
        <comment>Insert ACCOUNTING platform NetSuite</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (23, 'ACCOUNTING', 'NetSuite');
        </sql>
    </changeSet>

    <changeSet id="**************-2" author="madhupmamodia" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [customer_integration]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider_platform pp
                JOIN customer_integration.platform p ON pp.platform_id = p.id
                WHERE p.name = 'NetSuite' and pp.provider_id = 1
            </sqlCheck>
        </preConditions>
        <comment>Insert NetSuite integration platform</comment>
        <sql>
            INSERT INTO customer_integration.provider_platform
                (platform_id, provider_id, status, created_on)
            SELECT p.id, 1, 'ACTIVE', now()
            FROM customer_integration.platform p
            WHERE p.name = 'NetSuite' AND p.category = 'ACCOUNTING';
        </sql>
    </changeSet>

</databaseChangeLog>
