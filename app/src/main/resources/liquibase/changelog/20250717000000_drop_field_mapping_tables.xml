<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250717000000-1" author="vidya" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="fields_mapping" schemaName="customer_integration"/>
        </preConditions>
        <comment>Drop fields_mapping table as it's replaced by field-mapping-service</comment>

        <!-- First drop the foreign key constraint -->
        <dropForeignKeyConstraint
                baseTableName="fields_mapping"
                baseTableSchemaName="customer_integration"
                constraintName="fk_fields_mapping_parent_id"/>

        <!-- Then drop the table -->
        <dropTable tableName="fields_mapping" schemaName="customer_integration"/>

        <rollback>
            <createTable tableName="fields_mapping" schemaName="customer_integration">
                <column name="id" type="BIGINT" autoIncrement="true">
                    <constraints nullable="false" primaryKey="true" primaryKeyName="fields_mapping_pkey"/>
                </column>
                <column name="entity_id" type="BIGINT"/>
                <column name="origin_field" type="VARCHAR(255)"/>
                <column name="origin_field_label" type="VARCHAR(255)"/>
                <column name="mapped_field" type="VARCHAR(255)"/>
                <column name="mapped_field_label" type="VARCHAR(255)"/>
                <column name="is_required" type="BOOLEAN"/>
                <column name="is_active" type="BOOLEAN"/>
                <column name="company_id" type="BIGINT"/>
                <column name="integration_id" type="BIGINT"/>
                <column name="type" type="VARCHAR(50)"/>
                <column name="parent_id" type="BIGINT">
                    <constraints nullable="true"/>
                </column>
                <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
                <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
                <column name="created_by" type="BIGINT"/>
                <column name="updated_by" type="BIGINT"/>
            </createTable>

            <addForeignKeyConstraint baseColumnNames="parent_id"
                                     baseTableName="fields_mapping" baseTableSchemaName="customer_integration"
                                     constraintName="fk_fields_mapping_parent_id"
                                     referencedColumnNames="id"
                                     onDelete="SET NULL"
                                     referencedTableName="fields_mapping"
                                     referencedTableSchemaName="customer_integration"/>
        </rollback>
    </changeSet>

    <changeSet id="20250717000000-2" author="system" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="field_mapping_configuration" schemaName="customer_integration"/>
        </preConditions>
        <comment>Drop field_mapping_configuration table as it's replaced by field-mapping-service</comment>

        <dropTable tableName="field_mapping_configuration" schemaName="customer_integration"/>
        <rollback>
            <createTable tableName="field_mapping_configuration" schemaName="customer_integration">
                <column name="id" type="BIGINT" autoIncrement="true">
                    <constraints nullable="false" primaryKey="true" primaryKeyName="field_mapping_configuration_pkey" />
                </column>
                <column name="key" type="VARCHAR(255)" />
                <column name="value" type="VARCHAR(255)" />
                <column name="type" type="VARCHAR(255)" />
                <column name="is_deleted" type="boolean" defaultValueBoolean="false"/>
                <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
                <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
                <column name="created_by" type="BIGINT"/>
                <column name="updated_by" type="BIGINT"/>
            </createTable>
        </rollback>
    </changeSet>

</databaseChangeLog>
