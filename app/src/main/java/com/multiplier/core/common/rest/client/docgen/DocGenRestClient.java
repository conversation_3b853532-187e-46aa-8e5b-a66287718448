package com.multiplier.core.common.rest.client.docgen;

import com.multiplier.core.common.rest.client.docgen.model.*;
import com.multiplier.core.common.rest.client.docgen.model.folder.CreateFolderRequest;
import com.multiplier.core.common.rest.client.docgen.model.folder.FolderResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocGenRestClient {

    private final DocGenClient docGenClient;
    private final PlatformEndpointConfiguration endpointConfig;

    private DocumentResponse mapInternalPublicURL(final DocumentResponse response) {
        try {
            val publicEndpoint = endpointConfig.publicEndPoint();
            val internalEndpoint = endpointConfig.internalEndpoint();

            val internalDownloadURL = String.format("%s/documents/%s/download", internalEndpoint, response.id());
            response.internalDownloadURL(new URL(internalDownloadURL));

            val viewURL = response.viewURL();
            if (viewURL == null) {
                return response;
            }
            if (internalEndpoint.contains(viewURL.getHost()) && !publicEndpoint.isBlank()) {
                response.viewURL(UriComponentsBuilder.fromUriString(publicEndpoint)
                        .replacePath(viewURL.getPath())
                        .replaceQuery(viewURL.getQuery())
                        .build().toUri().toURL());
            }
        } catch (MalformedURLException ex) {
            log.error("Miss formed internalDownloadURL", ex);
        }
        return response;
    }

    private List<DocumentResponse> mapInternalPublicURL(final List<DocumentResponse> response) {

        if (response == null) {
            return List.of();
        }

        return response.stream().map(this::mapInternalPublicURL).collect(Collectors.toList());
    }

    public FolderResponse createFolder(final CreateFolderRequest request) {
        return docGenClient.createFolder(request);
    }

    public DocumentResponse getDocument(final long id) {
        return mapInternalPublicURL(docGenClient.getDocument(id));
    }

    public List<DocumentResponse> getDocuments(final Set<Long> ids) {
        return mapInternalPublicURL(docGenClient.getDocuments(ids));
    }

    public List<DocumentResponse> getAllDocumentVersions(final long id) {
        return mapInternalPublicURL(docGenClient.getAllDocumentVersions(id));
    }

    public DocumentResponse getDocumentVersion(final long id, final long versionId) {
        return mapInternalPublicURL(docGenClient.getDocumentVersion(id, versionId));
    }

    public DocumentResponse createDocument(final CreateDocumentRequest request) {
        return mapInternalPublicURL(docGenClient.createDocument(request));
    }

    public <T> DocumentResponse generateDocument(final long id, final GenerateDocumentRequest<T> request) {
        return mapInternalPublicURL(docGenClient.generateDocument(id, request));
    }

    public <T> DocumentResponse freezeDocument(final long id, final GenerateDocumentRequest<T> request) {
        return mapInternalPublicURL(docGenClient.freezeDocument(id, request));
    }

    public DocumentResponse replaceDocument(final long id, final MultipartFile request) {
        return mapInternalPublicURL(docGenClient.replaceDocument(id, request));
    }

    public DocumentResponse sendDocument(final long id) {
        return mapInternalPublicURL(docGenClient.sendDocument(id));
    }

    public DocumentShareResponse shareDocument(final long id, final DocumentShareRequest request) {
        return docGenClient.shareDocument(id, request);
    }

    public TemplateDetailResponse getTemplateDetail(final long documentId) {
        return docGenClient.getDocumentTemplateDetail(documentId);
    }

}
