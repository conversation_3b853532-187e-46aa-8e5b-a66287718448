package com.multiplier.core.common.rest.client.docgen;

import lombok.Getter;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

@Getter
@Accessors(fluent = true)
@Configuration
public class PlatformEndpointConfiguration {

    private final String publicEndPoint;
    private final String internalEndpoint;

    PlatformEndpointConfiguration(@Value("${platform.docgen.baseurl}") final String baseUrl,
            @Value("${platform.docgen.public-baseurl}") final String publicBaseUrl) {
        internalEndpoint = baseUrl + "/v2";
        publicEndPoint = StringUtils.hasText(publicBaseUrl) ? publicBaseUrl + "/v2" : null;
    }

}
