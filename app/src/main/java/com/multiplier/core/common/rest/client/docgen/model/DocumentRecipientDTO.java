package com.multiplier.core.common.rest.client.docgen.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(fluent = true, chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
public class DocumentRecipientDTO {
    private static final long serialVersionUID = 8296618315276381496L;

    private String firstName;
    private String lastName;
    private String email;
    private String recipientType;
    private boolean hasCompleted;
    private String role;
    private Integer signingOrder;
}
