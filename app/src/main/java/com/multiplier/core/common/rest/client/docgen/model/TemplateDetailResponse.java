package com.multiplier.core.common.rest.client.docgen.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(fluent = true, chain = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
public class TemplateDetailResponse {

    private Long id;
    private String country;
    private String countryStateCode;
    private String type;
    private String lang;
    private String externalId;
    private List<TemplateSignerDTO> signers;
}
