package com.multiplier.core.common.rest.client.docgen.extension;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.json.JSONObject;

import java.io.IOException;

public class JsonExtension {

    public static ObjectMapper getMapper() {

        return new ObjectMapper()
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .registerModule(new JavaTimeModule())
                .registerModule(new SimpleModule()
                        .addSerializer(LocalDateSerializer.INSTANCE)
                        .addSerializer(LocalDateTimeSerializer.INSTANCE)
                        .addSerializer(LocalTimeSerializer.INSTANCE)
                        .addSerializer(JSONObject.class, new JsonSerializer<JSONObject>() {
                            @Override
                            public void serialize(JSONObject value, JsonGenerator jgen, SerializerProvider provider) throws IOException {
                                jgen.writeRawValue(value.toString());
                            }
                }));
    }

    public static <T> String toJsonString(T source) throws JsonProcessingException {

        return getMapper().writeValueAsString(source);
    }

    public static <T> T fromJsonString(String source, Class<T> responseType) throws JsonProcessingException {

        return (responseType.isAssignableFrom(String.class)) ? (T) source : getMapper().readValue(source, responseType);
    }
}
