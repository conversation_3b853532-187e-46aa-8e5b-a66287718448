package com.multiplier.core.common.rest.client.docgen.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.multiplier.core.common.rest.client.docgen.enums.DocStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

// TODO: Check if we can replace this with a class from the com.multiplier.core.schema.common.DocGen package

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(fluent = true, chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
public class DocumentResponse {

    private Long id;
    private Long templateId;
    private String country;
    private String type;
    private String lang;
    private String filename;
    private String externalFileName;
    private DocStatus status;
    private String extension;
    private String contentType;
    private Integer size;
    private URL viewURL;
    private URL downloadURL;
    private URL externalURL;
    private URL internalDownloadURL;
    private URL externalDownloadURL;
    private boolean isFrozen = false;
    private Long version;
    private LocalDateTime versionedOn;
    private List<DocumentRecipientDTO> recipients = new ArrayList<>();
}
