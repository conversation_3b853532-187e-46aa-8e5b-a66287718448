package com.multiplier.core.common.rest.client.docgen.enums;

public enum TemplateType {

    /* Static */
    MEMBER_LEGAL_DOCUMENT,
    PAYROLL_REPORT,
    CONTRACT_FREELANCE_CUSTOM,
    CONTRACT_HR_MEMBER_CUSTOM,
    CONTRACT_BENEFIT_DOCUMENT,
    EX<PERSON>ENSE_RECEIPT,
    COMPANY_LOGO,
    PAYROLL_FREELANCER_INVOICE_CUSTOM,
    BENEFIT_DOCUMENT,
    CSM_DISPLAY_PICTURE,
    MEMBER_CHANGE_REQUEST_DOCUMENT,
    ADDITIONAL_PAYROLL_FORM,
    MEMBER_BANK_STATEMENT,
    CONTRACT_OFFBOARDING_DOCUMENT,

    /* PDF Monkey */
    CONTRACT_OFFER_LETTER,
    PAYROLL_SALARY_CALCULATION,
    PAYROLL_PAYSLIP,
    PAYROLL_FREELANCER_INVOICE,
    FREELANCER_REMITTANCE_ADVICE,
    CONTRACT_EMPLOYMENT_CERTIFICATE,
    FREELANCER_COMPANY_INVOICE,

    /* Panda Doc */
    CONTRACT_EOR,
    CONTRACT_EOR_FIXED,
    CONTRACT_FREELANCE,
    CONTRACT_FREELANCE_RECURRING,
    COMPANY_MSA,
    PERFORMANCE_REVIEW,
    SALARY_REVIEW,
    CONTRACT_ESOP,
    MEMBER_OFFBOARDING_DOCS,
    CONTRACT_ADDENDUM,
    COUNTRY_DOCUMENT_BASED_ON_TYPE
}