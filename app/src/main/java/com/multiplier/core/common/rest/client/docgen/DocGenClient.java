package com.multiplier.core.common.rest.client.docgen;

import com.multiplier.core.common.rest.client.docgen.extension.JsonExtension;
import com.multiplier.core.common.rest.client.docgen.model.CreateDocumentRequest;
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse;
import com.multiplier.core.common.rest.client.docgen.model.DocumentShareRequest;
import com.multiplier.core.common.rest.client.docgen.model.DocumentShareResponse;
import com.multiplier.core.common.rest.client.docgen.model.GenerateDocumentRequest;
import com.multiplier.core.common.rest.client.docgen.model.TemplateDetailResponse;
import com.multiplier.core.common.rest.client.docgen.model.folder.CreateFolderRequest;
import com.multiplier.core.common.rest.client.docgen.model.folder.FolderResponse;
import feign.codec.Decoder;
import feign.codec.Encoder;
import java.util.List;
import java.util.Set;
import lombok.val;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(value = "docgen-service",
        url = "${feign.client.config.docgen-service.url}",
        configuration = DocGenClient.FeignConfiguration.class)
public interface DocGenClient {

    @Configuration
    static class FeignConfiguration {

        @Bean
        public Decoder feignDecoder() {
            MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(JsonExtension.getMapper());
            HttpMessageConverters httpMessageConverters = new HttpMessageConverters(jacksonConverter);
            return new SpringDecoder(() -> httpMessageConverters);
        }

        @Bean
        public Encoder feignEncoder() {
            val jacksonConverter = new MappingJackson2HttpMessageConverter(JsonExtension.getMapper());
            val httpMessageConverters = new HttpMessageConverters(jacksonConverter);
            return new SpringEncoder(() -> httpMessageConverters);
        }

    }

    @GetMapping(value = "/documents/{id}")
    DocumentResponse getDocument(@PathVariable final long id);

    @GetMapping(value = "/documents")
    List<DocumentResponse> getDocuments(@RequestParam Set<Long> ids);

    @GetMapping(value = "/documents/{id}/versions")
    List<DocumentResponse> getAllDocumentVersions(@PathVariable final long id);

    @GetMapping(value = "/documents/{id}/versions/{versionId}")
    DocumentResponse getDocumentVersion(@PathVariable final long id, @PathVariable final long versionId);

    @GetMapping("/documents/{id}/template-detail")
    TemplateDetailResponse getDocumentTemplateDetail(@PathVariable long id);

    @PostMapping("/documents")
    DocumentResponse createDocument(@RequestBody final CreateDocumentRequest request);

    @PostMapping("/documents/{id}")
    DocumentResponse generateDocument(@PathVariable final long id, @RequestBody final GenerateDocumentRequest<?> request);

    @PutMapping(value = "/documents/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    DocumentResponse replaceDocument(@PathVariable final long id, @RequestPart final MultipartFile file);

    @PutMapping("/documents/{id}/freeze")
    DocumentResponse freezeDocument(@PathVariable final long id, @RequestBody final GenerateDocumentRequest<?> request);

    @PostMapping("/documents/{id}/send")
    DocumentResponse sendDocument(@PathVariable final long id);

    @PostMapping("/documents/{id}/share")
    DocumentShareResponse shareDocument(@PathVariable final long id, @RequestBody final DocumentShareRequest request);

    @GetMapping("/documents/{id}/download")
    byte[] downloadDocument(@PathVariable final long id);

    @PostMapping("/folders")
    FolderResponse createFolder(@RequestBody final CreateFolderRequest request);

}
