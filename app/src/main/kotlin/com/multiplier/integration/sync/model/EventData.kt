package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.multiplier.integration.adapter.model.knit.Compensation as EmployeeCompensation

@JsonIgnoreProperties(ignoreUnknown = true)
data class EventData(
    val profile: EmployeeProfile?,
    val contactInfo: EmployeeContactInfo?,
    val orgStructure: EmployeeOrgStructure?,
    val locations: EmployeeLocations?,
    val bankAccounts: List<BankAccount>?,
    val rawValues: EmployeeRawValues?,
    val compensation: EmployeeCompensation?,
    val leaveRequests: List<EmployeeLeaveRequest>? = null,
    val customFields: CustomFields?,
    var integrationId: String?,
    val employeeIdentificationData: List<EmployeeIdentificationData>? = null,
    val dependents: List<EmployeeDependents>? = null,
    val w4Data: EmployeeW4Details? = null,
    val isRecordMissing: Boolean?,
    val employeeId: String?,
) {
    override fun toString(): String {
        return "EventData(profile=$profile, contactInfo=$contactInfo, employeeLeaveRequests = $leaveRequests, orgStructure=$orgStructure, locations=$locations, bankAccounts=$bankAccounts, rawValues=$rawValues, customFields=$customFields)"
    }
}
