package com.multiplier.integration.rest

import com.multiplier.integration.service.TestDataCreationService
import mu.KotlinLogging
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/initiate-test")
class TestInitiationController(
    private val testDataCreationService: TestDataCreationService,
) {

    private val log = KotlinLogging.logger {}

    @PostMapping("/eor-sync")
    fun createEmployeesForEorSync(
        @RequestBody request: EmployeeSyncRequest
    ): List<Long> {
        return testDataCreationService.createEmployeesForEorSync(
            request.companyId,
            request.legalEntityId,
            request.countryCode,
            request.numberOfEmployees
        )
    }

    @PostMapping("/gp-sync")
    fun createEmployeesForGpSync(
        @RequestBody request: EmployeeSyncRequest
    ): String {
        return testDataCreationService.createEmployeesForGpSync(
            request.companyId,
            request.legalEntityId,
            request.countryCode,
            request.numberOfEmployees
        )
    }
}

data class EmployeeSyncRequest(
    val companyId: Long,
    val legalEntityId: Long,
    val countryCode: String,
    val numberOfEmployees: Int
)

enum class Command {
    UPDATE_BASIC_DATA
}
