package com.multiplier.integration.rest.model

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

/**
 * Request model for SFTP webhook notifications.
 * This model captures the information about a new file upload to the SFTP server.
 *
 * @property fileURI The URI of the uploaded file
 * @property bucket The bucket or container where the file is stored
 * @property uploadedUser The user who uploaded the file
 */
data class SFTPWebhookRequest(
    @field:NotBlank(message = "File URI is required")
    @JsonProperty("fileURI")
    val fileURI: String,

    @field:NotBlank(message = "Bucket is required")
    val bucket: String,

    @field:NotBlank(message = "Uploaded user is required")
    @JsonProperty("uploadedUser")
    val uploadedUser: String
)
