package com.multiplier.integration.rest

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.multiplier.common.exception.MplSystemException
import com.multiplier.integration.Constants
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InboundDataExtractionService.CountryExtractor.Companion.extractCountry
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha3CountryCode
import jakarta.servlet.http.HttpServletRequest
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.*
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

@RestController
@RequestMapping("/sync")
class SyncEventController(
    private val syncService: SyncService,
    private val hmacVerifier: HmacSignatureVerifier,
    private val dataMapper: DataMapper,
    private val featureFlagService: FeatureFlagService,
) {

    private val log = KotlinLogging.logger {}

    @PostMapping("/consume-sync-event")
    fun consumeSyncEvent(@RequestBody request: JsonNode, httpServletRequest: HttpServletRequest): String {
        log.info("Incoming sync request " + request)

        validateRequest(request, httpServletRequest)

        val integrationId = httpServletRequest.getHeader("X-Knit-Integration-Id")
            ?: throw InvalidRequestException("Invalid request")

        if (!isSyncEventDataFilterValid(request, integrationId)) {
            return "OK"
        }

        // bypassing additional checks if we are testing the webhook endpoints and add it to Knit
        if (Constants.KNIT_TEST_INTEGRATION_ID.equals(integrationId)) {
            return "OK"
        } else {
            val eventType = request.get("eventType")?.asText()
            if (EventType.fromString(eventType) == EventType.SYNC_HEARTBEAT) {
                return "Ok"
            }
            try {
                syncService.handleIncomingEvent(request, integrationId)
            } catch (e: Exception) {
                log.error("Exception while handling request from Knit", e)
                return "Ok"
            }
        }
        return "OK"
    }

    private fun validateRequest(request: JsonNode, httpServletRequest: HttpServletRequest) {
        val payload = request.toString()
        val signature = httpServletRequest.getHeader("X-Knit-Signature")

        val isValid = hmacVerifier.verify(payload = payload, signature = signature)

        if (!isValid) {
            throw InvalidRequestException("Invalid request")
        }
    }

    private fun isSyncEventDataFilterValid(request: JsonNode, integrationId: String): Boolean {
        val event = dataMapper.map(request)
        log.info { "Fetching multiplier knit integration id: ${featureFlagService.getStringValue(FeatureFlag.MTM_KNIT_INTEGRATION_ID)}" }
        if (!featureFlagService.isMtmIntegration(integrationId, event.profile?.workEmail)) {
            return true
        }

        val country =
            extractCountry(DataMapper.objectMapper.readValue(DataMapper.objectMapper.writeValueAsString(event)))
        if (country == null) {
            log.info("Skipping sync for Multiplier Employee ${event.profile?.employeeNumber} because country is null")
            return false
        }
        val isCountrySupported = featureFlagService.getStringValue(FeatureFlag.MTM_SUPPORTED_COUNTRIES)
            .split(",")
            .contains(mapToAlpha3CountryCode(country))

        val isDepartmentSupported = featureFlagService.getStringValue(FeatureFlag.MTM_SUPPORTED_DEPARTMENTS)
            .split(",")
            .map { it.trim() }
            .contains(event.orgStructure?.department?.trim() ?: "")

        if (!isCountrySupported) {
            log.info("Skipping sync for Multiplier Employee ${event.profile?.employeeNumber} because country is not whitelisted ($country)")
            return false
        }
        if (!isDepartmentSupported) {
            log.info("Skipping sync for Multiplier Employee ${event.profile?.employeeNumber} because department is not whitelisted (${event.orgStructure?.department})")
            return false
        }

        log.warn("Removing compensation data for Multiplier Employee ${event.profile?.employeeNumber}")
        (request.get("eventData") as ObjectNode).replace(
            "compensation",
            DataMapper.objectMapper.createObjectNode().apply {
                set<ObjectNode>("fixed", DataMapper.objectMapper.createArrayNode().apply {
                    add(DataMapper.objectMapper.createObjectNode().apply {
                        put("type", "SALARY")
                        put("amount", 1)
                        put("planId", null as String?)
                        put("endDate", null as String?)
                        put("currency", "USD")
                        put("frequency", null as String?)
                        put("payPeriod", "MONTHLY")
                        put("startDate", null as String?)
                        put("percentage", null as String?)
                    })
                })
            })

        return true
    }

//    @ResponseStatus(HttpStatus.FORBIDDEN)
//    @ExceptionHandler(InvalidRequestException::class)
//    fun handleInvalidRequestException(ex: InvalidRequestException): ErrorResponse {
//        return ErrorResponse(ex.message ?: "Invalid request")
//    }
}

@ResponseStatus(HttpStatus.FORBIDDEN)
class InvalidRequestException(message: String) : MplSystemException(CustomerErrorCode.INVALID_REQUEST, message)

data class ErrorResponse(val message: String)

@Component
class HmacSignatureVerifier(
    @Value("\${platform.knit.api-key}") val knitApiKey: String,
) {
    fun verify(payload: String, signature: String): Boolean {
        val hMacSHA256 = Mac.getInstance("HmacSHA256")
        val secretKey = SecretKeySpec(knitApiKey.toByteArray(Charsets.UTF_8), "HmacSHA256")
        hMacSHA256.init(secretKey)

        val hmacSha256Bytes = hMacSHA256.doFinal(payload.toByteArray(Charsets.UTF_8))

        val encoded = Base64.getUrlEncoder().withoutPadding().encodeToString(hmacSha256Bytes)

        return encoded == signature
    }
}