package com.multiplier.integration.rest

import com.multiplier.integration.adapter.SFTPWebhookAdapter
import com.multiplier.integration.rest.model.SFTPWebhookRequest
import com.multiplier.integration.service.IntegrationOrchestrator
import jakarta.validation.Valid
import mu.KotlinLogging
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Controller for handling SFTP webhook notifications.
 * This controller receives notifications about new file uploads to the SFTP server
 * and triggers the data processing workflow.
 */
@RestController
@RequestMapping(RestEndpoint.WEBHOOK_SFTP_V1)
class SFTPWebhookController(
    private val integrationOrchestrator: IntegrationOrchestrator,
    private val sftpWebhookAdapter: SFTPWebhookAdapter
) {
    private val log = KotlinLogging.logger {}

    /**
     * Endpoint for receiving file upload notifications.
     *
     * @param request The webhook request containing information about the uploaded file
     * @return ResponseEntity with no content and HTTP status 204 (No Content)
     */
    @PostMapping("/file-upload")
    fun handleFileUpload(@Valid @RequestBody request: SFTPWebhookRequest): ResponseEntity<Void> {
        log.info { "Received SFTP file upload notification: $request" }

        // Convert the webhook request to an integration input using the adapter
        val integrationInput = sftpWebhookAdapter.toIntegrationInput(request)
        log.info { "Converted to IntegrationInput: $integrationInput" }

        // Process the file using the integration orchestrator
        integrationOrchestrator.handleDataIngestion(integrationInput)
        log.info { "Successfully processed SFTP file upload: ${request.fileURI}" }

        // Return HTTP 204 No Content
        return ResponseEntity.noContent().build()
    }
}
