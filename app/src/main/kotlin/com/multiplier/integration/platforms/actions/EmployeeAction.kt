package com.multiplier.integration.platforms.actions

import com.multiplier.integration.adapter.api.TEMP_DATA_URL_PREFIX
import mu.KotlinLogging
import org.apache.logging.log4j.util.Strings
import java.io.ByteArrayOutputStream
import java.net.URI
import java.net.URL
import java.util.*

private val log = KotlinLogging.logger {}

interface EmployeeAction {
    fun downloadFileAndEncodeBase64(fileUrl: String): String? =
        when {
            fileUrl.startsWith(TEMP_DATA_URL_PREFIX, ignoreCase = true) -> {
                extractBase64FromDataUrl(fileUrl)
            }
            else -> {
                // add fallback to download file from insecure url as doc-gen is a bit unstable
                downloadFileAndEncodeBase64(toSecureUrl(fileUrl))
                    ?: downloadFileAndEncodeBase64(toInsecureUrl(fileUrl))
            }
        }

    private fun downloadFileAndEncodeBase64(url: URL): String? = runCatching {
        url.openStream().use { inputStream ->
            ByteArrayOutputStream().use { buffer ->
                inputStream.copyTo(buffer)
                val result = Base64.getEncoder().encodeToString(buffer.toByteArray())
                if (Strings.isNotEmpty(result))
                    result
                else
                    null
            }
        }
    }.getOrElse { e ->
        log.error("Error while downloading from URL: $url", e)
        null
    }

    private fun toSecureUrl(fileUrl: String): URL =
        URI.create(fileUrl.replace("http://", "https://", ignoreCase = true)).toURL()

    private fun toInsecureUrl(fileUrl: String): URL =
        URI.create(fileUrl.replace("https://", "http://", ignoreCase = true)).toURL()

    private fun extractBase64FromDataUrl(dataUrl: String): String {
        // Parse data URL format: http://base64-data/<data>
        return dataUrl.substringAfter(TEMP_DATA_URL_PREFIX)
    }
}
