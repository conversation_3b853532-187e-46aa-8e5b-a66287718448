package com.multiplier.integration.platforms

enum class Platform(val platformName: String) {
    BAMBOOHR("BambooHR"),
    PERSONIO("Personio"),
    HIBOB("Hibob"),
    SUCCESSFACTORS("SuccessFactors"),
    ORACLE_HCM("Oracle HCM"),
    PAYCOR("Paycor"),
    DARWINBOX("Darwinbox"),
    PAYLOCITY("Paylocity"),
    ZOHO_PEOPLE("Zoho People"),
    GREYTHR("GreytHR"),
    FRESHTEAM("Freshteam"),
    NAMELY("Namely"),
    HUMAANS("Humaans"),
    SAGE("Sage"),
    PAYCHEX("Paychex"),
    TRINET("TriNet"),
    KEKA_HR("Keka HR"),
    QUICKBOOKS_ONLINE("Quickbooks Online"),
    XERO("Xero"),
    NETSUITE("NetSuite"),
    UNKNOWN("Unknown");

    companion object {
        fun fromString(platformName: String): Platform {
            return Platform.entries.find { it.platformName.equals(platformName, ignoreCase = true) } ?: UNKNOWN
        }
    }
}