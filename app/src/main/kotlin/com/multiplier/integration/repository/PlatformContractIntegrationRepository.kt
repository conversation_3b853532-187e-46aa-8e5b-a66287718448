package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional


@Repository
interface PlatformContractIntegrationRepository :
        JpaRepository<JpaPlatformContractIntegration, Long> {
    fun findByContractId(contractId: Long): List<JpaPlatformContractIntegration>

    fun findFirstByContractIdOrderByCreatedOnDesc(contractId: Long): JpaPlatformContractIntegration?

    fun findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(
        contractId: Long?,
        platformId: Long?
    ): JpaPlatformContractIntegration?

    fun findFirstByPlatformEmployeeIdAndPlatformIdAndProviderIdAndIntegrationId(
        platformEmployeeId: String,
        platformId: Long,
        providerId: Long,
        integrationId: Long
    ): JpaPlatformContractIntegration?

    fun findFirstByContractIdAndProviderIdAndPlatformId(
        contractId: Long,
        providerId: Long?,
        platformId: Long?
    ): JpaPlatformContractIntegration?

    @Modifying
    @Transactional
    @Query("DELETE FROM JpaPlatformContractIntegration pci WHERE pci.platformEmployeeId IN :platformEmployeeIds AND pci.platformId = :platformId")
    fun deleteByPlatformEmployeeIdsAndPlatformId(
        @Param("platformEmployeeIds") platformEmployeeIds: List<String>,
        @Param("platformId") platformId: Long
    ): Int

    fun findByIntegrationId(@Param("integrationId") integrationId: Long): List<JpaPlatformContractIntegration>?

    fun findAllByContractIdIn(contractIds: Set<Long>): List<JpaPlatformContractIntegration>

    @Query("SELECT DISTINCT pci.contractId FROM JpaPlatformContractIntegration pci WHERE pci.integrationId IS NULL")
    fun findDistinctContractIdByIntegrationIdIsNull(): Set<Long>
}
