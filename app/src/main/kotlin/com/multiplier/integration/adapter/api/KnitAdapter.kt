package com.multiplier.integration.adapter.api

import UpdateEmployeeResponse
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.adapter.api.resources.knit.AddCustomFieldMappingRequest
import com.multiplier.integration.adapter.api.resources.knit.AddCustomFieldMappingResponse
import com.multiplier.integration.adapter.api.resources.knit.ApiError
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.DepartmentsListResponse
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.GetAllFieldsResponse
import com.multiplier.integration.adapter.api.resources.knit.GetCompensationPlanResponse
import com.multiplier.integration.adapter.api.resources.knit.GetFieldValuesResponse
import com.multiplier.integration.adapter.api.resources.knit.GetLeaveTypesResponse
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.adapter.api.resources.knit.GetTerminationReasonResponse
import com.multiplier.integration.adapter.api.resources.knit.KnitIntegrations
import com.multiplier.integration.adapter.api.resources.knit.LeaveBalanceResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequest
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.TerminateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.TerminateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationResponse
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.ApproveTimeOffRequest
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooAddTimeOffResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooApproveTimeOffResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooPassthroughRequest
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooTimeOffRequest
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooTimeOffResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Category
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Employee
import com.multiplier.integration.adapter.api.resources.knit.bamboo.GetDocumentCategoriesResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.GetEmployeeDirectoryResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.GetWorkLocationsResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.ListData
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Lists
import com.multiplier.integration.adapter.api.resources.knit.bamboo.OptionData
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Options
import com.multiplier.integration.adapter.api.resources.knit.bamboo.PassthroughResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.WorkLocationResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.WorkLocationResponseBody
import com.multiplier.integration.adapter.api.resources.knit.hibob.GetWorksitesResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobBankAccount
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobBankAccountsResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobEmployee
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobEmployeeLookup
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobPassthroughRequest
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobPassthroughRequestPeopleSearchBody
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobPassthroughResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobSearchFilter
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobTimeOffRequest
import com.multiplier.integration.adapter.api.resources.knit.hibob.KekaGetAllLocationsResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.LookupEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.PassthroughRequest
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteData
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSiteResponseBodySerialized
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaResponse
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaTimeOffRequest
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaUpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.successfactors.GetSAPBusinessUnitsResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.GetSAPWorkLocationsResponse
import com.multiplier.integration.adapter.api.resources.knit.oracle.GetLegalEntitiesResponse
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategoriesResponse
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.SyncStartResponse
import com.multiplier.integration.adapter.model.createSyncRequest
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.config.HttpClientConfig.Companion.DEFAULT_REQUEST_TIMEOUT
import com.multiplier.integration.config.HttpClientConfig.Companion.FE_APPROPRIATE_REQUEST_TIMEOUT
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.KnitAuthResponse
import com.multiplier.integration.types.KnitAuthToken
import com.multiplier.integration.types.KnitAuthTokenError
import com.multiplier.integration.types.PlatformCategory
import employeeRequestFromBasicDetails
import employeeRequestFromContactDetails
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.http.content.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import mu.KotlinLogging
import org.json.JSONArray
import org.json.JSONObject
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.w3c.dom.Element
import org.w3c.dom.Node
import org.xml.sax.InputSource
import java.io.IOException
import java.io.StringReader
import javax.xml.parsers.DocumentBuilderFactory

interface KnitAdapter {

    fun getPositionsDetails(
        companyId: Long,
        platformId: Long,
    ): GetPositionDetailResponse?

    fun createEmployeeRecord(
        companyId: Long,
        platformId: Long,
        employeeData: CreateEmployeeRequest,
    ): CreateEmployeeResponse

    fun createDocument(
        companyId: Long,
        platformId: Long,
        documentData: CreateDocumentRequest,
    ): CreateDocumentResponse

    fun updateEmployeeDetails(
        companyId: Long,
        integrationId: Long,
        platformId: Long,
        employeeData: UpdateEmployeeDetailsRequest,
    ): UpdateEmployeeDetailsResponse

    fun updateCompensation(
        companyId: Long,
        platformId: Long,
        compensationData: UpdateCompensationRequest,
    ): UpdateCompensationResponse

    fun getWorksitesResponse(
        companyId: Long,
        platformId: Long,
    ): GetWorksitesResponse

    fun getEmployeeLeaveBalance(
        integrationId: String,
        employeeId: String,
    ): LeaveBalanceResponse

    fun getBambooWorkLocations(
        companyId: Long,
        platformId: Long,
    ): GetWorkLocationsResponse

    fun getSuccessFactorsWorkLocations(
        companyId: Long,
        platformId: Long,
    ): GetSAPWorkLocationsResponse

    fun getSuccessFactorsBusinessUnits(
        companyId: Long,
        platformId: Long,
    ): GetSAPBusinessUnitsResponse

    fun getDocumentCategories(
        companyId: Long,
        platformId: Long,
        employeeId: String?,
    ): GetDocumentCategoriesResponse

    fun getEmployeeDirectory(
        companyId: Long,
        platformId: Long,
    ): GetEmployeeDirectoryResponse

    fun getDocCategories(
        companyId: Long,
        platformId: Long,
    ): DocumentCategoriesResponse

    fun getCompensationPlan(
        companyId: Long,
        platformId: Long,
    ): GetCompensationPlanResponse

    fun terminateEmployee(
        companyId: Long,
        platformId: Long,
        employeeData: TerminateEmployeeRequest,
    ): TerminateEmployeeResponse

    fun getTerminationReason(
        companyId: Long,
        platformId: Long,
    ): GetTerminationReasonResponse

    fun requestAuthToken(
        companyId: String,
        companyName: String,
        userEmail: String,
        userName: String,
        knitAppId: String,
        clearErrors: Boolean,
        category: String,
    ): KnitAuthResponse?

    fun updateEmployeeRecord(
        companyId: Long,
        platformId: Long,
        externalEmployeeId: String,
        basicDetails: BasicDetails,
    ): UpdateEmployeeResponse

    fun updateEmployeeContactDetails(
        companyId: Long,
        platformId: Long,
        externalEmployeeId: String,
        contactDetails: ContactDetails,
    ): UpdateEmployeeResponse

    fun startSync(
        integrationId: String,
        category: PlatformCategory,
    ): String?

    fun startSync(
        integrationId: String,
        category: PlatformCategory,
        isInitialSync: Boolean,
    ): String?

    fun updateExpenseReport(
        integrationId: String,
        reportId: String,
    ): Boolean

    fun getAllFields(
        companyId: Long,
        platformId: Long,
        platformName: String,
    ): GetAllFieldsResponse

    fun getFieldValues(
        companyId: Long,
        platformId: Long,
        platformName: String,
        fieldType: String,
    ): GetFieldValuesResponse

    fun addCustomFieldMapping(
        companyId: Long,
        platformId: Long,
        addCustomFieldMappingRequest: AddCustomFieldMappingRequest,
    ): AddCustomFieldMappingResponse

    fun getLeaveTypes(companyId: Long, platformId: Long): GetLeaveTypesResponse

    fun lookupHibobEmployeeByEmail(companyId: Long, platformId: Long, email: String): LookupEmployeeResponse
    fun addBambooHrTimeOffRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        request: BambooTimeOffRequest,
    ): BambooAddTimeOffResponse

    fun approveBambooHrTimeOffRequest(
        companyId: Long,
        platformId: Long,
        requestId: String,
        status: String,
        note: String,
    ): BambooApproveTimeOffResponse

    fun setTestOngoing(isTestOngoing: Boolean)
    fun updateKekaEmployeeDetailsPassthrough(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: KekaUpdateEmployeeDetailsRequest,
    ): KekaResponse

    fun addKekaLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: KekaTimeOffRequest,
    ): KekaResponse

    fun getAllLocationsKekaHR(companyId: Long, platformId: Long): KekaGetAllLocationsResponse

    fun addHibobLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: HibobTimeOffRequest,
    ): HibobPassthroughResponse

    fun cancelHibobLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestId: String,
    ): HibobPassthroughResponse

    fun getEmployeeLeaveRequests(integrationId: String, employeeId: String, month: String): LeaveRequestResponse

    fun getLegalEntitiesOracleHCM(
        companyId: Long,
        platformId: Long
    ): GetLegalEntitiesResponse

    fun createLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: LeaveCreateRequest,
    ): LeaveCreateRequestResponse

    fun getDepartmentsList(
        companyId: Long,
        platformId: Long,
        platformName: String,
    ): DepartmentsListResponse

    fun getHibobEmployeeBankAccounts(
        companyId: Long,
        platformId: Long,
        employeeId: String,
    ): List<HibobBankAccount>
}

@Service
class DefaultKnitAdapter(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    private val syncRepository: SyncRepository,
    @Qualifier("knitHttpClient") private val httpClient: HttpClient,
    private var testOngoing: Boolean = false,
) : KnitAdapter {
    private val log = KotlinLogging.logger {}

    @Value("\${platform.knit.api-url}")
    private lateinit var knitBaseApiUrl: String
    private final val integrationIdHeader = "X-Knit-Integration-Id"
    val json = Json {
        ignoreUnknownKeys = true  // This will ignore any unknown keys in the JSON
        explicitNulls = false     // This will allow missing fields to be set to null
    }

    override fun getPositionsDetails(
        companyId: Long,
        platformId: Long,
    ): GetPositionDetailResponse? {
        val url = knitBaseApiUrl + "hr.employees.positions?originData=false"
        val integrationId = getCompanyToken(companyId, platformId)
        log.info { "Fetching position details for companyId=$companyId" }
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                log.info { "Successfully fetched positions from Knit for companyId: $companyId" }
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    json.decodeFromString<GetPositionDetailResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error fetching position details: $httpStatusCode, ${response.status.description}" + responseBody }
                    GetPositionDetailResponse(
                        success = false,
                        error = ApiError(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error fetching position details" }
            GetPositionDetailResponse(
                success = false,
                error = ApiError(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getCompensationPlan(
        companyId: Long,
        platformId: Long,
    ): GetCompensationPlanResponse {
        val url = knitBaseApiUrl + "hr.compensation.plans?originData=false"
        val integrationId = getCompanyToken(companyId, platformId)
        log.info { "Fetching compensation plans for companyId=$companyId" }
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                log.info { "Successfully fetched compensation plans from Knit for companyId: $companyId" }
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    json.decodeFromString<GetCompensationPlanResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error fetching compensation plans: $httpStatusCode, ${response.status.description}" + responseBody }
                    GetCompensationPlanResponse(
                        success = false,
                        error = ApiError(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error fetching compensation plans for companyId=$companyId" }
            GetCompensationPlanResponse(
                success = false,
                error = ApiError(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun createEmployeeRecord(
        companyId: Long,
        platformId: Long,
        employeeData: CreateEmployeeRequest,
    ): CreateEmployeeResponse {
        val url = knitBaseApiUrl + "hr.employee.create"
        log.info { "Creating employee record for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val body = TextContent(Json.encodeToString(employeeData), ContentType.Application.Json)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                val responseBody = response.bodyAsText()
                val httpStatusCode = response.status.value
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK || response.status == HttpStatusCode.Created) {
                    Json.decodeFromString<CreateEmployeeResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error creating employee record: $httpStatusCode, ${response.status.description} responseBody: $responseBody" }
                    CreateEmployeeResponse(
                        success = false,
                        errors = listOf(response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Error creating employee record" }
            CreateEmployeeResponse(
                success = false,
                errors = listOf(e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun createDocument(
        companyId: Long,
        platformId: Long,
        documentData: CreateDocumentRequest,
    ): CreateDocumentResponse {
        val url = knitBaseApiUrl + "hr.employees.document.upload"
        log.info { "Creating document record for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val body = TextContent(Json.encodeToString(documentData), ContentType.Application.Json)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                log.info { "### $response" }
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                if (response.status == HttpStatusCode.OK || response.status == HttpStatusCode.Created) {
                    Json.decodeFromString<CreateDocumentResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error creating document record: $httpStatusCode, ${response.status.description}" + responseBody }
                    CreateDocumentResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error creating document record" }
            CreateDocumentResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun updateCompensation(
        companyId: Long,
        platformId: Long,
        compensationData: UpdateCompensationRequest,
    ): UpdateCompensationResponse {
        val url = knitBaseApiUrl + "hr.employee.compensation.update"
        log.info { "Updating compensation for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val body =
            TextContent(DataMapper.objectMapper.writeValueAsString(compensationData), ContentType.Application.Json)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    json.decodeFromString<UpdateCompensationResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error updating compensation: $httpStatusCode, ${response.status.description}, entire response: $responseBody" }
                    UpdateCompensationResponse(
                        success = false,
                        error = UpdateCompensationErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error updating compensation" }
            UpdateCompensationResponse(
                success = false,
                error = UpdateCompensationErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun updateEmployeeDetails(
        companyId: Long,
        platformIntegrationId: Long,
        platformId: Long,
        employeeData: UpdateEmployeeDetailsRequest,
    ): UpdateEmployeeDetailsResponse {
        val url = knitBaseApiUrl + "hr.employee.update"
        log.info { "Updating employee details for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val body = TextContent(Json.encodeToString(employeeData), ContentType.Application.Json)
        System.out.println("BODY" + body)
        System.out.println("PLOAD" + Json.encodeToString(employeeData))
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    if (!testOngoing) {
                        updateInternalEmployeeData(platformIntegrationId, employeeData)
                    }
                    Json.decodeFromString<UpdateEmployeeDetailsResponse>(responseBody)
                        .copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error updating employee details: $httpStatusCode, ${response.status.description}" + responseBody }
                    UpdateEmployeeDetailsResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error updating employee details" }
            UpdateEmployeeDetailsResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getDocCategories(
        companyId: Long,
        platformId: Long,
    ): DocumentCategoriesResponse {
        val url = knitBaseApiUrl + "hr.documents.categories?originData=false"
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    json.decodeFromString<DocumentCategoriesResponse>(responseBody).also {
                        it.responseCode = httpStatusCode // Set the HTTP status code in the response object
                    }
                } else {
                    // Handle HTTP-level errors
                    log.error { "Error fetching document categories: $httpStatusCode, ${response.status.description}" + responseBody }
                    DocumentCategoriesResponse(
                        success = false,
                        data = null, // No categories data available
                        error = com.multiplier.integration.adapter.api.resources.workday.ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            // Handle network errors
            log.error(e) { "Error fetching document categories" }
            DocumentCategoriesResponse(
                success = false,
                data = null, // No categories data available
                error = com.multiplier.integration.adapter.api.resources.workday.ErrorResponse(
                    msg = e.localizedMessage
                ),
                responseCode = null
            )
        }
    }

    override fun getWorksitesResponse(
        companyId: Long,
        platformId: Long,
    ): GetWorksitesResponse {
        val url = knitBaseApiUrl + "passthrough"
        log.info { "Fetching work sites for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "company/named-lists/site"
        )
        val body = TextContent(Json.encodeToString(passthroughRequestBody), ContentType.Application.Json)
        return try {
            runBlocking {
                // Make the network call
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    // Parse the successful response
                    val rawResponse = json.decodeFromString<GetWorksitesResponse>(responseBody)
                    val responseBodySerialized = rawResponse.data?.responseJson?.body  // WorkSiteData
                    val responseBodyString = rawResponse.data?.response?.body  // String
                    if (responseBodySerialized != null) {
                        // The body contains deserialized WorkSiteData, proceed as normal
                        rawResponse.copy(responseCode = httpStatusCode)
                    } else if (responseBodyString != null) {
                        // The body contains a serialized JSON string, attempt to deserialize it
                        try {
                            val workSiteData = Json.decodeFromString<WorkSiteData>(responseBodyString)
                            GetWorksitesResponse(
                                success = rawResponse.success,
                                data = WorkSiteResponse(responseJson = WorkSiteResponseBodySerialized(workSiteData)), // Place the deserialized WorkSiteData here
                                responseCode = httpStatusCode
                            )
                        } catch (e: SerializationException) {
                            log.error { "Error deserializing work site data: ${e.localizedMessage}" }
                            // Handle the deserialization error appropriately. Maybe return an error response.
                            GetWorksitesResponse(
                                error = ErrorResponse(msg = "Error deserializing response: ${e.localizedMessage}"),
                                responseCode = httpStatusCode
                            )
                        }
                    } else {
                        // Neither a String nor WorkSiteData, handle this scenario (maybe return an error response or throw an exception)
                        log.error { "Response body was neither a serialized string nor WorkSiteData" }
                        GetWorksitesResponse(
                            error = ErrorResponse(msg = "Invalid response format"),
                            responseCode = httpStatusCode
                        )
                    }
                } else {
                    // Handle the error case
                    log.error { "Error fetching work sites: $httpStatusCode, ${response.status.description}" + responseBody }
                    GetWorksitesResponse(
                        error = ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: Exception) { // Catching all exceptions, as JSON parsing might throw various types
            // Handle the exception
            log.error(e) { "Error fetching work sites" }
            GetWorksitesResponse(
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getAllLocationsKekaHR(
        companyId: Long,
        platformId: Long,
    ): KekaGetAllLocationsResponse {
        val url = knitBaseApiUrl + "passthrough"
        log.info { "Fetching work sites for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "hris/locations"
        )
        val body = TextContent(Json.encodeToString(passthroughRequestBody), ContentType.Application.Json)
        return try {
            runBlocking {
                // Make the network call
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    // Parse the successful response
                    val rawResp = json.decodeFromString<GetWorksitesResponse>(responseBody)
                    val resp = json.decodeFromString<KekaGetAllLocationsResponse>(rawResp.data!!.response!!.body!!)
                    resp.copy(responseCode = httpStatusCode)
                } else {
                    // Handle the error case
                    log.error { "Error fetching work sites: $httpStatusCode, ${response.status.description}" + responseBody }
                    KekaGetAllLocationsResponse(
                        error = ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: Exception) {
            // Handle the exception
            log.error(e) { "Error fetching work sites" }
            KekaGetAllLocationsResponse(
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }


    override fun getLegalEntitiesOracleHCM(
        companyId: Long,
        platformId: Long
    ): GetLegalEntitiesResponse {
        val url = knitBaseApiUrl + "passthrough"
        log.info { "Fetching legal entities for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "legalEmployersLov",
        )
        val body = TextContent(Json.encodeToString(passthroughRequestBody), ContentType.Application.Json)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }

                if (response.status == HttpStatusCode.OK) {
                    if (responseBody.isBlank()) {
                        GetLegalEntitiesResponse(
                            success = false,
                            error = ErrorResponse(msg = "Empty response received from API"),
                            responseCode = httpStatusCode
                        )
                    } else {
                        val rawResp = json.decodeFromString<GetLegalEntitiesResponse>(responseBody)
                        GetLegalEntitiesResponse(
                            success = true,
                            data = rawResp.data,
                            responseCode = httpStatusCode
                        )
                    }
                } else {
                    log.error { "Error fetching legal entities: $httpStatusCode, ${response.status.description}" }
                    GetLegalEntitiesResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Error fetching legal entities" }
            GetLegalEntitiesResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getEmployeeLeaveBalance(
        integrationId: String,
        employeeId: String,
    ): LeaveBalanceResponse {
        val url = knitBaseApiUrl + "hr.employees.leave.balance"
        log.info { "Fetching leave balance for integrationId=$integrationId and employeeId=$employeeId" }
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                log.info { "Successfully leave balance for integrationId=$integrationId and employeeId=$employeeId" }
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    Json.decodeFromString<LeaveBalanceResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error fetching leave balances: $httpStatusCode, ${response.status.description}" }
                    LeaveBalanceResponse(
                        success = false,
                        error = ApiError(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error fetching leave balance" }
            LeaveBalanceResponse(
                success = false,
                error = ApiError(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    override fun getEmployeeLeaveRequests(
        integrationId: String,
        employeeId: String,
        month: String,
    ): LeaveRequestResponse {
        val url = knitBaseApiUrl + "hr.employees.leave.requests?month=$month&employeeId=$employeeId"
        log.info { "Fetching leave requests for integrationId=$integrationId and employeeId=$employeeId" }
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                log.info { "Successfully leave requests for integrationId=$integrationId and employeeId=$employeeId" }
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "[getEmployeeLeaveRequests] Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    val json = Json {
                        ignoreUnknownKeys = true  // This will ignore any unknown keys in the JSON
                        explicitNulls = false     // This will allow missing fields to be set to null
                    }
                    json.decodeFromString<LeaveRequestResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error fetching leave balances: $httpStatusCode, ${response.status.description}" }
                    LeaveRequestResponse(
                        success = false,
                        error = ApiError(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error fetching leave balance" }
            LeaveRequestResponse(
                success = false,
                error = ApiError(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun createLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: LeaveCreateRequest
    ): LeaveCreateRequestResponse {
        log.info("Adding time off request for companyId: $companyId, platformId: $platformId, employeeId: $employeeId")
        val url = knitBaseApiUrl + "hr.employees.leave.create"
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, requestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info("Response body: $responseBody")
                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully added time off request")
                    Json.decodeFromString<LeaveCreateRequestResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error("Failed to add time off request. Status: ${response.status.description}")
                    LeaveCreateRequestResponse(
                        success = false,
                        error = ApiError(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }

            }
        } catch (e: Exception) {
            log.error("Exception occurred while adding time off request", e)
            LeaveCreateRequestResponse(
                success = false,
                error = ApiError(msg = "Error adding time off request: ${e.localizedMessage}"),
                responseCode = 400
            )
        }
    }

    override fun getBambooWorkLocations(
        companyId: Long,
        platformId: Long,
    ): GetWorkLocationsResponse {
        val url = knitBaseApiUrl + "passthrough"
        // Prepare the passthrough request body
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "meta/lists"
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                if (response.status == HttpStatusCode.OK) {
                    val xmlString = extractResponseStringFromPassthrough(responseBody)
                    val lists = parseWorkLocationsXml(xmlString)
                    GetWorkLocationsResponse(
                        success = true,
                        data = WorkLocationResponse(
                            response = WorkLocationResponseBody(xmlString),
                            parsedData = lists  // Include parsed XML data here
                        ),
                        responseCode = httpStatusCode
                    )
                } else {
                    GetWorkLocationsResponse(
                        success = false,
                        error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error fetching work locations: ${response.status.description}"),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            GetWorkLocationsResponse(
                success = false,
                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error fetching work locations: ${e.localizedMessage}"),
                responseCode = null
            )
        }
    }

    override fun getSuccessFactorsWorkLocations(
        companyId: Long,
        platformId: Long,
    ): GetSAPWorkLocationsResponse {
        val url = knitBaseApiUrl + "passthrough"
        // Prepare the passthrough request body
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "FOLocation"
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                if (response.status == HttpStatusCode.OK) {
                    val workLocationResponse = json.decodeFromString<GetSAPWorkLocationsResponse>(responseBody)
                    workLocationResponse.copy(responseCode = httpStatusCode)
                } else {
                    GetSAPWorkLocationsResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            GetSAPWorkLocationsResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getSuccessFactorsBusinessUnits(
        companyId: Long,
        platformId: Long,
    ): GetSAPBusinessUnitsResponse {
        val url = knitBaseApiUrl + "passthrough"
        // Prepare the passthrough request body
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "FOBusinessUnit"
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                if (response.status == HttpStatusCode.OK) {
                    val businessUnitsResponse = json.decodeFromString<GetSAPBusinessUnitsResponse>(responseBody)
                    businessUnitsResponse.copy(responseCode = httpStatusCode)
                } else {
                    GetSAPBusinessUnitsResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            GetSAPBusinessUnitsResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getDocumentCategories(
        companyId: Long,
        platformId: Long,
        employeeId: String?,
    ): GetDocumentCategoriesResponse {
        val url = knitBaseApiUrl + "passthrough"
        log.info { "Fetching document categories for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val path = if (employeeId != null) "employees/$employeeId/files/view/" else "files/view/"
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = path
        )
        val requestBody = TextContent(Json.encodeToString(passthroughRequestBody), ContentType.Application.Json)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, requestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    val rawResponse = json.decodeFromString<PassthroughResponse>(responseBody)
                    val responseBodyString = rawResponse.data?.response?.body
                    if (!responseBodyString.isNullOrBlank()) {
                        try {
                            val categories = parseXmlToCategories(responseBodyString)
                            val filterCategories = categories.filter { it.canUploadFiles }
                            GetDocumentCategoriesResponse(
                                success = true,
                                categories = filterCategories,
                                responseCode = httpStatusCode
                            )
                        } catch (e: Exception) {
                            log.error { "Error parsing XML: ${e.localizedMessage}" }
                            GetDocumentCategoriesResponse(
                                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error parsing XML: ${e.localizedMessage}"),
                                responseCode = httpStatusCode,
                                success = false
                            )
                        }
                    } else {
                        GetDocumentCategoriesResponse(
                            error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Invalid response format"),
                            responseCode = httpStatusCode,
                            success = false
                        )
                    }
                } else {
                    log.error { "Error fetching document categories: $httpStatusCode, ${response.status.description}" }
                    GetDocumentCategoriesResponse(
                        error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode,
                        success = false
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Error fetching document categories" }
            GetDocumentCategoriesResponse(
                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(
                    msg = e.localizedMessage
                ),
                responseCode = null,
                success = false
            )
        }
    }

    override fun getEmployeeDirectory(companyId: Long, platformId: Long): GetEmployeeDirectoryResponse {
        val url = knitBaseApiUrl + "passthrough"
        log.info { "Fetching employee directory for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val path = "employees/directory"
        val request = PassthroughRequest(
            method = "GET",
            path = path
        )
        return try {
            runBlocking {
                val response = httpPost(
                    url,
                    integrationId,
                    TextContent(Json.encodeToString(request), ContentType.Application.Json)
                )
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    val rawResponse = json.decodeFromString<PassthroughResponse>(responseBody)
                    val responseBodyString = rawResponse.data?.response?.body
                    try {
                        val employees = responseBodyString?.let { parseXmlToEmployee(it) }
                        GetEmployeeDirectoryResponse(
                            success = true,
                            employees = employees,
                            responseCode = httpStatusCode
                        )
                    } catch (e: Exception) {
                        log.error { "Error parsing XML: ${e.localizedMessage}" }
                        GetEmployeeDirectoryResponse(
                            error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error parsing XML: ${e.localizedMessage}"),
                            responseCode = httpStatusCode,
                            success = false
                        )
                    }
                } else {
                    log.error { "Error fetching employee directory: $httpStatusCode, ${response.status.description}" }
                    GetEmployeeDirectoryResponse(
                        error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = response.status.description),
                        responseCode = httpStatusCode,
                        success = false
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Error fetching employee directory" }
            GetEmployeeDirectoryResponse(
                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(
                    msg = e.localizedMessage
                ),
                responseCode = null,
                success = false
            )
        }
    }

    override fun terminateEmployee(
        companyId: Long,
        platformId: Long,
        employeeData: TerminateEmployeeRequest,
    ): TerminateEmployeeResponse {
        val url = knitBaseApiUrl + "hr.employee.terminate"
        log.info { "Terminate employee record for companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val body = TextContent(Json.encodeToString(employeeData), ContentType.Application.Json)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    Json.decodeFromString<TerminateEmployeeResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error terminate employee record: $httpStatusCode, ${response.status.description} responseBody: " + responseBody }
                    TerminateEmployeeResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error terminate employee record" }
            TerminateEmployeeResponse(
                success = false,
                error = ErrorResponse(e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun getTerminationReason(companyId: Long, platformId: Long): GetTerminationReasonResponse {
        val url = knitBaseApiUrl + "hr.termination.reasons?originData=false"
        val integrationId = getCompanyToken(companyId, platformId)
        log.info { "getTerminationReason for companyId=$companyId" }
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                log.info { "Successfully fetched termination reasons from Knit for companyId: $companyId" }
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    json.decodeFromString<GetTerminationReasonResponse>(responseBody)
                        .copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error fetching position details: $httpStatusCode, ${response.status.description}" + responseBody }
                    GetTerminationReasonResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Error fetching position details" }
            GetTerminationReasonResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage),
                responseCode = null
            )
        }
    }

    override fun requestAuthToken(
        companyId: String,
        companyName: String,
        userEmail: String,
        userName: String,
        knitAppId: String,
        clearErrors: Boolean,
        category: String,
    ): KnitAuthResponse? {
        return runBlocking {
            log.info {
                "Initiating request for auth token. companyId=$companyId, companyName=$companyName, " +
                        "knitAppId=$knitAppId"
            }
            val body = KnitIntegrations.CreateAuthToken.Request(
                originOrgId = companyId,
                originOrgName = companyName,
                originUserEmail = userEmail,
                originUserName = userName,
                filters = listOf(
                    KnitIntegrations.CreateAuthToken.Filter(
                        category = category,
                        apps = listOf(knitAppId)
                    )
                ),
                clearErrors = clearErrors
            )
            val response =
                httpClient.post(KnitIntegrations.CreateAuthToken()) {
                    setBody(body)
                }

            val responseBody: KnitIntegrations.CreateAuthToken.Response? = response.body()
            log.info { "Received response for companyId=$companyId: $responseBody" }
            val success = responseBody?.success ?: false
            val authToken = KnitAuthToken()
            val errorToken = KnitAuthTokenError()
            if (success) {
                val token = responseBody?.message?.token
                authToken.token = token
                log.info { "Auth token successfully retrieved for companyId =$companyId" }
            } else {
                val errorMsg = responseBody?.error?.message
                errorToken.msg = errorMsg
                log.warn { "Failed to retrieve auth token for companyId=$companyId. Error: $errorMsg" }
            }
            KnitAuthResponse(success, authToken, errorToken)
        }
    }

    override fun updateEmployeeRecord(
        companyId: Long,
        platformId: Long,
        externalEmployeeId: String,
        basicDetails: BasicDetails,
    ): UpdateEmployeeResponse {
        val logPrefix = "UpdateBasicDetails-$externalEmployeeId"
        log.info { "$logPrefix: Updating basic details for ${basicDetails.firstName} ${basicDetails.lastName}" }
        val requestBodyJson = DataMapper.objectMapper.writeValueAsString(employeeRequestFromBasicDetails(basicDetails))
        return performEmployeeUpdate(requestBodyJson, companyId, platformId, logPrefix)
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        platformId: Long,
        externalEmployeeId: String,
        contactDetails: ContactDetails,
    ): UpdateEmployeeResponse {
        val logPrefix = "UpdateContact-$externalEmployeeId"
        log.info { "$logPrefix: Updating contact details for externalEmployeeId $externalEmployeeId" }
        val requestBodyJson =
            DataMapper.objectMapper.writeValueAsString(employeeRequestFromContactDetails(contactDetails))
        return performEmployeeUpdate(requestBodyJson, companyId, platformId, logPrefix)
    }

    override fun startSync(integrationId: String, category: PlatformCategory): String? {
        val uniqueIdentifier = "SyncStart"
        log.info { "$uniqueIdentifier: Initiating sync start." }
        val url = knitBaseApiUrl + "sync.start"
        var isInitialSync = true
        val matchingSyncs = syncRepository.findByIntegrationId(integrationId)
        if (matchingSyncs.isNotEmpty()) {
            isInitialSync = false
        }
        val requestBodyJson = DataMapper.objectMapper.writeValueAsString(createSyncRequest(isInitialSync, category))
        return try {
            runBlocking {
                val httpResponse =
                    httpPost(url, integrationId, TextContent(requestBodyJson, ContentType.Application.Json))
                if (httpResponse.status != HttpStatusCode.OK) {
                    log.error("$uniqueIdentifier: Failed to start sync. Status: ${httpResponse.status}")
                    null
                } else {
                    val responseBodyString = runBlocking { httpResponse.body<String>() }
                    val response: SyncStartResponse = DataMapper.objectMapper.readValue(responseBodyString)
                    if (!response.success) {
                        log.error("$uniqueIdentifier: Unexpected error occurred while starting sync.")
                        null
                    } else {
                        log.info { "$uniqueIdentifier: Successful sync start. SyncJobId: ${response.data.syncJobId}" }
                        response.data.syncJobId
                    }
                }
            }
        } catch (e: Exception) {
            log.error("$uniqueIdentifier: Error parsing the response JSON: ${e.message}")
            return null
        }
    }

    override fun startSync(integrationId: String, category: PlatformCategory, isInitialSync: Boolean): String? {
        val uniqueIdentifier = "SyncStart"
        log.info { "$uniqueIdentifier: Initiating sync start." }
        val url = knitBaseApiUrl + "sync.start"

        val requestBodyJson = DataMapper.objectMapper.writeValueAsString(createSyncRequest(isInitialSync, category))
        return try {
            runBlocking {
                val httpResponse =
                    httpPost(url, integrationId, TextContent(requestBodyJson, ContentType.Application.Json))
                if (httpResponse.status != HttpStatusCode.OK) {
                    log.error("$uniqueIdentifier: Failed to start sync. Status: ${httpResponse.status}")
                    null
                } else {
                    val responseBodyString = runBlocking { httpResponse.body<String>() }
                    val response: SyncStartResponse = DataMapper.objectMapper.readValue(responseBodyString)
                    if (!response.success) {
                        log.error("$uniqueIdentifier: Unexpected error occurred while starting sync.")
                        null
                    } else {
                        log.info { "$uniqueIdentifier: Successful sync start. SyncJobId: ${response.data.syncJobId}" }
                        response.data.syncJobId
                    }
                }
            }
        } catch (e: Exception) {
            log.error("$uniqueIdentifier: Error parsing the response JSON: ${e.message}")
            return null
        }
    }

    override fun updateExpenseReport(integrationId: String, reportId: String): Boolean {
        log.info { "Calling update expense report for integrationId " + integrationId + "and reportId " + reportId }
        val url = knitBaseApiUrl + "expense.status.update"
        val updateExpenseReportRequest = UpdateExpenseReportRequest(
            reportStatus = "REIMBURSED",
            reportId = reportId
        )
        val requestBodyJson = DataMapper.objectMapper.writeValueAsString(updateExpenseReportRequest)
        return try {
            runBlocking {
                val httpResponse =
                    httpPost(url, integrationId, TextContent(requestBodyJson, ContentType.Application.Json))
                if (httpResponse.status != HttpStatusCode.OK) {
                    val bodyString = runBlocking { httpResponse.body<String>() }
                    val errorResponse: ReportStatusUpdateErrorResponse = DataMapper.objectMapper.readValue(bodyString)
                    log.error {
                        "Failed calling update expense report for integrationId " +
                                integrationId + "and reportId " + reportId + "response status: " + httpResponse.status + "message: " + errorResponse.error.msg
                    }
                    false
                } else {
                    val responseBodyString = runBlocking { httpResponse.body<String>() }
                    val response: ReportStatusUpdateSuccessResponse =
                        DataMapper.objectMapper.readValue(responseBodyString)
                    log.info(
                        "Response for update expense report for integrationId " + integrationId
                                + "and reportId " + reportId + " is " + response
                    )
                    true
                }
            }
        } catch (e: Exception) {
            log.error {
                "Error parsing update expense report for integrationId " +
                        integrationId + "and reportId " + reportId + "error reason: " + e.message
            }
            return false
        }
    }

    override fun getAllFields(companyId: Long, platformId: Long, platformName: String): GetAllFieldsResponse {
        log.info { "getAllFields for companyId=$companyId" }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpGet(
                    knitBaseApiUrl + "fields.list?appId=$platformName",
                    integrationId
                ) // client.newCall(request).execute()
                val responseBody = response.bodyAsText()
                log.info("Response getAllFields Body: $responseBody")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully fetched all fields from Knit for companyId: $companyId, platformId: $platformId")
                    json.decodeFromString<GetAllFieldsResponse>(responseBody).copy(responseCode = response.status.value)
                } else {
                    log.error { "Error fetching all fields: ${response.status.description}" }
                    GetAllFieldsResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = response.status.value
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Error fetching all fields from Knit" }
            GetAllFieldsResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage ?: "Unknown error"),
                responseCode = 500
            )
        }
    }

    override fun getFieldValues(
        companyId: Long,
        platformId: Long,
        platformName: String,
        fieldType: String,
    ): GetFieldValuesResponse {
        log.info { "getFieldValues for companyId=$companyId with fieldType=$fieldType" }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpGet(
                    knitBaseApiUrl + "hr.employees.field.values?fieldType=$fieldType",
                    integrationId,
                    FE_APPROPRIATE_REQUEST_TIMEOUT
                )
                val responseBody = response.bodyAsText()
                log.info("Response getFieldValues Body: $responseBody")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully fetched field values from Knit for companyId: $companyId, platformId: $platformId")
                    json.decodeFromString<GetFieldValuesResponse>(responseBody)
                        .copy(responseCode = response.status.value)
                } else {
                    log.warn { "Error fetching field values: ${response.status.description}" }
                    GetFieldValuesResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = response.status.value
                    )
                }
            }
        } catch (e: Exception) {
            log.warn(e) { "Error fetching field values from Knit" }
            GetFieldValuesResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage ?: "Unknown error"),
                responseCode = 500
            )
        }
    }

    override fun getDepartmentsList(
        companyId: Long,
        platformId: Long,
        platformName: String,
    ): DepartmentsListResponse {
        log.info { "getDepartmentsList for companyId=$companyId" }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpGet(
                    knitBaseApiUrl + "hr.departments.list",
                    integrationId,
                    FE_APPROPRIATE_REQUEST_TIMEOUT
                )
                val responseBody = response.bodyAsText()
                log.info("Response getDepartmentsList Body: $responseBody")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully fetched departments list from Knit for companyId: $companyId, platformId: $platformId")
                    json.decodeFromString<DepartmentsListResponse>(responseBody)
                        .copy(responseCode = response.status.value)
                } else {
                    log.warn { "Error fetching departments list: ${response.status.description}" }
                    DepartmentsListResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = response.status.value
                    )
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Error fetching departments list from Knit" }
            DepartmentsListResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage ?: "Unknown error"),
                responseCode = 500
            )
        }
    }

    override fun addCustomFieldMapping(
        companyId: Long,
        platformId: Long,
        addCustomFieldMappingRequest: AddCustomFieldMappingRequest,
    ): AddCustomFieldMappingResponse {
        log.info { "addCustomFieldMapping for companyId=$companyId with request=$addCustomFieldMappingRequest" }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(
                    knitBaseApiUrl + "field.map",
                    integrationId,
                    TextContent(json.encodeToString(addCustomFieldMappingRequest), ContentType.Application.Json)
                )
                val responseBody = response.bodyAsText()
                log.info("Response addCustomFieldMapping Body: $responseBody")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully add custom field mapping for companyId: $companyId, platformId: $platformId")
                    json.decodeFromString<AddCustomFieldMappingResponse>(responseBody)
                        .copy(responseCode = response.status.value)
                } else {
                    log.error { "Error adding custom field mapping: ${response.status.description}" }
                    AddCustomFieldMappingResponse(
                        success = false,
                        error = ErrorResponse(msg = response.status.description + responseBody),
                        responseCode = response.status.value
                    )
                }
            }
        } catch (e: Exception) {
            log.warn(e) { "Error fetching field values from Knit" }
            AddCustomFieldMappingResponse(
                success = false,
                error = ErrorResponse(msg = e.localizedMessage ?: "Unknown error"),
                responseCode = 500
            )
        }
    }

    override fun getLeaveTypes(companyId: Long, platformId: Long): GetLeaveTypesResponse {
        val url = knitBaseApiUrl + "hr.leave.types"
        log.info { "Fetching leave types for companyId=$companyId" }
        val integrationId = getCompanyToken(companyId, platformId)
        return try {
            runBlocking {
                val response = httpGet(url, integrationId)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    Json.decodeFromString<GetLeaveTypesResponse>(responseBody).copy(responseCode = httpStatusCode)
                } else {
                    log.error { "Error fetching leave types: $httpStatusCode, ${response.status.description} responseBody: " + responseBody }
                    GetLeaveTypesResponse(
                        success = false,
                        errors = response.status.description + responseBody,
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception fetching leave types" }
            GetLeaveTypesResponse(
                success = false,
                errors = e.localizedMessage,
                responseCode = null
            )
        }
    }

    override fun lookupHibobEmployeeByEmail(
        companyId: Long,
        platformId: Long,
        email: String,
    ): LookupEmployeeResponse {
        val url = knitBaseApiUrl + "passthrough"
        val body = HibobPassthroughRequestPeopleSearchBody(
            showInactive = true,
            filters = listOf(
                HibobSearchFilter(
                    fieldPath = "/root/email",
                    values = listOf(email),
                    operator = "equals"
                )
            )
        )
        val passthroughRequestBody = HibobPassthroughRequest(
            method = "POST",
            path = "people/search",
            body = Json.encodeToString(body)
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        return try {
            runBlocking {
                val response = httpPost(url, getCompanyToken(companyId, platformId), passthroughRequestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                if (response.status == HttpStatusCode.OK) {
                    val jsonString = extractResponseStringFromPassthrough(responseBody)
                    val employees = parseJsonToEmployees(jsonString)
                    if (employees.isNotEmpty())
                        LookupEmployeeResponse(
                            success = true,
                            data = HibobEmployeeLookup(
                                employees = employees,
                            ),
                            responseCode = httpStatusCode
                        )
                    else
                        LookupEmployeeResponse(
                            success = false,
                            error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "No matches: ${response.status.description}"),
                            responseCode = 404
                        )
                } else {
                    LookupEmployeeResponse(
                        success = false,
                        error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error fetching hibob employees: ${response.status.description}"),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: Exception) {
            LookupEmployeeResponse(
                success = false,
                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error fetching hibob employees: ${e.localizedMessage}"),
                responseCode = 400
            )
        }
    }

    override fun addBambooHrTimeOffRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        request: BambooTimeOffRequest,
    ): BambooAddTimeOffResponse {
        log.info("Adding BambooHR time off request for companyId: $companyId, platformId: $platformId, employeeId: $employeeId")
        val url = knitBaseApiUrl + "passthrough"
        val passthroughRequestBody = BambooPassthroughRequest(
            method = "PUT",
            path = "employees/$employeeId/time_off/request",
            body = Json.encodeToString(request),
            headers = mapOf("accept" to "application/json")
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info("Received response from BambooHR. Response body: $responseBody")
                if (response.status == HttpStatusCode.OK) {
                    val jsonString = extractResponseStringFromPassthrough(responseBody)
                    val jsonResponse = Json.decodeFromString<BambooTimeOffResponse>(jsonString)
                    log.info("Successfully added time off request")
                    BambooAddTimeOffResponse(
                        success = true,
                        data = jsonResponse,
                        responseCode = httpStatusCode
                    )
                } else {
                    log.error("Failed to add time off request. Status: ${response.status.description}")
                    BambooAddTimeOffResponse(
                        success = false,
                        error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error adding time off request: ${response.status.description}"),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: Exception) {
            log.error("Exception occurred while adding time off request", e)
            BambooAddTimeOffResponse(
                success = false,
                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error adding time off request: ${e.localizedMessage}"),
                responseCode = 400
            )
        }
    }

    override fun approveBambooHrTimeOffRequest(
        companyId: Long,
        platformId: Long,
        requestId: String,
        status: String,
        note: String,
    ): BambooApproveTimeOffResponse {
        log.info("Approving BambooHR time off request for companyId: $companyId, platformId: $platformId, requestId: $requestId")
        val url = knitBaseApiUrl + "passthrough"
        val approveRequestBody = ApproveTimeOffRequest(status, note)
        val passthroughRequestBody = BambooPassthroughRequest(
            method = "PUT",
            path = "time_off/requests/$requestId/status",
            body = Json.encodeToString(approveRequestBody)
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                log.info("Received response from BambooHR for approval. Status code: $httpStatusCode")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully approved time off request. RequestId: $requestId")
                    BambooApproveTimeOffResponse(
                        success = true,
                        responseCode = httpStatusCode
                    )
                } else {
                    throw IntegrationDownstreamException("Knit server error with code: ${response.status}")
                }
            }
        } catch (e: Exception) {
            log.error("Exception occurred while approving time off request", e)
            BambooApproveTimeOffResponse(
                success = false,
                error = com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse(msg = "Error approving time off request: ${e.localizedMessage}"),
                responseCode = 400
            )
        }
    }

    override fun updateKekaEmployeeDetailsPassthrough(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: KekaUpdateEmployeeDetailsRequest,
    ): KekaResponse {
        log.info("Updating employee details for KekaHR companyId: $companyId, platformId: $platformId")
        val url = knitBaseApiUrl + "passthrough"
        val passthroughRequestBody = PassthroughRequest(
            method = "PUT",
            path = "hris/employees/$employeeId/personaldetails",
            body = Json.encodeToString(requestBody)
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                log.info("Received response from KekaHR for updateKekaEmployeeDetailsPassthrough. Status code: $httpStatusCode")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully updateKekaEmployeeDetailsPassthrough")
                    KekaResponse(
                        succeeded = true,
                    )
                } else {
                    throw IntegrationDownstreamException("Knit server error with code: ${response.status} and body ${response.bodyAsText()}")
                }
            }
        } catch (e: Exception) {
            log.error("Exception occurred while approving time off request", e)
            KekaResponse(
                succeeded = false,
                errors = e.localizedMessage
            )
        }
    }

    override fun addKekaLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: KekaTimeOffRequest,
    ): KekaResponse {
        log.info("[KekaHR] Creating leave request for employeeId: $employeeId companyId: $companyId, platformId: $platformId")
        val url = knitBaseApiUrl + "passthrough"
        val passthroughRequestBody = PassthroughRequest(
            method = "POST",
            path = "time/leaverequests",
            body = Json.encodeToString(requestBody)
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                log.info("Received response from KekaHR for addKekaLeaveRequest. Status code: $httpStatusCode")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully addKekaLeaveRequest")
                    KekaResponse(
                        succeeded = true,
                    )
                } else {
                    throw IntegrationDownstreamException("Knit server error with code: ${response.status} and body ${response.bodyAsText()}")
                }
            }
        } catch (e: Exception) {
            log.error("Exception occurred while creating time off request", e)
            KekaResponse(
                succeeded = false,
                errors = e.localizedMessage
            )
        }
    }

    override fun addHibobLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestBody: HibobTimeOffRequest,
    ): HibobPassthroughResponse {
        log.info("[Hibob] Creating leave request for employeeId: $employeeId companyId: $companyId, platformId: $platformId")
        val url = knitBaseApiUrl + "passthrough"
        val passthroughRequestBody = PassthroughRequest(
            method = "POST",
            path = "timeoff/employees/${employeeId}/requests",
            body = Json.encodeToString(requestBody)
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                log.info("Received response from Hibob for addHibobLeaveRequest. Status code: $httpStatusCode")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully addHibobLeaveRequest")
                    HibobPassthroughResponse(
                        succeeded = true,
                    )
                } else {
                    throw IntegrationDownstreamException("Knit server error with code: ${response.status} and body ${response.bodyAsText()}")
                }
            }
        } catch (e: Exception) {
            log.error("Exception occurred while creating time off request", e)
            HibobPassthroughResponse(
                succeeded = false,
                errors = e.localizedMessage
            )
        }
    }

    override fun cancelHibobLeaveRequest(
        companyId: Long,
        platformId: Long,
        employeeId: String,
        requestId: String,
    ): HibobPassthroughResponse {
        log.info("[Hibob] Deleting leave request for employeeId: $employeeId companyId: $companyId, platformId: $platformId")
        val url = knitBaseApiUrl + "passthrough"
        val passthroughRequestBody = PassthroughRequest(
            method = "DELETE",
            path = "timeoff/employees/${employeeId}/requests/${requestId}",
        ).let { TextContent(Json.encodeToString(it), ContentType.Application.Json) }
        val integrationId = getCompanyToken(companyId, platformId)

        return try {
            runBlocking {
                val response = httpPost(url, integrationId, passthroughRequestBody)
                val httpStatusCode = response.status.value
                log.info("Received response from Hibob for cancelHibobLeaveRequest. Status code: $httpStatusCode")

                if (response.status == HttpStatusCode.OK) {
                    log.info("Successfully cancelHibobLeaveRequest")
                    HibobPassthroughResponse(
                        succeeded = true,
                    )
                } else {
                    throw IntegrationDownstreamException("Knit server error with code: ${response.status} and body ${response.bodyAsText()}")
                }
            }
        } catch (e: Exception) {
            log.error("Exception occurred while deleting time off request", e)
            HibobPassthroughResponse(
                succeeded = false,
                errors = e.localizedMessage
            )
        }
    }

    override fun getHibobEmployeeBankAccounts(
        companyId: Long,
        platformId: Long,
        employeeId: String,
    ): List<HibobBankAccount> {
        val url = knitBaseApiUrl + "passthrough"
        log.info { "Fetching bank accounts for employeeId=$employeeId, companyId=$companyId, platformId=$platformId" }
        val integrationId = getCompanyToken(companyId, platformId)
        val passthroughRequestBody = PassthroughRequest(
            method = "GET",
            path = "people/$employeeId/bank-accounts"
        )
        val body = TextContent(Json.encodeToString(passthroughRequestBody), ContentType.Application.Json)
        return try {
            runBlocking {
                val response = httpPost(url, integrationId, body)
                val httpStatusCode = response.status.value
                val responseBody = response.bodyAsText()
                log.info { "Response Body: $responseBody" }
                if (response.status == HttpStatusCode.OK) {
                    val rawResponse = json.decodeFromString<PassthroughResponse>(responseBody)
                    val bankAccountsJson = rawResponse.data?.response?.body
                    if (!bankAccountsJson.isNullOrBlank()) {
                        val response = json.decodeFromString<HibobBankAccountsResponse>(bankAccountsJson)
                        if (!response.values.isNullOrEmpty()) {
                            response.values
                        } else {
                            log.info("No bank accounts found for employeeId=$employeeId")
                            emptyList()
                        }
                    } else {
                        emptyList()
                    }
                } else {
                    log.error { "Error fetching bank accounts: $httpStatusCode, ${response.status.description} responseBody: $responseBody" }
                    emptyList()
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Exception fetching bank accounts for employeeId: $employeeId" }
            emptyList()
        }
    }

    override fun setTestOngoing(isTestOngoing: Boolean) {
        testOngoing = isTestOngoing
    }

    private fun extractResponseStringFromPassthrough(responseBody: String): String {
        val jsonOuterResponse = JSONObject(responseBody)
        val dataObject = jsonOuterResponse.getJSONObject("data")
        val responseObject = dataObject.getJSONObject("response")
        val jsonString = responseObject.getString("body")
        return jsonString
    }

    fun parseJsonToEmployees(jsonString: String): List<HibobEmployee> {
        val employeesList = mutableListOf<HibobEmployee>()

        val jsonObject = JSONObject(jsonString)

        val employeesArray: JSONArray = jsonObject.getJSONArray("employees")

        for (i in 0 until employeesArray.length()) {
            val employeeJson = employeesArray.getJSONObject(i)

            val id = employeeJson.getJSONObject("/root/id").getString("value")
            val email = employeeJson.getJSONObject("/root/email").getString("value")

            val employee = HibobEmployee(id = id, email = email)
            employeesList.add(employee)
        }

        return employeesList
    }

    fun parseWorkLocationsXml(xmlString: String): Lists {
        // Trim the XML string to remove any leading whitespace
        val trimmedXmlString = xmlString.trim()
        // Remove BOM if present
        val xmlStringWithoutBOM = if (trimmedXmlString.startsWith("\uFEFF")) {
            trimmedXmlString.substring(1)
        } else {
            trimmedXmlString
        }
        // Prepare for parsing
        val docBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder()
        val inputSource = InputSource(StringReader(xmlStringWithoutBOM))
        val document = docBuilder.parse(inputSource)
        val lists = mutableListOf<ListData>()
        val listNodes = document.getElementsByTagName("list")
        for (i in 0 until listNodes.length) {
            val node = listNodes.item(i)
            if (node.nodeType == Node.ELEMENT_NODE) {
                val element = node as Element
                val fieldId = element.getAttribute("fieldId")
                val alias = element.getAttribute("alias")
                val name = element.getElementsByTagName("name").item(0).textContent
                val options = parseOptions(element.getElementsByTagName("options").item(0) as Element)
                lists.add(ListData(fieldId, alias, name, options))
            }
        }

        return Lists(lists)
    }

    fun parseOptions(element: Element): Options {
        val options = mutableListOf<OptionData>()
        val optionNodes = element.getElementsByTagName("option")
        for (i in 0 until optionNodes.length) {
            val node = optionNodes.item(i)
            if (node.nodeType == Node.ELEMENT_NODE) {
                val optionElement = node as Element
                val id = optionElement.getAttribute("id")
                val archived = optionElement.getAttribute("archived")
                val createdDate = optionElement.getAttribute("createdDate")
                val archivedDate = optionElement.getAttribute("archivedDate")
                val name = optionElement.textContent
                options.add(OptionData(id, archived, createdDate, archivedDate, name))
            }
        }

        return Options(options)
    }

    private fun parseXmlToCategories(xmlString: String): List<Category> {
        val docBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder()
        val inputSource = InputSource(StringReader(xmlString))
        val document = docBuilder.parse(inputSource)
        val categories = mutableListOf<Category>()
        val nodeList = document.getElementsByTagName("category")
        for (i in 0 until nodeList.length) {
            val node = nodeList.item(i)
            if (node.nodeType == Node.ELEMENT_NODE) {
                val element = node as Element
                val id = element.getAttribute("id")
                val name = element.getElementsByTagName("name").item(0).textContent
                val canUploadFilesStr = element.getElementsByTagName("canUploadFiles").item(0).textContent
                val canUploadFiles = canUploadFilesStr == "yes"
                categories.add(Category(id, name, canUploadFiles = canUploadFiles))
            }
        }
        return categories
    }

    private fun parseXmlToEmployee(xmlString: String): List<Employee> {
        val docBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder()
        val inputSource = InputSource(StringReader(xmlString))
        val document = docBuilder.parse(inputSource)
        val nodeList = document.getElementsByTagName("employee")

        return (0 until nodeList.length).mapNotNull { i ->
            val node = nodeList.item(i)
            if (node.nodeType == Node.ELEMENT_NODE) {
                val element = node as Element
                val id = element.getAttribute("id")
                val workEmail = element.getElementsByTagName("field").let { fields ->
                    (0 until fields.length).firstNotNullOfOrNull { j ->
                        val field = fields.item(j) as? Element
                        if (field?.getAttribute("id") == "workEmail") {
                            field.textContent.trim()
                        } else {
                            null
                        }
                    }
                }
                Employee(id, workEmail)
            } else {
                null
            }
        }
    }

    private fun updateInternalEmployeeData(
        integrationId: Long,
        request: UpdateEmployeeDetailsRequest,
    ) {
        log.info(
            "[HibobHRPlatformStrategy] Updating internal employee data for externalEmployeeId=${request.employeeId}",
        )
        val objectMapper = jacksonObjectMapper()
        val employee =
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                integrationId,
                request.employeeId!!
            )
                .firstOrNull()
                ?: throw IntegrationIllegalStateException("Employee not found in internal data for employeeId=${request.employeeId} and integrationId=$integrationId")
        val employeeData = DataMapper.objectMapper.readValue(employee.employeeData, EmployeeData::class.java)
        val newProfile = employeeData.profile?.copy(
            firstName = request.firstName,
            lastName = request.lastName,
            workEmail = request.workEmail,
        )
        val newContactInfo = employeeData.contactInfo?.copy(personalEmails = request.personalEmails)
        val newEmployeeData = employeeData.copy(profile = newProfile, contactInfo = newContactInfo)
        try {
            employee.employeeData = DataMapper.objectMapper.writeValueAsString(newEmployeeData)
            platformEmployeeDataRepository.save(employee)
            log.info(
                "[HibobHRPlatformStrategy] Finished update internal employee data for externalEmployeeId=${request.employeeId}",
            )
        } catch (e: Exception) {
            log.error(
                "Error updating internal employee data for externalEmployeeId=${request.employeeId} and integrationId=$integrationId",
            )
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException(e.message, e)
        }
    }

    private fun getCompanyToken(companyId: Long, platformId: Long): String {
        val token =
            companyIntegrationRepository
                .findEnabledIntegration(
                    companyId,
                    platformId,
                )
                ?.firstOrNull()
                ?.accountToken
                ?: throw IntegrationIllegalArgumentException(
                    "No token found for companyId $companyId, platformId $platformId"
                )
        return token
    }

    private fun performEmployeeUpdate(
        requestBodyJson: String,
        companyId: Long,
        platformId: Long,
        logPrefix: String,
    ): UpdateEmployeeResponse {
        val integrationId = getCompanyToken(companyId, platformId)
        val url = knitBaseApiUrl + "hr.employee.update"
        return try {
            runBlocking {
                val httpResponse =
                    httpPost(url, integrationId, TextContent(requestBodyJson, ContentType.Application.Json))
                if (httpResponse.status != HttpStatusCode.OK) {
                    log.error("$logPrefix: Failed to update employee record with status: ${httpResponse.status}")
                    val bodyString = runBlocking { httpResponse.body<String>() }
                    val errorResponse: ErrorResponse = DataMapper.objectMapper.readValue(bodyString)
                    log.error("$logPrefix: Error Message from API: ${errorResponse.error.msg}")
                    UpdateEmployeeResponse(success = false, errorMessage = errorResponse.error.msg)
                } else {
                    val responseBodyString = runBlocking { httpResponse.body<String>() }
                    val responseBody: EmployeeCreationSuccessResponse =
                        DataMapper.objectMapper.readValue(responseBodyString)
                    if (!responseBody.success) {
                        log.error("$logPrefix: Unexpected error occurred performEmployeeUpdate")
                        UpdateEmployeeResponse(success = false, errorMessage = "Unexpected error occurred")
                    } else {
                        log.info { "$logPrefix: Successfully updated employee with ID: ${responseBody.employeeId}" }
                        UpdateEmployeeResponse(success = true)
                    }
                }
            }
        } catch (e: Exception) {
            log.error("$logPrefix: Unexpected error occurred")
            return UpdateEmployeeResponse(success = false, errorMessage = "Unexpected error occurred")
        }
    }

    private fun httpGet(
        url: String,
        integrationId: String,
        timeoutInMilliseconds: Long = DEFAULT_REQUEST_TIMEOUT
    ): HttpResponse {
        return runBlocking {
            httpClient.get(url) {
                headers {
                    append(integrationIdHeader, integrationId)
                }
                timeout {
                    requestTimeoutMillis = timeoutInMilliseconds
                }
            }
        }
    }

    private fun httpDelete(url: String, integrationId: String): HttpResponse {
        return runBlocking {
            httpClient.delete(url) {
                headers {
                    append(integrationIdHeader, integrationId)
                }
            }
        }
    }

    private fun httpPost(url: String, integrationId: String, body: Any): HttpResponse {
        return runBlocking {
            httpClient.post(url) {
                setBody(body)
                headers {
                    append(integrationIdHeader, integrationId)
                }
            }
        }
    }

    @Serializable
    data class UpdateExpenseReportRequest(
        val reportStatus: String = "REIMBURSED",
        val reportId: String,
    )

    data class ErrorResponseData(
        val msg: String,
    )

    data class ErrorResponse(val error: ErrorMessage)
    data class ErrorMessage(val msg: String)

    data class EmployeeCreationSuccessResponse(val success: Boolean, val employeeId: Long)

    data class ReportStatusUpdateSuccessResponse(
        val success: Boolean,
        val data: String,
    )

    data class ReportStatusUpdateErrorResponse(
        val success: Boolean,
        val error: ErrorResponseData,
    )
}