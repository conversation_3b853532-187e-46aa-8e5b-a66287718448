package com.multiplier.integration.adapter.api.resources.knit.hibob

import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import kotlinx.serialization.Serializable

@Serializable
data class HibobBankAccountsResponse(
    val values: List<HibobBankAccount>? = null,
)

@Serializable
data class HibobBankAccount(
    val bankAccountType: String? = null,
    val routingNumber: String? = null,
    val accountNickname: String? = null,
    val accountNumber: String? = null,
    val bankName: String? = null,
    val branchAddress: String? = null,
    val bicOrSwift: String? = null,
    val iban: String? = null,
    val allocation: String? = null,
    val amount: Double? = null,
    val useForBonus: Boolean? = null,
    val id: Int? = null,
    val changedBy: String? = null
)