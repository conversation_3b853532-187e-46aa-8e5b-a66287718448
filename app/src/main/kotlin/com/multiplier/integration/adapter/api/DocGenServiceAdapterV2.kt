package com.multiplier.integration.adapter.api

import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.documentgeneration.v2.grpc.schema.DocumentProto
import com.multiplier.documentgeneration.v2.grpc.schema.DocumentServiceGrpc
import java.net.URI
import java.net.URL
import java.util.*
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

const val TEMP_DATA_URL_PREFIX = "http://base64-data/"

fun interface DocGenServiceAdapterV2 {
    fun getDocument(documentId: String): DocumentResponse?
}

@Service
class DocgenV2Service(
    private val docgenServiceAdapterV1: DocgenServiceAdapter
) : DocGenServiceAdapterV2 {
    private val logger = KotlinLogging.logger {}

    @GrpcClient("docgen-service")
    private lateinit var documentClient: DocumentServiceGrpc.DocumentServiceBlockingStub

    companion object {
        fun isV1DocumentId(id: String?) = runCatching { id.orEmpty().toLong() }.isSuccess
    }

    override fun getDocument(documentId: String): DocumentResponse? {
        return if (isV1DocumentId(documentId)) {
            docgenServiceAdapterV1.getDocument(documentId.toLong())
        }
        else {
            getDocumentV2(documentId)
        }
    }

    private fun getDocumentV2(documentId: String): DocumentResponse? =
        runCatching {
            logger.info { "Downloading document: $documentId" }
            val result = documentClient.downloadDocument(
                DocumentProto.GetDocumentRequest.newBuilder()
                    .setId(documentId)
                    .build()
            )
            return DocumentResponse().internalDownloadURL(createDataUrlFromFile(result.fileContent.toByteArray()))
        }.onFailure { e ->
            logger.error(e) { "Failed to download v2 document with id: $documentId" }
        }.getOrNull()

    // FIX ME: because internal download URL is not yet supported in docgen v2
    // we have to create a data url from the file content and use it temporarily.
    // Once internal download URL is supported, we can refactor getDocumentV2() method and remove this method.
    private fun createDataUrlFromFile(fileContent: ByteArray): URL {
        val base64Content = Base64.getEncoder().encodeToString(fileContent)
        val dataUrl = "$TEMP_DATA_URL_PREFIX$base64Content"
        return URI.create(dataUrl).toURL()
    }
}