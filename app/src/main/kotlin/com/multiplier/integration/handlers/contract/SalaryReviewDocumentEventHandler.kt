package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.compensation.SalaryReviewDocumentMessage.SalaryReviewDocumentEventMessage
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocGenServiceAdapterV2
import com.multiplier.integration.core.model.ContractSalaryDocumentUpdateEvent
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseSalaryReviewDocumentEventMessageFromString
import mu.KotlinLogging
import org.apache.commons.lang3.StringUtils
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class SalaryReviewDocumentEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService,
    private val docgenServiceAdapterV2: DocGenServiceAdapterV2
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleSalaryReviewDocumentUpdateEvent(event: ContractSalaryDocumentUpdateEvent) {

        val eventLogId = event.eventLogId
        log.info("Started handleSalaryReviewDocumentUpdateEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleContractDocumentUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of handleContractDocumentUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleContractDocumentUpdateEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleContractDocumentUpdateEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val payload: SalaryReviewDocumentEventMessage = parseSalaryReviewDocumentEventMessageFromString(payloadString)

        val documentId = StringUtils.defaultIfBlank(payload.documentIdV2,  payload.documentId.toString())
        val contractId = payload.contractId

        log.info("Fetching document by ID: {}", documentId)
        val document = docgenServiceAdapterV2.getDocument(documentId)

        log.info { "Fetching contract by ID: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.updateSalaryReviewDocument(companyId, contractId, document!!)
    }
}