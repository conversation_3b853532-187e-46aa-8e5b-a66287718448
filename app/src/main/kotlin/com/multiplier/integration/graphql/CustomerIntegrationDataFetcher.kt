package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.PendingEmployeeService
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.types.Company
import com.multiplier.integration.types.CompanyIntegrationInfo
import com.multiplier.integration.types.CompanySyncStatus
import com.multiplier.integration.types.CustomerIntegration
import com.multiplier.integration.types.CustomerIntegrationDefinition
import com.multiplier.integration.types.Employee
import com.multiplier.integration.types.FetchEmployeesResult
import com.multiplier.integration.types.LatestSyncResult
import com.multiplier.integration.types.Position
import com.multiplier.integration.types.SyncStatus
import com.multiplier.integration.types.SyncSummaryResultDownloadInput
import com.multiplier.integration.types.SyncSummaryResultDownloadOutput
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsData
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsDataLoader
import com.netflix.graphql.dgs.DgsEntityFetcher
import com.netflix.graphql.dgs.DgsQuery
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.dataloader.MappedBatchLoader
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.task.SimpleAsyncTaskExecutor
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.concurrent.DelegatingSecurityContextExecutor
import org.springframework.security.core.context.SecurityContextHolder
import java.time.LocalDateTime
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage

@DgsComponent
class CustomerIntegrationDataFetcher(
    private val customerIntegrationService: CustomerIntegrationService,
    private val pendingEmployeeService: PendingEmployeeService,
    private val syncService: SyncService,
    private val currentUser: CurrentUser,
) {

    private val log = KotlinLogging.logger {}

    @Value("\${platform.base-url}")
    private lateinit var baseUrl: String

    @DgsQuery(field = "customerIntegrationDefinitions")
    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getAllSupportedIntegrations(): List<CustomerIntegrationDefinition> =
        customerIntegrationService.listSupportedIntegrations()

    @DgsData(
        parentType = DgsConstants.COMPANY.TYPE_NAME,
        field = DgsConstants.COMPANY.CustomerIntegrations
    )
    @PreAuthorize(
        "@me.allowed('view.operations.company.integration') || (@me.allowed('view.company.company.has-integration') && @me.isCompanyUserForCompanyId(#dfe.getSource().getId()))"
    )
    fun findCustomerIntegrationsForCompanyId(
        dfe: DgsDataFetchingEnvironment
    ): CompletableFuture<List<CustomerIntegration>> {
        val dataLoader =
            dfe.getDataLoader<Long, List<CustomerIntegration>>(
                CustomerIntegrationDataLoader::class.java
            )
        val company = dfe.getSource<Company>()
        return dataLoader.load(company?.id)
    }

    @DgsEntityFetcher(name = DgsConstants.COMPANY.TYPE_NAME)
    fun fetchCompany(values: Map<String, Any>): Company {
        val company = Company()
        values["id"].toString().toLongOrNull().also { company.id = it }

        return company
    }

    @DgsQuery(field = "getPositionsForIntegrations")
    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getPositionsForIntegrations(
        @InputArgument("integrationId") integrationId: Long
    ): List<Position> {
        log.info("Fetching positions for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return try {
            customerIntegrationService.getPositionsForIntegration(integrationId, currentUserCompanyId, isOpsUser(currentUser))
        } catch (e: Exception) {
            log.error("Error fetching positions for integrationId: {}", integrationId, e)
            emptyList()
        }
    }

    @DgsQuery(field = "companyIntegrationInfo")
    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getCompanyIntegrationInfo(
        @InputArgument("integrationId") integrationId: Long
    ): CompanyIntegrationInfo {
        log.info("Fetching integration info for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return customerIntegrationService.getCompanyIntegrationInfo(integrationId, currentUserCompanyId, isOpsUser(currentUser))
    }

    @DgsQuery(field = "getLatestSyncResultForIntegration")
    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getLatestSyncResultForIntegration(
        @InputArgument("integrationId") integrationId: Long
    ): LatestSyncResult? {
        log.info("Fetching latest sync result for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return customerIntegrationService.getLatestSyncResultForIntegration(integrationId, currentUserCompanyId, false, isOpsUser(currentUser))
    }

    @DgsQuery(field = "validateIntegrationCredentials")
    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun validateIntegrationCredentials(
        @InputArgument("integrationId") integrationId: Long
    ): TaskResponse? {
        log.info("Validating integration credentials for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return customerIntegrationService.validateIntegrationCredentials(integrationId, currentUserCompanyId, true, isOpsUser(currentUser))
    }

    @DgsQuery(field = "syncStatus")
    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getSyncStatus(
        @InputArgument("integrationId") integrationId: Long,
        @InputArgument("syncType") syncType: SyncType
    ): CompanySyncStatus {
        log.info("Fetching sync status for integrationId: {} and syncType: {}", integrationId, syncType)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        var syncOngoing = customerIntegrationService.getCompanyIntegrationInfo(integrationId, currentUserCompanyId, isOpsUser(currentUser))
        var syncStatus = if (syncOngoing.incomingSyncInProgress) SyncStatus.PENDING else SyncStatus.SUCCESS
        return CompanySyncStatus(syncOngoing.companyId, syncStatus, LocalDateTime.now(), LocalDateTime.now())
    }

    @DgsQuery(field = "getEmployeesToSendInviteTo")
    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getEmployeesToSendInviteTo(
        @InputArgument("integrationId") integrationId: Long
    ): List<Employee> {
        log.info("Fetching EmployeesToSendInviteTo for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return pendingEmployeeService.getEmployeesToSendInviteTo(integrationId, currentUserCompanyId, isOpsUser(currentUser))
    }

    @DgsQuery(field = "getLatestEmployeesForImport")
    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun getLatestEmployeesForImport(
        @InputArgument("integrationId") integrationId: Long
    ): FetchEmployeesResult {
        log.info("Fetching LatestEmployeesForImport for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return syncService.getEmployeesToSync(integrationId, currentUserCompanyId, isOpsUser(currentUser))
    }

    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery(field = DgsConstants.QUERY.SyncSummaryResultDownload)
    fun syncSummaryResultDownload(
        input: SyncSummaryResultDownloadInput,
    ): SyncSummaryResultDownloadOutput {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        log.info("Get syncSummaryResultDownload for companyId $currentUserCompanyId")
        return customerIntegrationService.getSyncSummaryResultDownloadableFile(input.syncId, currentUserCompanyId)
    }

}


@DgsDataLoader(name = DgsConstants.CUSTOMERINTEGRATION.TYPE_NAME, maxBatchSize = 50)
class CustomerIntegrationDataLoader(
    private val customerIntegrationService: CustomerIntegrationService,
) : MappedBatchLoader<Long, List<CustomerIntegration>?> {

    fun getSecurityContextExecutor() =
        DelegatingSecurityContextExecutor(SimpleAsyncTaskExecutor(), SecurityContextHolder.getContext())

    override fun load(
        keys: MutableSet<Long>?
    ): CompletionStage<Map<Long, List<CustomerIntegration>?>> {
        val executor = getSecurityContextExecutor()

        if (!keys.isNullOrEmpty()) {
            return CompletableFuture.supplyAsync(
                {
                    customerIntegrationService.findCompanyConnectedIntegrationsByCompanyIds(
                        keys.toSet()
                    )
                },
                executor::execute
            )
        }

        return CompletableFuture.supplyAsync({ mutableMapOf() }, executor::execute)
    }
}
