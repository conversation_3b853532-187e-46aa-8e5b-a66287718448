package com.multiplier.integration.graphql

import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.FieldMappingService
import com.multiplier.integration.types.IntegrationEntityMappingStatusOutput
import com.multiplier.integration.types.IntegrationFieldsMappingContractorOutput
import com.multiplier.integration.types.IntegrationFieldsMappingOutputV2
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsQuery
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class FieldMappingFetcher(
    private val fieldMappingService: FieldMappingService
) {
    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery(field = DgsConstants.QUERY.GetIntegrationFieldsMappingProfile)
    fun getIntegrationFieldsMappingProfile(
        entityId: Long,
        integrationId: Long
    ): IntegrationFieldsMappingOutputV2 {
        log.info("[getIntegrationFieldsMappingProfile] entityId $entityId - integrationId $integrationId")
        return fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)
    }

    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery(field = DgsConstants.QUERY.GetIntegrationFieldsMappingContractorProfile)
    fun getIntegrationFieldsMappingContractorProfile(
        integrationId: Long
    ): IntegrationFieldsMappingContractorOutput {
        log.info("[getIntegrationFieldsMappingContractorProfile] - integrationId $integrationId")
        return fieldMappingService.getIntegrationFieldsMappingContractorProfile(integrationId)
    }

    @PreAuthorize("@me.allowed('validate.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery(field = DgsConstants.QUERY.GetIntegrationEntityMappingStatus)
    fun getIntegrationEntityMappingStatus(
        integrationId: Long
    ): List<IntegrationEntityMappingStatusOutput> {
        log.info("[getIntegrationEntityMappingStatus] integrationId $integrationId")
        return fieldMappingService.getIntegrationLegalEntityMappings(integrationId)
    }
}