package com.multiplier.integration.service

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.multiplier.common.exception.toSystemException
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.expense.schema.BulkDeleteExpensesRequest
import com.multiplier.expense.schema.CreateExpensesRequest
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.Constants
import com.multiplier.integration.Constants.EmployeeOrigin
import com.multiplier.integration.adapter.api.ContractOffBoardingServiceAdapter
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.PaymentServiceAdapter
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.ContractOffBoardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.adapter.model.knit.BankAccountDetails
import com.multiplier.integration.adapter.model.knit.CompensationType
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.adapter.model.knit.EmployeeLocation
import com.multiplier.integration.adapter.model.knit.Locations
import com.multiplier.integration.adapter.model.knit.Profile
import com.multiplier.integration.adapter.util.ContractOnboardingService
import com.multiplier.integration.platforms.Platform
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.JpaEntityIntegrationRepository
import com.multiplier.integration.repository.PendingEmployeeRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.repository.TimeoffEventRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPendingEmployee
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.model.JpaSync
import com.multiplier.integration.repository.model.JpaTimeoffEvent
import com.multiplier.integration.service.InboundDataExtractionService.BaseExtractor.Companion.extractDataFromPathAsString
import com.multiplier.integration.service.InboundDataExtractionService.CountryExtractor.Companion.extractCountry
import com.multiplier.integration.service.InboundDataExtractionService.CountryExtractor.Companion.extractState
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.service.fieldmappings.CompensationSchemaConfigService
import com.multiplier.integration.service.fieldmappings.EmployeeDataFieldMapper
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.DataMapper.Companion.objectMapper
import com.multiplier.integration.sync.model.BankAccount
import com.multiplier.integration.sync.model.EmployeeLeaveRequest
import com.multiplier.integration.sync.model.EmploymentStatus
import com.multiplier.integration.sync.model.EventData
import com.multiplier.integration.sync.model.ExpenseData
import com.multiplier.integration.sync.model.PayType
import com.multiplier.integration.sync.model.RoutingType
import com.multiplier.integration.types.Employee
import com.multiplier.integration.types.FetchEmployeesResult
import com.multiplier.integration.types.NotificationType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha3CountryCode
import com.multiplier.integration.utils.convertDateToLocalDate
import com.multiplier.integration.utils.toDate
import com.multiplier.integration.utils.toEmployeeCompensationData
import com.multiplier.integration.utils.validateIntegrationCompanyMatch
import com.multiplier.member.schema.MemberBankDynamicDetail
import com.multiplier.member.schema.MemberBankStaticDetail
import com.multiplier.member.schema.PaymentAccountType
import com.multiplier.member.schema.UpsertBankDetailsInput
import com.multiplier.member.schema.UpsertBankDetailsRequest
import com.multiplier.payse.schema.common.AccountType
import com.multiplier.payse.schema.common.PaymentDirection
import com.multiplier.payse.schema.common.PaymentPartner
import com.multiplier.payse.schema.common.TransferType
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPartner
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirement
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirements
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementsRequest
import com.multiplier.pigeonservice.dto.Attachment
import mu.KotlinLogging
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.random.Random

const val BANK_DATA_SPEC_PREFIX = "bank"
const val GP_BULK_ONBOARDING_TEMPLATE_VERSION = "1.0.2"

@Service
class SyncService(
    private val jpaReceivedEventRepository: ReceivedEventRepository,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val pendingEmployeeRepository: PendingEmployeeRepository,
    private val syncRepository: SyncRepository,
    private val notificationsService: NotificationsService,
    private val knitAdapter: KnitAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val jpaEntityIntegrationRepository: JpaEntityIntegrationRepository,
    private val integrationRepository: CompanyIntegrationRepository,
    private val manualSyncsInProgress: ConcurrentHashMap<String, Boolean> = ConcurrentHashMap<String, Boolean>(),
    private val contractOffboardingServiceAdapter: ContractOffBoardingServiceAdapter,
    private val expenseProcessorService: ExpenseProcessorService,
    private val dataMapper: DataMapper,
    private val customerIntegrationService: CustomerIntegrationService,
    private val employeeService: EmployeeService,
    private val newCompanyServiceAdapter: NewCompanyServiceAdapter,
    private val contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter,
    private val paymentServiceAdapter: PaymentServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val timeoffEventRepository: TimeoffEventRepository,
    private var localSyncInProgress: Boolean = false,
    private var companyId : Long = 0,
    private var countryCode : String = "",
    private var entityId : Long = 0,
    private val createdContractIds: MutableList<Long> = mutableListOf(),
    private val platformTimeoffIntegrationRepository: PlatformTimeoffIntegrationRepository,
    private val employeeDataFieldMapper: EmployeeDataFieldMapper,
    private val compensationSchemaConfigService: CompensationSchemaConfigService,
    private val contractOnboardingService: ContractOnboardingService,
    private val featureFlagService: FeatureFlagService
) {
    private val log = KotlinLogging.logger {}
    private val jacksonObjectMapper = jacksonObjectMapper().apply {
        configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true)
        configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)
    }
    fun removeManualSyncInProgress(integration: JpaCompanyIntegration) {
        integration.incomingSyncInProgress = false
        integrationRepository.save(integration)
    }

    fun setLocalSyncInprogress(isLocalSyncInProgress: Boolean) {
        localSyncInProgress = isLocalSyncInProgress
    }

    fun getLocalSyncIsInProgress() : Boolean{
        return localSyncInProgress
    }

    fun getCreatedContractIds() : List<Long>{
        return createdContractIds
    }

    fun setCompanyId(companyId : Long) {
        this.companyId = companyId
    }

    fun setCountryCode(countryCode : String) {
        this.countryCode = countryCode
    }

    fun setEntityId(entityId : Long) {
        this.entityId = entityId
    }

    fun handleIncomingEvent(event: JsonNode, integrationId: String) {
        log.info("Incoming event  {}", event)
        val providedSyncID = event.get("syncRunId")?.asText() ?: ""
        val matchingSync = getOrCreateSync(providedSyncID, integrationId)

        val matchingIntegration = findMatchingIntegration(matchingSync)

        val confirmedByUser = isConfirmedByUser(matchingIntegration)

        val eventId = event.get("eventId").asText()
        val receivedEvent = createReceivedEvent(event, integrationId, confirmedByUser, eventId)

        processEvent(event, matchingIntegration, matchingSync, receivedEvent)
    }

    private fun getOrCreateSync(providedSyncID: String, integrationId: String): JpaSync {
        var matchingSync = syncRepository.findBySyncId(providedSyncID).orElse(null)

        if (matchingSync == null) {
            synchronized(this) {
                matchingSync = syncRepository.findBySyncId(providedSyncID).orElse(null)
                if (matchingSync == null && providedSyncID.isNotEmpty()) {
                    matchingSync = createNewSync(providedSyncID, integrationId)
                }
            }
        }
        return matchingSync
    }

    private fun createNewSync(syncId: String, integrationId: String): JpaSync {
        val sync = JpaSync(syncId, integrationId, LocalDateTime.now(), null, null, true)
        return try {
            syncRepository.deleteByIntegrationId(integrationId)
            syncRepository.save(sync)
        } catch (e: Exception) {
            log.warn("Exception while trying to save sync : {}", e)
            syncRepository.findBySyncId(syncId).orElse(null)
        }
    }

    private fun findMatchingIntegration(matchingSync: JpaSync): JpaCompanyIntegration {
        return matchingSync.integrationId?.let {
            companyIntegrationRepository.findByAccountToken(it)
        } ?: throw NoSuchElementException("Integration not found for account token: ${matchingSync.integrationId}")
    }

    private fun isConfirmedByUser(matchingIntegration: JpaCompanyIntegration?): Boolean {
        return matchingIntegration != null &&
                matchingIntegration.incomingSyncEnabled &&
                !matchingIntegration.incomingSyncInProgress
    }

    private fun createReceivedEvent(event: JsonNode, integrationId: String, confirmedByUser: Boolean, eventId: String): JpaReceivedEvent? {
        return if (!event.get("syncRunId").isNull) {
            val eventType = event.get("eventType")?.asText()
            val recordIdentifier = event.get("eventData")?.get("profile")?.get("id")?.asText()
            val syncDataType = event.get("syncDataType")?.asText()

            JpaReceivedEvent(
                eventId, event.get("syncRunId").asText(), integrationId, EventType.fromString(eventType), syncDataType, null,
                recordIdentifier, LocalDateTime.now(),
                event.toString(), confirmedByUser, false
            )
        } else null
    }

    private fun processEvent(event: JsonNode, matchingIntegration: JpaCompanyIntegration, matchingSync: JpaSync, receivedEvent: JpaReceivedEvent?) {
        if (event.get("eventType")?.asText() == "sync.events.processed") {
            handleSyncProcessedEvent(matchingIntegration, matchingSync, receivedEvent)
        } else {
            handleRegularEvent(event, matchingIntegration, receivedEvent)
        }
    }

    private fun handleSyncProcessedEvent(matchingIntegration: JpaCompanyIntegration, matchingSync: JpaSync, receivedEvent: JpaReceivedEvent?) {
        if (localSyncInProgress) {
            localSyncInProgress = false
            log.info("Received processed event. Local sync is over...")
            return
        }
        matchingSync.endTime = LocalDateTime.now()
        matchingSync.inProgress = false
        matchingIntegration.incomingSyncInProgress = false
        try {
            companyIntegrationRepository.save(matchingIntegration)
            syncRepository.save(matchingSync)
        } catch (e: Exception) {
            log.error("Could not update data for sync :{}. Exception: {}", matchingSync, e)
        }
        receivedEvent?.let { jpaReceivedEventRepository.save(it) }
    }

    private fun handleRegularEvent(event: JsonNode, matchingIntegration: JpaCompanyIntegration, receivedEvent: JpaReceivedEvent?) {
        receivedEvent?.let {
            if (!localSyncInProgress) {
                jpaReceivedEventRepository.save(it)
            } else {
                processTestEmployeeEvent(event, companyId, entityId, countryCode)
            }
        }
        processTimeoffEvents(event, matchingIntegration)
    }

    private fun processTimeoffEvents(event: JsonNode, matchingIntegration: JpaCompanyIntegration) {
        val eventData = dataMapper.map(event)
        if (!eventData.leaveRequests.isNullOrEmpty()) {
            val timeoffEvents = mapEmployeeLeaveRequestsToTimeoffEvents(eventData.leaveRequests, matchingIntegration, eventData.profile?.id)
            timeoffEvents.forEach { timeoffEvent ->
                timeoffEventRepository.save(timeoffEvent)
            }
        }
    }

    private fun removeTimeoffIntegration(externalTimeoffId: String) {
        val toRemove = platformTimeoffIntegrationRepository.findByExternalTimeoffId(externalTimeoffId = externalTimeoffId)
        if (toRemove != null)
            platformTimeoffIntegrationRepository.delete(toRemove)
    }

    fun convertStringToDate(stringDate: String): com.google.type.Date {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'")
        val localDate = LocalDate.parse(stringDate, formatter)

        return com.google.type.Date.newBuilder()
            .setYear(localDate.year)
            .setMonth(localDate.monthValue)
            .setDay(localDate.dayOfMonth)
            .build()
    }



    fun processApprovedCreateEvents() {
        log.info ("Processing approved create events...")
        val employeeCreatedEvents =
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_NEW,
                syncDataType = "employee",
                confirmedByUser = true,
                processed = false
            )

        if (employeeCreatedEvents.isEmpty()) {
            log.info("No pending create events to process at this time.")
        }

        val errorList = mutableListOf<EmployeeValidationError>();
        for (event in employeeCreatedEvents) {
            Thread.sleep(1000)
            try {
                log.info("Start processing event for eventId: ${event.eventId}")
                // setting inviteMember = false to prevent a racing between manually import and scheduler import, which may run at the same times
                // that results in a group of employees maybe invited and somes are not, we will introduce a lock and comeback to this later
                this.processEmployeeEvent(event, false, errorList);
            } catch (e: Exception) {
                log.warn("Error processing event for eventId: ${event.eventId}", e)
                event.errors = e.message
            } finally {
                log.info("Finalizing processing event for eventId: ${event.eventId}")
                event.processed = true
                jpaReceivedEventRepository.save(event)
            }
        }

        val inProgressIntegrationIdToCountryMap: Map<String, List<String>> = employeeCreatedEvents
            .groupBy({ it.integrationId!! }, { it.entityCountry!! })
            .mapValues { entry -> entry.value.distinct() }

        if (errorList.size > 0) {
            val errorByIntegrationIdMap = errorList.groupBy { it.integrationId }
            errorByIntegrationIdMap.forEach{ entry ->
                val companyId = entry.value.first().companyId
                val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId!!)
                val input = getFileFromResources("/templates/error-report-file.xlsx")

                val resultFileByteArray = ExcelResultGenerator.addValidationErrorsToInputSheet(
                    inputSheet = input,
                    validationErrors = entry.value
                )
                val attachments = listOf(Attachment("error-report-file.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", resultFileByteArray))

                val containFailedCreations = inProgressIntegrationIdToCountryMap[entry.key]!!.filter { country ->
                    errorList.any { it.entityCountry == country }
                }.joinToString(", ")
                val successfulEntities = inProgressIntegrationIdToCountryMap[entry.key]!!.filter { country ->
                    errorList.none { it.entityCountry == country }
                }.joinToString(", ")
                val notMapped = jpaReceivedEventRepository.getUnsyncedEntityCountryListByIntegrationId(entry.key)
                    .joinToString(", ")

                notificationsService.sendingResultEmail(
                    companyAdmins,
                    attachments = attachments,
                    templateType = NotificationType.IntegrationGPSyncReportToAdmin,
                    templateParams = mutableMapOf(
                        "containFailedCreations" to containFailedCreations,
                        "successfulEntities" to successfulEntities,
                        "notMapped" to notMapped,
                    )
                )
            }
        }

        processExpenseEvents()

        log.info("Finished processing all creation events.")
    }

    private fun processExpenseEvents() {
        log.info("Processing expense events")
        val expenseCreatedEvents = fetchExpenseCreatedEvents()
        val groupedEventsByIntegrationAndSyncId = groupEventsByIntegrationAndSyncId(expenseCreatedEvents)

        groupedEventsByIntegrationAndSyncId.forEach { (integrationAndSyncId, events) ->
            processEventsForIntegrationAndSyncId(integrationAndSyncId, events, EventType.RECORD_NEW)
        }
    }

    fun processExpenseUpdateEvents() {
        log.info("Processing expense update events")
        val expenseCreatedEvents = fetchExpenseUpdatedEvents()
        val groupedEventsByIntegrationAndSyncId = groupEventsByIntegrationAndSyncId(expenseCreatedEvents)

        groupedEventsByIntegrationAndSyncId.forEach { (integrationAndSyncId, events) ->
            processEventsForIntegrationAndSyncId(integrationAndSyncId, events, EventType.RECORD_UPDATE)
        }
    }

    private fun fetchExpenseCreatedEvents(): List<JpaReceivedEvent> {
        return jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
            eventType = EventType.RECORD_NEW,
            syncDataType = "expense",
            confirmedByUser = true,
            processed = false
        )
    }

    private fun fetchExpenseUpdatedEvents(): List<JpaReceivedEvent> {
        return jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
            eventType = EventType.RECORD_UPDATE,
            syncDataType = Constants.SyncDataType.EXPENSE,
            confirmedByUser = true,
            processed = false
        )
    }

    private fun groupEventsByIntegrationAndSyncId(events: List<JpaReceivedEvent>): Map<Pair<String?, String?>, List<JpaReceivedEvent>> {
        log.info("Grouping EventsByIntegrationAndSyncId")
        return events.groupBy { Pair(it.integrationId, it.syncId) }
    }

    private fun processEventsForIntegrationAndSyncId(integrationAndSyncId: Pair<String?, String?>, events: List<JpaReceivedEvent>, eventType: EventType) {
        val (accountToken, syncId) = integrationAndSyncId
        log.info("Starting to processEventsForIntegrationAndSyncId $syncId and eventType $eventType")
        val syncEndEvent = jpaReceivedEventRepository.findByEventTypeAndSyncId(eventType = EventType.SYNC_END, syncId = syncId).firstOrNull()
        if (syncEndEvent == null) {
            log.warn("Skipping events as syncId $syncId has not finished processing")
            return
        }
        accountToken?.let { token ->
            val companyIntegration = companyIntegrationRepository.findByAccountToken(token)
            if (companyIntegration != null) {
                if (companyIntegration.incomingSyncEnabled) {
                    val eventsGroupedByReportId = groupEventsByReportId(events)
                    eventsGroupedByReportId.forEach { (reportId, eventsWithSameReportId) ->
                        if (reportId == null) {
                            log.error("Skipping events as they have a null reportId")
                        } else {
                            processEventsWithSameReportId(eventsWithSameReportId, eventType)
                        }
                    }
                    // After processing, mark all events as processed
                    events.forEach { markEventAsProcessed(it) }
                } else {
                    log.info("Skipping events for accountToken=$accountToken as incoming_sync_in_progress is true.")
                }
            } else {
                log.error("Company integration not found for accountToken=$accountToken")
            }
        }
    }

    private fun mapEmployeeLeaveRequestsToTimeoffEvents(
        employeeLeaveRequests: List<EmployeeLeaveRequest>?,
        matchingIntegration: JpaCompanyIntegration,
        employeeId: String?
    ): List<JpaTimeoffEvent> {
        if (employeeLeaveRequests.isNullOrEmpty()) {
            return emptyList()
        }

        return employeeLeaveRequests.map { leaveRequest ->
            JpaTimeoffEvent(
                employeeId = employeeId ?: throw IntegrationInternalServerException("mapEmployeeLeaveRequestsToTimeoffEvents with null employeeId FAILED"),
                timeoffType = leaveRequest.leaveType?.name ?: "NOT_SPECIFIED",
                externalId = leaveRequest.id ?: "UNKNOWN",
                integrationId = matchingIntegration.id!!,
                shouldProcess = matchingIntegration.timeOffSyncEnabled,
                processed =  false,
                internalId = null, //internal id is assigned once the mapping is completed
                timeoffData = DataMapper.objectMapper.writeValueAsString(leaveRequest)
            )
        }
    }

    private fun markEventAsProcessed(event: JpaReceivedEvent) {
        try {
            event.processed = true
            jpaReceivedEventRepository.save(event)
            log.info("Event marked as processed: eventId=${event.eventId}")
        } catch (e: Exception) {
            log.error("Failed to mark event as processed: eventId=${event.eventId}", e)
        }
    }

    private fun groupEventsByReportId(events: List<JpaReceivedEvent>): Map<String?, List<JpaReceivedEvent>> {
        return events.mapNotNull { event ->
            try {
                // Directly use readTree to get the "eventData" node, then convert it to ExpenseData
                val eventDataNode = jacksonObjectMapper.readTree(event.data)["eventData"]
                if (eventDataNode != null) {
                    val expenseData = jacksonObjectMapper.treeToValue(eventDataNode, ExpenseData::class.java)
                    expenseData.details?.reportId?.let { reportId ->
                        reportId to event
                    }
                } else {
                    log.error("Event data for event=${event.eventId} is missing or null")
                    null
                }
            } catch (e: Exception) {
                log.error("Error processing event=${event.eventId}, unable to extract reportId", e)
                null // Ensure events causing exceptions are filtered out
            }
        }.groupBy({ it.first }, { it.second })
    }

    private fun processEventsWithSameReportId(events: List<JpaReceivedEvent>, eventType: EventType) {
        log.info("Starting to processEventsWithSameReportId $eventType")
        var skipGroupDueToError = false
        val createExpensesRequestList = mutableListOf<CreateExpensesRequestWithReportId>()

        try {
            val expenseRequest = expenseProcessorService.generateExpenseRequestFromExpenseEvents(events, eventType)
            val externalExpenseId = expenseRequest.first!!

            if (eventType == EventType.RECORD_UPDATE && !expenseRequest.third) {
                // Fetch the first matching entity by externalId
                val entities = jpaEntityIntegrationRepository.findByExternalId(externalExpenseId)
                val entity = entities.firstOrNull()

                if (entity != null) {
                    val deleteRequest = BulkDeleteExpensesRequest.newBuilder()
                        .addExpenseIds(entity.internalId)
                        .build()

                    expenseProcessorService.processBulkRevokeExpenseRequest(deleteRequest)
                    log.info("Processed bulk delete request for non-approved expense with externalId: $externalExpenseId")

                    // Delete the entities from the database, ideally this should be handled using some column like isDeleted
                    entities.forEach { entity ->
                        log.info("Deleting entity:",  entity)
                        jpaEntityIntegrationRepository.delete(entity)
                    }
                    log.info("Deleted all matching entities for externalId: $externalExpenseId")

                } else {
                    log.warn("No matching entity found for externalId: $externalExpenseId")
                }
                return
            }
            createExpensesRequestList.add(expenseRequest)
        } catch (e: IntegrationIllegalStateException) {
            log.info("IllegalStateException caught while processing expense events", e)
            skipGroupDueToError = true
        } catch (e: IntegrationDownstreamException) {
            log.info("IntegrationDownstreamException caught while processing expense events", e)
            skipGroupDueToError = true
        } catch (e: Exception) {
            log.error("Error processing expense events", e)
            skipGroupDueToError = true
        }

        if (!skipGroupDueToError && createExpensesRequestList.isNotEmpty()) {
            expenseProcessorService.processBulkCreateExpenseRequest(createExpensesRequestList, eventType)
        }
        log.info("Completed processEventsWithSameReportId")
    }

    @Synchronized
    fun processTestEmployeeEvent(event:JsonNode, companyId: Long, legalEntityId: Long, countryCode: String) {
        log.info("Generating test employee event for " + event)
        val onboardingEmployeeData = generateOnboardingEmployeeData(event, companyId, legalEntityId, countryCode)
        val eventData: Map<String, Any> = jsonNodeToMap(event)

        val eventDataJsonNode: JsonNode = event.get("eventData")
        var profile : JsonNode = eventDataJsonNode.get("profile")

        val platformId = profile.get("id").textValue()

        log.info("This is the platform id " + platformId)

        val extendedOnboardingEmployeeData = onboardingEmployeeData + mapOf(
            "countryCode" to Country.CountryCode.valueOf(countryCode).name,
            "templateVersion" to GP_BULK_ONBOARDING_TEMPLATE_VERSION
        )
        val bulkOnboardingRequest = BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = legalEntityId,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.valueOf(countryCode),
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = extendedOnboardingEmployeeData)
        )

        val matchingIntegration = companyIntegrationRepository.findByCompanyIdIn(setOf(companyId)) ?: throw RuntimeException("Not found company integration")



        val validationResp = contractOnboardingServiceAdapter.validateBulkOnboarding(bulkOnboardingRequest)
        if (validationResp.isNotEmpty()) {
            log.error("Processing created employee with event ${event}, error: ${validationResp.first()}")
            return;
        }
        val response = contractOnboardingServiceAdapter.bulkOnboarding(bulkOnboardingRequest)


        log.info("Contract id of created contract " + response)

        if (response != null && response.isNotEmpty()) {

            log.info("Adding created contracts to createdContractIds" + response)
            createdContractIds.addAll(response)


            val contract = contractServiceAdapter.findContractByContractId(response[0])
            insertNewEmployeeIntoContractIntegration(eventData, contract, matchingIntegration[0],  platformId)
            matchingIntegration[0].id?.let { insertNewCreatedEmployeeToCache(eventData, it, platformId) }
        }

    }

    fun jsonNodeToMap(node: JsonNode): Map<String, Any> {
        val result = mutableMapOf<String, Any>()

        node.fields().forEach { (key, value) ->
            result[key] = when {
                value.isObject -> jsonNodeToMap(value) // Recurse if the node is an object
                value.isArray -> value.map { element ->
                    if (element.isObject) jsonNodeToMap(element)
                    else element.asText()
                } // Convert arrays
                value.isNull -> "" // Handle null by using an empty string or other placeholder
                else -> value.asText()
            }
        }
        return result
    }


    fun generateOnboardingEmployeeData(
        receivedEvent: JsonNode,
        companyId: Long?,
        legalEntityId: Long?,
        countryCode: String?
    ): Map<String, String> {

        val eventDataJsonNode: JsonNode = receivedEvent.get("eventData")
        var profile : JsonNode = eventDataJsonNode.get("profile")

        if (profile.get("firstName") == null) {
            return  mutableMapOf<String, String>();
        }

        val employeeData = mutableMapOf<String, String>().apply {
            put("bank.bankName", "E12345")
            put("firstName", profile.get("firstName").asText())
            put("lastName", profile.get("lastName").asText())
            put("email", profile.get("workEmail").asText())
            put("gender", "MALE")
            put("phoneNumber", "+************")
            put("address.line1", "Address Line 1")
            put("address.city", "Address City")
            put("address.country", "Vietnam")
            put("address.postalCode", "34000")
            put("position", "SDE")
            put("startOn", "2024-09-14")
            val randomEmployeeId = (1..10).map { ('0'..'9').random() }.joinToString("")
            put("employeeId", randomEmployeeId)
            put("rateFrequency", "MONTHLY")
            put("payrollFrequency", "MONTHLY")
            put("basePay", "5000")
            put("race", "Vietnamese")
            put("immigrationStatus", "Citizen")
            put("nid", "123321")
            put("bank.accountHolderName", "Account Holder Name")
            put("bank.address.country", "SG")
            put("bank.swiftCode", "VTCBVNVXXXX")
            put("bank.accountNumber", "***********")
        }
        return employeeData
    }

    @Synchronized
    fun processEmployeeEvent(
        event: JpaReceivedEvent,
        inviteMember: Boolean,
        errorList: MutableList<EmployeeValidationError>
    ): Boolean {
        val eventData = parseEventData(event).toMutableMap()
        val matchingIntegration = extractIntegrationFromEvent(event)
        val employeeId = extractDataFromPathAsString(eventData, "profile.id") ?: throw RuntimeException("Not found employee id")

        if (shouldSkipProcessing(employeeId, matchingIntegration.id!!)) {
            return false
        }

        // Fetch Hibob bank accounts if platform is Hibob
        if (matchingIntegration.platform.name.equals("Hibob", ignoreCase = true)) {
            val bankAccounts = knitAdapter.getHibobEmployeeBankAccounts(
                matchingIntegration.companyId,
                matchingIntegration.platform.id!!,
                event.identifiervalue ?: employeeId
            )
            if (bankAccounts.isNotEmpty()) {
                eventData["bankAccountsTable"] = objectMapper
                    .readValue(objectMapper.writeValueAsString(bankAccounts.first()))
                addToEventData(event, "bankAccountsTable", bankAccounts.first())
            }
        }

        val companyId = matchingIntegration.companyId
        val employeeCountry = extractCountry(eventData)
            ?: throw RuntimeException("Employee working country not found")
        val alpha3EmployeeCountry = mapToAlpha3CountryCode(employeeCountry, matchingIntegration.platform.name)
        event.entityCountry = alpha3EmployeeCountry

        val onboardingType: OnboardingType = getOnboardingType(eventData)
        val employeeLegalEntity = findEmployeeLegalEntity(companyId, alpha3EmployeeCountry)
        if (employeeLegalEntity == null) {
            handleMissingLegalEntity(event, onboardingType)
            return false
        }

        val onboardingEmployeeData = mapEmployeeData(
            eventData = eventData,
            companyIntegration = matchingIntegration,
            legalEntityId = employeeLegalEntity.id,
            onboardingType = onboardingType
        )
        val extendedOnboardingEmployeeData = onboardingEmployeeData + mapOf(
            "countryCode" to Country.CountryCode.valueOf("COUNTRY_CODE_$alpha3EmployeeCountry").name,
            "templateVersion" to GP_BULK_ONBOARDING_TEMPLATE_VERSION,
            "state" to (extractState(eventData) ?: ""),
        )

        var bulkOnboardingRequest = createBulkOnboardingRequest(
            companyId,
            alpha3EmployeeCountry,
            extendedOnboardingEmployeeData,
            onboardingType,
            employeeLegalEntity.id
        )
        bulkOnboardingRequest = contractOnboardingService.cleanBulkContractOnboardingRequest(
            bulkOnboardingRequest,
            Platform.fromString(matchingIntegration.platform.name)
        )
        if (featureFlagService.isMtmIntegration(
                matchingIntegration.accountToken,
                extractDataFromPathAsString(eventData, "profile.workEmail")
        )) {
            bulkOnboardingRequest = contractOnboardingService
                .cleanBulkContractOnboardingMtmRequest(bulkOnboardingRequest, alpha3EmployeeCountry)
        }

        if (!validateOnboardingRequest(
                bulkOnboardingRequest,
                event,
                errorList,
                extendedOnboardingEmployeeData,
                matchingIntegration
            )
        ) {
            return false
        }

        val response = contractOnboardingServiceAdapter.bulkOnboarding(bulkOnboardingRequest)
        if (response.isEmpty()) {
            return false
        }

        handleSuccessfulOnboarding(response.first(), inviteMember, eventData, matchingIntegration)

        return true
    }

    private fun getOnboardingType(eventData: Map<String, Any>): OnboardingType =
        if (eventData["compensation"] == null) OnboardingType.HRIS_PROFILE_DATA
        else OnboardingType.GLOBAL_PAYROLL

    private fun parseEventData(event: JpaReceivedEvent): Map<String, Any> {
        val receivedEvent = objectMapper.readTree(event.data)
        val eventDataJsonNode: JsonNode = receivedEvent.get("eventData")
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        return objectMapper.readValue(objectMapper.writeValueAsString(eventDataJsonNode))
    }

    private fun addToEventData(event: JpaReceivedEvent, key: String, value: Any) {
        try {
            val receivedEvent: Map<String, Any> = objectMapper.readValue(event.data!!)
            val newReceivedEvent = receivedEvent.toMutableMap()
            val eventData: Map<String, Any> = objectMapper
                .readValue(objectMapper.writeValueAsString(receivedEvent["eventData"]))
            val newEventData = eventData.toMutableMap()
            newEventData[key] = value
            newReceivedEvent["eventData"] = newEventData
            event.data = objectMapper.writeValueAsString(newReceivedEvent)
        } catch (e: Exception) {
            log.error("Error addToEventData for key=$key and value=${value}", e)
        }
    }

    private fun extractIntegrationFromEvent(event: JpaReceivedEvent): JpaCompanyIntegration {
        val integrationId = event.integrationId ?: throw RuntimeException("Not found knit integration id")
        val matchingIntegration = companyIntegrationRepository.findByAccountToken(integrationId) ?: throw RuntimeException("Not found company integration")
        return matchingIntegration
    }

    private fun shouldSkipProcessing(employeeId: String, integrationId: Long): Boolean {
        return employeeId.isBlank() || checkIfEmployeeAlreadyExistedInCache(employeeId, integrationId)
    }

    private fun findEmployeeLegalEntity(
        companyId: Long,
        alpha3EmployeeCountry: String,
    ): CompanyOuterClass.LegalEntity? {
        val legalEntities = newCompanyServiceAdapter.getLegalEntities(companyId)
        return legalEntities.firstOrNull {
            it.address.country == alpha3EmployeeCountry && it.hasCapabilities.globalPayroll
        }
    }

    private fun handleMissingLegalEntity(event: JpaReceivedEvent, onboardingType: OnboardingType) {
        event.isEntityEnabled = false
        throw RuntimeException("Not found employee legal entity for onboardingType $onboardingType")
    }

    private fun mapEmployeeData(
        eventData: Map<String, Any>,
        companyIntegration: JpaCompanyIntegration,
        legalEntityId: Long,
        onboardingType: OnboardingType,
    ): Map<String, String> {
        return employeeDataFieldMapper.mapEmployeeData(
            eventData = eventData,
            legalEntityId = legalEntityId,
            onboardingType = onboardingType,
            platformId = companyIntegration.platform.id!!,
            integrationId = companyIntegration.id!!,
            companyId = companyIntegration.companyId
        )
    }

    private fun createBulkOnboardingRequest(
        companyId: Long,
        country: String,
        employeeData: Map<String, String>,
        onboardingType: OnboardingType,
        legalEntityId: Long,
    ): BulkContractOnboardingRequest {
        val groupedData = compensationSchemaConfigService.groupEmployeeData(
            input = employeeData,
            entityId = legalEntityId,
        )

        return BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = legalEntityId,
            context = onboardingType,
            countryCode = Country.CountryCode.valueOf("COUNTRY_CODE_$country"),
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = groupedData
        )
    }

    private fun validateOnboardingRequest(bulkOnboardingRequest: BulkContractOnboardingRequest, event: JpaReceivedEvent, errorList: MutableList<EmployeeValidationError>, extendedOnboardingEmployeeData: Map<String, String>, matchingIntegration: JpaCompanyIntegration): Boolean {

        val validationResp = contractOnboardingServiceAdapter.validateBulkOnboarding(bulkOnboardingRequest)
        if (validationResp.isNotEmpty()) {
            handleValidationError(event, errorList, extendedOnboardingEmployeeData, validationResp.first(), matchingIntegration)
            return false
        }
        return true
    }

    private fun handleSuccessfulOnboarding(contractId: Long, inviteMember: Boolean, eventData: Map<String, Any>, matchingIntegration: JpaCompanyIntegration) {
        if (inviteMember) {
            inviteMember(contractId)
        } else {
            storePendingInvite(eventData, matchingIntegration.accountToken, contractId)
        }
        insertNewCreatedEmployeeToCache(eventData, matchingIntegration.id!!, null)
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        insertNewEmployeeIntoContractIntegration(eventData, contract, matchingIntegration, null)
    }

    private fun handleValidationError(
        event: JpaReceivedEvent,
        errorList: MutableList<EmployeeValidationError>,
        extendedOnboardingEmployeeData: Map<String, String>,
        errorMessage: String,
        matchingIntegration: JpaCompanyIntegration
    ) {
        log.warn("Processing created employee with eventId ${event.id}, employeeId ${event.identifiervalue} error: $errorMessage")
        event.errors = errorMessage
        val validationError = EmployeeValidationError(
            error = errorMessage,
            errorList.size,
            "",
            employeeName = "${extendedOnboardingEmployeeData["firstName"]} ${extendedOnboardingEmployeeData["lastName"]}",
            companyId = matchingIntegration.companyId,
            hrMemberDataInput = extendedOnboardingEmployeeData,
            integrationId = event.integrationId!!,
            entityCountry = event.entityCountry
        )
        errorList.add(validationError)

        if (isDuplicateProfileError(errorMessage, extendedOnboardingEmployeeData)) {
            handleDuplicateProfile(extendedOnboardingEmployeeData, matchingIntegration)
        }
    }

    private fun isDuplicateProfileError(errorMessage: String, extendedOnboardingEmployeeData: Map<String, Any>): Boolean {
        return errorMessage.contains("${extendedOnboardingEmployeeData["email"]} is already used", ignoreCase = true) ||
                errorMessage.contains("${extendedOnboardingEmployeeData["workEmail"]} already exists", ignoreCase = true)
    }

    private fun handleDuplicateProfile(extendedOnboardingEmployeeData: Map<String, Any>, matchingIntegration: JpaCompanyIntegration) {
        val member = memberServiceAdapter.findMemberByEmailAddress(extendedOnboardingEmployeeData["email"].toString())
        val contract = contractServiceAdapter.findContractByMemberId(member.id)
        insertNewEmployeeIntoContractIntegration(extendedOnboardingEmployeeData, contract, matchingIntegration, null)
        insertNewCreatedEmployeeToCache(extendedOnboardingEmployeeData, matchingIntegration.id!!, null)
    }
    
    private fun isValidDate(dateStr: String): Boolean {
        val regex = Regex("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$")
        return regex.matches(dateStr)
    }

    private fun getBankDetailFromStaticFields(eventData: EventData): Map<String, String> {
        val bankAccount = getPrioritizedBankAccount(eventData) ?: return emptyMap()

        log.info("Using static fields for bank details of employee: ${eventData.profile?.id}")

        return mapOf(
            "$BANK_DATA_SPEC_PREFIX.accountHolderName" to "${eventData.profile?.firstName.orEmpty()}${eventData.profile?.lastName.orEmpty()}",
            "$BANK_DATA_SPEC_PREFIX.accountNumber" to bankAccount.accountNumber.orEmpty(),
            "$BANK_DATA_SPEC_PREFIX.clabe" to bankAccount.accountNumber.orEmpty(),
            "$BANK_DATA_SPEC_PREFIX.bankName" to bankAccount.bankName.orEmpty(),
            "$BANK_DATA_SPEC_PREFIX.bankBranch" to bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.BRANCH_CODE }?.number.orEmpty(),
            "$BANK_DATA_SPEC_PREFIX.swiftCode" to bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.SWIFT_CODE }?.number.orEmpty(),
            "$BANK_DATA_SPEC_PREFIX.localBankCode" to bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.BANK_IDENTIFICATION_CODE }?.number.orEmpty(),
            "$BANK_DATA_SPEC_PREFIX.country" to eventData.locations?.workAddress?.country.orEmpty()
        )
    }

    private fun getBankDetailFromDynamicFields(eventData: EventData, requirement: IntegrationPaymentAccountRequirement): Map<String, String> {
        val bankAccount = getPrioritizedBankAccount(eventData) ?: return emptyMap()
        val requirementFields = requirement.accountRequirementsList ?: return emptyMap()

        log.info("Using dynamic fields for bank details of employee: ${eventData.profile?.id}")

        val bankDetails = mutableMapOf<String, String>()

        requirementFields.forEach { field ->
            val key = field.requirementKey
            val mappedKey = field.mappedKey

            val value = when (mappedKey) {
                "accountHolderName" -> "${eventData.profile?.firstName.orEmpty()}${eventData.profile?.lastName.orEmpty()}"
                "accountNumber", "clabe" -> bankAccount.accountNumber.orEmpty()
                "bankName" -> bankAccount.bankName.orEmpty()
                "branchName", "branchCode" -> bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.BRANCH_CODE }?.number.orEmpty()
                "swiftCode" -> bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.SWIFT_CODE }?.number.orEmpty()
                "localBankCode" -> bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.BANK_IDENTIFICATION_CODE }?.number.orEmpty()
                "ifscCode" -> bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.IFSC_CODE }?.number.orEmpty()
                "iban" -> bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.IBAN }?.number.orEmpty()
                "routingNumber" -> bankAccount.routingInfo?.firstOrNull { it.type == RoutingType.ROUTING_NUMBER }?.number.orEmpty()
                "country" -> eventData.locations?.workAddress?.country.orEmpty()
                else -> eventData.customFields?.fields?.get(mappedKey)?.toString().orEmpty()
            }

            log.info("Mapped key: $mappedKey, Value: $value")
            bankDetails["$BANK_DATA_SPEC_PREFIX.$key"] = value
        }

        return bankDetails
    }

    private fun getPrioritizedBankAccount(eventData: EventData): BankAccount? {
        val prioritizedBankAccount = eventData.bankAccounts
            ?.sortedWith(compareBy(
                { it.payTypes?.contains(PayType.ALL) == true },
                { it.payTypes?.contains(PayType.REGULAR) == true }
            ))
            ?.firstOrNull()

        log.info("Prioritized bank account selected: $prioritizedBankAccount for employee: ${eventData.profile?.id}")

        return prioritizedBankAccount
    }

    fun handleBankUpdate(contractId: Long, eventData: EventData, country: String) {
        log.info("Handling bank update for contractId=$contractId")

        val request = IntegrationPaymentAccountRequirementsRequest.newBuilder()
            .setAccountType(AccountType.PERSONAL)
            .setTransferType(TransferType.FIAT)
            .setSourceCurrency(eventData.compensation?.fixed?.firstOrNull { it.type == CompensationType.SALARY.name }?.currency.orEmpty())
            .setTargetCurrency(eventData.compensation?.fixed?.firstOrNull { it.type == CompensationType.SALARY.name }?.currency.orEmpty())
            .setCountryCode(country)
            .setPaymentDirection(PaymentDirection.PAY_OUT)
            .addPaymentPartners(PaymentPartner.HSBC)
            .setIntegrationPartner(IntegrationPartner.KNIT)
            .build()

        log.info("Request created: $request")

        var paymentRequirements: IntegrationPaymentAccountRequirements? = null

        try {
            log.info("Fetching payment requirements for request: $request")
            paymentRequirements = paymentServiceAdapter.getIntegrationPaymentAccountRequirements(request)
            log.info("Payment requirements fetched successfully for employee: ${eventData.profile?.id}")
        } catch (e: Exception) {
            log.error("Error getting payment requirements for employee: ${eventData.profile?.id}", e)
        }

        val bankDetails: Map<String, String>

        if (paymentRequirements == null || paymentRequirements.requirementsList.isNullOrEmpty()) {
            log.warn("No payment requirements found, using static fields for employee: ${eventData.profile?.id}")
            bankDetails = getBankDetailFromStaticFields(eventData)
        } else {
            log.info("Using dynamic fields for employee: ${eventData.profile?.id}")
            bankDetails = getBankDetailFromDynamicFields(eventData, paymentRequirements.requirementsList.first())
        }

        log.info("Bank details to be updated: $bankDetails")

        val upsertBankDetailsRequest = UpsertBankDetailsRequest.newBuilder()
            .addInputs(
                UpsertBankDetailsInput.newBuilder()
                    .setRequestId(eventData.profile?.id)
                    .setMemberId(contractId)
                    .setStaticDetail(
                        MemberBankStaticDetail.newBuilder()
                            .setAccountHolderName(bankDetails["accountHolderName"].orEmpty())
                            .setAccountNumber(bankDetails["accountNumber"].orEmpty())
                            .setBankName(bankDetails["bankName"].orEmpty())
                            .setBankBranch(bankDetails["branchName"].orEmpty())
                            .setSwiftCode(bankDetails["swiftCode"].orEmpty())
                            .setLocalBankCode(bankDetails["localBankCode"].orEmpty())
                            .build()
                    )
                    .setDynamicDetail(
                        MemberBankDynamicDetail.newBuilder()
                            .setMemberId(contractId)
                            .setPaymentAccountRequirementType("")
                            .setPaymentAccountType(PaymentAccountType.PERSONAL)
                            .setTransferType(com.multiplier.member.schema.TransferType.FIAT)
                            .setSourceCurrency(eventData.compensation?.fixed?.firstOrNull { it.type == CompensationType.SALARY.name }?.currency.orEmpty())
                            .setTargetCurrency(eventData.compensation?.fixed?.firstOrNull { it.type == CompensationType.SALARY.name }?.currency.orEmpty())
                            .putAllDetailFields(bankDetails)
                            .build()
                    )
                    .build()
            )
            .build()

        try {
            log.info("Upserting bank details for memberId: ${eventData.profile?.id}")
            memberServiceAdapter.upsertBankDetails(upsertBankDetailsRequest)
            log.info("Bank details upserted successfully for memberId: ${eventData.profile?.id}")
        } catch (e: Exception) {
            log.error("Error upserting bank details for memberId: ${eventData.profile?.id}", e)
        }
    }

    private fun checkIfEmployeeAlreadyExistedInCache(employeeId: String, integrationId: Long): Boolean {
        try {
            log.info("Checking if employeeId=$employeeId with integrationID=$integrationId already existed in cache")
            val existedEmployeeData = platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                integrationId,
                employeeId
            ).firstOrNull()
            return existedEmployeeData != null
        } catch (e: Exception) {
            log.error("Error checking employee existed with ID: $employeeId", e)
            return false
        }
    }

    @Async
    fun sendInvites(syncId: String, currentUserCompanyId: Long?, isOpsUser: Boolean = false) {
        log.info("Sending invites for syncId " + syncId)

        var integrationId = syncRepository.findBySyncId(syncId).get().integrationId
        if (integrationId == null) {
            throw RuntimeException("Invalid sync provided")
        }
        var integration =  integrationRepository.findByAccountToken(integrationId)
        integration ?: throw BadRequestException("Integration not found for syncId: $syncId")

        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        val pendingEmployees = pendingEmployeeRepository.findByIntegrationIdAndInviteRequested(integrationId, false)
        pendingEmployeeRepository.setInvitationRequestedForAll(integrationId = integration.accountToken, inviteRequested = true)
        try {
            for (employee in pendingEmployees) {
                val success = inviteMember(employee.contractId!!)
                if (success) {
                    pendingEmployeeRepository.setPendingInviteByIdentifier(employee.identifier!!, false)
                }
            }
        } catch (e: Exception) {
            log.error("Error while sending invites for integrationId=${integrationId}", e)
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException("Error while sending invites for integrationId=${integrationId}", e)
        }
    }

    private fun storePendingInvite(eventData : Map<String, Any>, integrationId: String, contractId: Long) {
        if (extractDataFromPathAsString(eventData, "profile") == null) {
            return
        }
        val pendingEmployee = JpaPendingEmployee(integrationId, null, extractDataFromPathAsString(eventData, "profile.id"),
            extractDataFromPathAsString(eventData, "profile.firstName"), extractDataFromPathAsString(eventData, "profile.lastName"),
            false, true, contractId, inviteRequested = false)
        pendingEmployeeRepository.save(pendingEmployee)
    }

    @Async
    fun inviteMember(contractId: Long) : Boolean {
        log.info("inviteMember running on ${Thread.currentThread().getName()}")
        val inviteRequest = BulkContract.InviteBulkMemberContractsRequest.newBuilder()
            .addContractIds(contractId).build()
        val inviteResponse = contractServiceAdapter.inviteBulkMemberContracts(inviteRequest)
        return inviteResponse.successful
    }

    fun startManualSync(integrationId: Long, currentUserCompanyId: Long?, category: PlatformCategory, isOpsUser: Boolean = false) {
        var integration = integrationRepository.findById(integrationId).get()
        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        var accountToken = integration.accountToken
        integration.incomingSyncInProgress = true
        if (category == PlatformCategory.EXPENSES) {
            integration.incomingSyncEnabled = true
        }
        integrationRepository.save(integration)
        knitAdapter.startSync(accountToken, category) ?: throw RuntimeException("Could not initiate sync")
    }

    private fun startSyncAndWaitUntilFinished(integrationId: String, category: PlatformCategory) {
        val integration = integrationRepository.findByAccountToken(integrationId) ?: throw NoSuchElementException("Integration not found for account token: $integrationId")
        val syncJobId = knitAdapter.startSync(integrationId, category)
        if (syncJobId == null) {
            throw RuntimeException("Could not initiate sync")
        }
        integration.incomingSyncInProgress = true
        integrationRepository.save(integration)
        val syncOver = waitForSyncToFinish(integrationId)
        if (!syncOver) {
            throw RuntimeException("Sync is not over.")
        }
    }

    fun getEmployeesToSync(integrationId: Long, currentUserCompanyId: Long?, isOpsUser: Boolean = false) : FetchEmployeesResult {
        var integration = integrationRepository.findById(integrationId).get()
        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        val token = integration.accountToken
        val syncId = syncRepository.findSyncOrderByStartTime(token).getOrNull(0)?.syncId

        if (syncId != null) {
            jpaReceivedEventRepository.setMatchingSyncToProvidedSyncForUnprocessedEventsForGivenIntegration(
                token,
                syncId
            )
        }

        val matchingEvents = jpaReceivedEventRepository.findBySyncIdAndAndProcessed(syncId, false)

        val employees = mutableListOf<Employee>()
        for (matchingEvent in matchingEvents) {
            if (matchingEvent.eventType != EventType.RECORD_NEW) {
                continue
            }
            val eventData = DataMapper.objectMapper.readTree(matchingEvent.data)
            val profileData = eventData.get("eventData").get("profile")
            val employee = Employee()
            employee.firstName = profileData.get("firstName").textValue()
            employee.lastName = profileData.get("lastName").textValue()
            employee.id = Random.nextLong()
            employee.externalId = profileData.get("id").textValue()
            employees.add(employee)
        }
        val result = FetchEmployeesResult()
        result.employees = employees
        result.syncId = syncId
        removeManualSyncInProgress(integration)
        return result;

    }

    fun waitForSyncToFinish(integrationId: String, maxAttempts: Int = 300): Boolean {
        var attempts = 0

        while (attempts < maxAttempts) {

            val syncOver = syncRepository.findByIntegrationIdAndInProgress(integrationId,
                false)

            if (syncOver == null || syncOver.isEmpty()) {
                // Sync is still in progress, wait for 1 second
                Thread.sleep(1000)
                attempts++
            } else {
                // Sync is over, return true
                return true
            }
        }

        // Sync is not over after the maximum number of attempts, return false
        return false
    }

    @Async
    fun manuallyImportEmployees(syncId : String, currentUserCompanyId: Long?, isOpsUser: Boolean = false) {
        log.info("Starting manual import of employees for syncId: {}, currentUserCompanyId: {} ", syncId, currentUserCompanyId)
        var matchingSync = syncRepository.findBySyncId(syncId).get()
        var integration =  integrationRepository.findByAccountToken(matchingSync.integrationId!!)

        integration ?: throw BadRequestException("Integration not found for syncId: $syncId")

        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        integration.incomingSyncEnabled = true
        integration.importInProgress = true
        integration.lastIncomingSyncTime = LocalDateTime.now()
        integrationRepository.save(integration)

        var eventsToSync = jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(EventType.RECORD_NEW,
            false, syncId).toMutableList()

        val updateEvents = jpaReceivedEventRepository.findByEventTypeAndProcessedAndSyncId(EventType.RECORD_UPDATE,
            false, syncId)

        //Check if any of the update events relates to an employee that is not yet in cache. if yes,
        //treat it as a create event and add it to events to sync
        for (event in updateEvents) {
            val jpaPlatformEmployeeData = platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                integration.id!!,
                event.identifiervalue!!
            )
            if (jpaPlatformEmployeeData.isNullOrEmpty()) {
                event.eventType = EventType.RECORD_NEW
                eventsToSync.add(event)
            }
        }

        log.info("There are {} events to sync for syncId {}", eventsToSync.size, syncId)
        val errorList = mutableListOf<EmployeeValidationError>();

        for (event in eventsToSync) {
            Thread.sleep(1000)
            try {
                log.info("Start processing event for eventId: ${event.eventId}")
                this.processEmployeeEvent(event, false, errorList)
            } catch (e: Exception) {
                log.error("Error processing event for eventId: ${event.eventId}", e)
            } finally {
                log.info("Finalizing processing event for eventId: ${event.eventId}")
                event.processed = true
                jpaReceivedEventRepository.save(event)
            }
        }
        integration.importInProgress = false
        companyIntegrationRepository.save(integration)

        // Send mail to company admins that import is completed
        notificationsService.sendAdminEmployeesImportCompleted(
            integration.companyId, integration.platform.name
        )
    }

    fun insertNewEmployeeIntoContractIntegration(
        eventData: Map<String, Any>,
        contract: ContractOuterClass.Contract,
        companyIntegration: JpaCompanyIntegration,
        platformEmployeeId: String?
    ) {
        log.info("Adding employee to platform_contract_integration table, contractId: ${contract.id}, employeeId: ${extractDataFromPathAsString(
            eventData,
            "profile.id"
        )}")
        if (contract.id == 0L) {
            throw NoSuchElementException("No contract found in bulkContract response, skipping creating entry in platform_contract_integration table")
        }


        val jpaPlatformContractIntegration = JpaPlatformContractIntegration(
            contractId = contract.id,
            providerId = companyIntegration.provider.id ?: throw IntegrationIllegalStateException("Provider ID is null"),
            provider = companyIntegration.provider,
            platformId = companyIntegration.platform.id ?: throw IntegrationIllegalStateException("Platform ID is null"),
            platform = companyIntegration.platform,
            platformEmployeeId = platformEmployeeId ?: extractDataFromPathAsString(eventData, "profile.id")!!,
            integrationId = companyIntegration.id!!,
            remoteId = "remoteId" // Drop this column
        )

        platformContractIntegrationRepository.save(jpaPlatformContractIntegration)
    }

    fun insertNewCreatedEmployeeToCache(eventData: Map<String, Any>, integrationId: Long, platformEmployeeId: String?) {
        val employeeId = platformEmployeeId?: extractDataFromPathAsString(eventData, "profile.id")
        log.info("Insert new employee data to cache for employeeId=$employeeId")
        if (employeeId.isNullOrBlank()) {
            return
        }

        try {
            val newEmployeeData = EmployeeData(
                profile = Profile(
                    firstName = extractDataFromPathAsString(eventData, "profile.firstName"),
                    lastName = extractDataFromPathAsString(eventData, "profile.lastName"),
                    workEmail = extractDataFromPathAsString(eventData, "profile.workEmail"),
                    id = employeeId,
                ),
                compensation = extractDataFromPathAsString(eventData, "compensation")?.toEmployeeCompensationData(),
                bankAccounts = listOf(
                    BankAccountDetails(
                        accountNumber = extractDataFromPathAsString(eventData, "bankAccounts[0].accountNumber"),
                        accountType = extractDataFromPathAsString(eventData, "bankAccounts[0].accountType.name"),
                        bankName = extractDataFromPathAsString(eventData, "bankAccounts[0].bankName"),
                    )
                ),
                locations = Locations(
                    // setting these two to null since we are not tracking them now
                    workAddress = null,
                    permanentAddress = null,
                    presentAddress = EmployeeLocation(
                        addressLine1 = extractDataFromPathAsString(eventData, "locations.presentAddress.addressLine1"),
                        addressLine2 = extractDataFromPathAsString(eventData, "locations.presentAddress.addressLine2"),
                        city = extractDataFromPathAsString(eventData, "locations.presentAddress.city"),
                        state = extractDataFromPathAsString(eventData, "locations.presentAddress.state"),
                        country = extractDataFromPathAsString(eventData, "locations.presentAddress.country"),
                        zipCode = extractDataFromPathAsString(eventData, "locations.presentAddress.zipCode"),
                        addressType = extractDataFromPathAsString(eventData, "locations.presentAddress.addressType"),
                    )
                )
            )
            val newPlatformEmployeeData = JpaPlatformEmployeeData(
                employeeId = employeeId,
                employeeData = DataMapper.objectMapper.writeValueAsString(newEmployeeData),
                integrationId = integrationId, //PETAR - are you sure you want to use this here
                //In almost all cases, we are using integrationId as defined by Knit, which is the
                //account token value in the company integration table
                origin = EmployeeOrigin.EXTERNAL.name,
                isDeleted = false
            )
            platformEmployeeDataRepository.save(newPlatformEmployeeData)
        } catch (e: Exception) {
            log.error("Error while inserting new employee data to cache for employeeId=$employeeId and companyIntegrationId=${integrationId}", e)
        }
    }

    @Transactional
    fun processDeletedEvent(event: JpaReceivedEvent) {
        log.info("Processing event with eventId=${event.eventId} and integrationId=${event.integrationId}")

        val eventData: EventData = dataMapper.map(event)

        val employeeId = eventData.employeeId

        log.info("Finding contractId for platformEmployeeId=${employeeId}")

        val (companyIntegration, platformContractIntegration) = customerIntegrationService.findPlatformContractIntegrationFromEvent(
            event,
            employeeId!!
        )
        if (platformContractIntegration == null) {
            throw jakarta.persistence.EntityNotFoundException("Contract not found for eventId=${event.eventId} and platformEmployeeId=${employeeId}")
        }
        val contractId = platformContractIntegration.contractId

        val origin = employeeService.getEmployeeDataOrigin(contractId)
        if (origin == EmployeeOrigin.INTERNAL) {
            log.info("Employee origin is EOR, skipping offboard employee")
            return
        }
        if (!eventData.isRecordMissing!! && eventData.profile?.employmentStatus != EmploymentStatus.INACTIVE) {
            throw IntegrationInternalServerException("Can not terminate employee who are still in Active status")
        }
        val terminationDate = getTerminationDate(event)
            ?: throw EntityNotFoundException("Not found terminationDate for platformEmployeeId=${employeeId}")

        val internalEmployeeData = getCachedEmployeeData(employeeId!!, companyIntegration)
        completeOffboardingEmployee(contractId, terminationDate)
        deleteCachedEmployeeData(event, internalEmployeeData, terminationDate)
    }

    private fun getTerminationDate(event: JpaReceivedEvent): Date? {
        return try {
            val receivedEvent = DataMapper.objectMapper.readTree(event.data)
            val eventDataJsonNode: JsonNode = receivedEvent["eventData"]
            eventDataJsonNode.get("terminationDate").asText().toDate()
        } catch (e: Exception) {
            return null
        }
    }

    private fun completeOffboardingEmployee(
        contractId: Long,
        terminationDate: Date,
    ) {
        log.info("Handle complete offboarding employee: contractId=${contractId}, lastWorkingDay=${terminationDate}, ${convertDateToLocalDate(terminationDate)}")
        val contractOffboardingId: Long
        val contractOffboardings = contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId))
        if (contractOffboardings.isEmpty()) {
            val offboardingResponse = contractOffboardingServiceAdapter.initialiseResignationOffboarding(
                ContractOffBoardingRequest(
                    contractId = contractId,
                    lastWorkingDay = terminationDate.convertDateToLocalDate().toString(),
                    terminationReason = "Terminated by external platform"
                )
            )
            log.info("Successfully init offboarding with contractId=${contractId}, offboardingStatus=${offboardingResponse.contractOffBoardingStatus}")
            contractOffboardingId = offboardingResponse.id
        }
        else {
            val existedContractOffboarding = contractOffboardings.find { it.contractId == contractId } ?: throw IntegrationInternalServerException("Not found contract offboarding with contractId $contractId")
            contractOffboardingId = existedContractOffboarding.id
        }
        contractOffboardingServiceAdapter.verifyAndCompleteOffboarding(contractOffboardingId)
        log.info("Successfully complete offboarding with contractOffboardingId=${contractOffboardingId}")
    }

    private fun getCachedEmployeeData(
        employeeId: String,
        companyIntegration: JpaCompanyIntegration,
    ): JpaPlatformEmployeeData {
        log.info("Fetching cached employee data for platformEmployeeId=${employeeId}, integrationid=${companyIntegration.id!!}")
        val internalEmployeeData =
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                companyIntegration.id!!,
                employeeId
            ).firstOrNull()
                ?: throw EntityNotFoundException("Cache employee data not found for platformEmployeeId=${employeeId}")
        return internalEmployeeData
    }

    private fun deleteCachedEmployeeData(
        event: JpaReceivedEvent,
        internalEmployeeData: JpaPlatformEmployeeData,
        terminationDate: Date,
    ) {
        val employeeData = dataMapper.map(internalEmployeeData.employeeData)
        val internalEmployeeProfile = employeeData.profile
        val newInternalEmployeeDataProfile = internalEmployeeProfile?.copy(
            terminationDate = terminationDate.toString(),
        )
        log.info("Delete cache employee data with eventId=${event.eventId} and employeeId=${internalEmployeeData.employeeId}")
        internalEmployeeData.employeeData = DataMapper.objectMapper
            .writeValueAsString(employeeData.copy(profile = newInternalEmployeeDataProfile))
        internalEmployeeData.isDeleted = true
        platformEmployeeDataRepository.save(internalEmployeeData)
        log.info("Success deleting cache employee data with eventId=${event.eventId} for employeeId=${internalEmployeeData.employeeId}")
    }

    private fun convertDateToLocalDate(date: Date): LocalDate {
        val instant = date.toInstant()
        val zonedDateTime = instant.atZone(ZoneId.systemDefault())
        return zonedDateTime.toLocalDate()
    }
}

// This should be a data class, please refactor it. Please!!
typealias CreateExpensesRequestWithReportId = Triple<String?, CreateExpensesRequest, Boolean>
