package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.Field
import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.adapter.api.resources.knit.FieldValues
import com.multiplier.integration.adapter.api.resources.knit.GetFieldValuesResponse
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.ExternalPlatformValuesRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository

import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaExternalPlatformValues
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.model.JpaReceivedEventArchive
import com.multiplier.integration.repository.model.LegalMappingStatus
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.model.MaritalStatus
import com.multiplier.integration.types.Company
import com.multiplier.integration.types.FieldMappingV2
import com.multiplier.integration.types.Gender
import com.multiplier.integration.types.IntegrationEntityMappingStatusOutput
import com.multiplier.integration.types.IntegrationFieldsMappingContractorOutput
import com.multiplier.integration.types.IntegrationFieldsMappingOutputV2
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.types.UnmappedField
import com.multiplier.integration.utils.formatExternalKey
import com.multiplier.integration.utils.mapPlatformIdToKnitAppId
import com.multiplier.integration.utils.populateKeyFromLabel
import com.multiplier.integration.utils.populateLabelFromKey
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.LocalDateTime
import java.util.*

@Service
class FieldMappingService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val knitAdapter: KnitAdapter,
    private val receivedEventRepository: ReceivedEventRepository,
    private val newCompanyServiceAdapter: NewCompanyServiceAdapter,
    private val contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter,
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val externalPlatformValuesRepository: ExternalPlatformValuesRepository,
    private val receivedEventsArchiveRepository: ReceivedEventsArchiveRepository,
    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter,
) {

    private val log = KotlinLogging.logger {}
    private val objectMapper = jacksonObjectMapper()
    private val dataMapper = DataMapper()

    fun formatUnmappedFields(
        updatedExternalPlatformValues: List<JpaExternalPlatformValues>,
        thirdPartyFields: List<UnmappedField>,
        integration: JpaCompanyIntegration,
    ): List<UnmappedField> {
        val externalKeyToValuesMap = updatedExternalPlatformValues.associate {
            it.fieldId to it.values
        }
        val unmappedFields = thirdPartyFields.map {
            val updatedKey = if (!it.isMappedByThirdParty) it.key.populateKeyFromLabel(it.label) else it.key
            val childrenMapping = externalKeyToValuesMap[it.fieldId]?.map { child ->
                val mappedValue = objectMapper.readValue(child, FieldValues::class.java)
                val childKey =
                    if (it.isCustomField && integration.platform.isSpecialEnum) mappedValue.id else mappedValue.label
                dataMapper.map(
                    key = childKey,
                    label = mappedValue.label,
                    isMappedByThirdParty = it.isMappedByThirdParty,
                    fieldId = null,
                    type = "STRING",
                    fieldFromApp = null,
                    isCustomField = it.isCustomField
                ).build()
            }
            dataMapper.map(
                key = updatedKey,
                label = it.label.populateLabelFromKey(it.key?.replace("customFields.fields.", "")),
                isMappedByThirdParty = it.isMappedByThirdParty,
                fieldId = it.fieldId,
                type = it.type,
                fieldFromApp = it.fieldFromApp,
                isCustomField = it.isCustomField
            ).apply {
                if (childrenMapping != null) {
                    children(childrenMapping)
                }
            }.build()
        }
        return handleExternalFieldWithSubFields(unmappedFields)
    }
    fun getKnitFields(
        integrationId: Long,
        companyId: Long,
        integration: JpaCompanyIntegration,
    ): List<FieldData> {
        log.info("Fetching data specs from KNIT for integrationId $integrationId")
        val platformKeys = runBlocking {
            knitAdapter.getAllFields(
                companyId,
                integration.platform.id!!,
                mapPlatformIdToKnitAppId(integration.platform.name)
            )
        }
        val externalFields = buildList {
            platformKeys.data?.run {
                default?.let { addAll(it) }
                listOfNotNull(mapped, unmapped)
                    .flatten()
                    .onEach { it.isCustomField = true }
                    .let { addAll(it) }
            }
        }
        return externalFields
    }

    private fun getOnboardDataSpecs(
        matchedLegalEntity: CompanyOuterClass.LegalEntity?,
        companyId: Long,
    ): List<BulkOnboardDataSpec> {
        if (matchedLegalEntity == null) {
            log.info("Fetching data specs for contractor field mapping")
            val getOnboardDataSpecsRequest = FieldRequirementsRequest.newBuilder()
                .setUseCase("FREELANCER_MEMBER_ONBOARDING")
                .setCompanyId(companyId)
                .build()
            return contractOnboardingServiceAdapter.getFieldRequirements(getOnboardDataSpecsRequest)
        }
        log.info("Fetching data specs from onboarding service for legal entity ${matchedLegalEntity.legalName}")
        val getOnboardDataSpecsRequest = BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = matchedLegalEntity.id!!,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.valueOf("COUNTRY_CODE_${matchedLegalEntity.address.country}"),
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = emptyMap())
        )
        return contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(getOnboardDataSpecsRequest)
    }

    private fun handleExternalFieldWithSubFields(externalFields: List<UnmappedField>): List<UnmappedField> {
        // handle for compensation first, need to update for general case
        val typeField = externalFields.find { it.key == "compensation.variable[0].type" }
        val planIdField = externalFields.find { it.key == "compensation.variable[0].planId" }
        return externalFields.map {
            if (it.key in listOf(
                    "compensation.variable[0].amount",
                    "compensation.variable[0].payPeriod",
                    "compensation.variable[0].frequency"
                )
            ) {
                it.apply {
                    subFields = listOf(typeField, planIdField)
                }
            }
            it
        }
    }

    fun formatKnitFields(externalFields: List<FieldData>?): List<UnmappedField> {
        if (externalFields.isNullOrEmpty()) {
            return emptyList()
        }
        return externalFields.map {
            val isMapped = it.mappedKey != null
            val formatWithCustomFieldKey = formatExternalKey(it, isMapped)
            dataMapper.map(
                formatWithCustomFieldKey,
                it.label,
                isMapped,
                it.fieldId,
                it.dataType,
                it.fieldFromApp,
                it.isCustomField ?: false
            ).build()
        }
    }

    fun getIntegrationLegalEntityMappings(integrationId: Long): List<IntegrationEntityMappingStatusOutput> {
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
        // Fetch existed entity mappings
        var entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        val entityMappingIds = entityMappings.map { it.entityId }.toMutableSet()
        log.info("Fetching legal entities for company ${integration.companyId}")
        val legalEntities = newCompanyServiceAdapter.getLegalEntities(integration.companyId)
        val newLegalEntities = mutableListOf<JpaLegalEntityMapping>()
        val entityIdToLegalEntity = mutableMapOf<Long, CompanyOuterClass.LegalEntity>()
        for (legalEntity in legalEntities) {
            if (legalEntity.id !in entityMappingIds) {
                log.info("Found new legal entity need to be added ${legalEntity.legalName}")
                newLegalEntities.add(
                    JpaLegalEntityMapping(
                        entityId = legalEntity.id,
                        entityName = legalEntity.legalName,
                        companyId = integration.companyId,
                        status = LegalMappingStatus.UNMAPPED,
                        isEnabled = false,
                        integrationId = integrationId,
                        entityCountry = legalEntity.address.country
                    )
                )
            }
            entityIdToLegalEntity[legalEntity.id] = legalEntity
        }
        if (newLegalEntities.isNotEmpty()) {
            legalEntityMappingRepository.saveAll(newLegalEntities)
            entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        }
        return entityMappings.map {
            val legalEntityObj = entityIdToLegalEntity[it.entityId]
            val legalEntityMapped = dataMapper.map(legalEntityObj)
            IntegrationEntityMappingStatusOutput.newBuilder()
                .entityMappingId(it.id)
                .integrationId(integrationId)
                .isEnabled(it.isEnabled)
                .legalEntity(legalEntityMapped)
                .entityMappingStatus(com.multiplier.integration.types.LegalMappingStatus.valueOf(it.status.name))
                .company(
                    Company.newBuilder()
                        .id(it.companyId)
                        .build()
                )
                .build()
        }
    }

    @Transactional
    fun saveIntegrationEntityMappingStatus(entityMappingId: Long, enableDataSync: Boolean = true): TaskResponse {
        val legalEntityMapping = legalEntityMappingRepository.findById(entityMappingId).orElseThrow {
            EntityNotFoundException(
                "Not found legal entity mapping with id = $entityMappingId"
            )
        }
        legalEntityMapping.isEnabled = enableDataSync

        if (!enableDataSync) {
            legalEntityMappingRepository.save(legalEntityMapping)
            return TaskResponse.newBuilder()
                .success(true)
                .message("Successfully updated status")
                .build()
        }

        if (legalEntityMapping.status != LegalMappingStatus.FULLY_MAPPED) {
            return TaskResponse.newBuilder()
                .success(false)
                .message("Need to map all fields to enable sync for this legal entity")
                .build()
        }

        legalEntityMappingRepository.save(legalEntityMapping)
        // Update old events failed for not existed legal entity mapping to be processed again
        val integration = companyIntegrationRepository.findById(legalEntityMapping.integrationId)
            .orElseThrow { IllegalStateException("Integration not found for integrationId=${legalEntityMapping.integrationId}") }
        if (integration.enabled && integration.incomingSyncEnabled) {
            val receivedEvents =
                receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                    integration.accountToken,
                    legalEntityMapping.entityCountry,
                    isEntityEnabled = false,
                    processed = true
                )
            val updatedReceivedEvents = mutableListOf<JpaReceivedEvent>()
            val archivedReceivedEvents =
                receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                    integration.accountToken,
                    legalEntityMapping.entityCountry,
                    isEntityEnabled = false,
                    processed = true
                )
            updatedReceivedEvents.addAll(receivedEvents.map {
                it.isEntityEnabled = true
                it.processed = false
                it
            })
            if (archivedReceivedEvents.isNotEmpty()) {
                updatedReceivedEvents.addAll(restoreArchivedReceivedEventsForEntityRetry(archivedReceivedEvents))
                receivedEventsArchiveRepository.deleteAll(archivedReceivedEvents)
            }
            if (updatedReceivedEvents.isNotEmpty()) {
                receivedEventRepository.saveAll(updatedReceivedEvents)
            }
        }
        return TaskResponse.newBuilder()
            .success(true)
            .message("Successfully updated status")
            .build()
    }

    private fun getDepartmentFieldValues(
        integration: JpaCompanyIntegration,
        fieldId: String
    ): GetFieldValuesResponse {
        log.info("Department field $fieldId returned empty values, trying departments.list endpoint")
        try {
            val deptResp = knitAdapter.getDepartmentsList(
                integration.companyId,
                integration.platform.id!!,
                mapPlatformIdToKnitAppId(integration.platform.name)
            )
            // Only use departments response if it was successful and has data
            if (deptResp.success && !deptResp.data?.departments.isNullOrEmpty()) {
                val fieldValues = deptResp.data.departments.map { dept ->
                    FieldValues(
                        id = dept.id,
                        label = dept.name
                    )
                }
                return GetFieldValuesResponse(
                    success = true,
                    data = Field(fields = fieldValues),
                    responseCode = deptResp.responseCode
                )
            } else {
                log.warn("Departments list endpoint returned empty response")
                return GetFieldValuesResponse(
                    success = false,
                    error = ErrorResponse(msg = "No departments found or error occurred"),
                    responseCode = deptResp.responseCode
                )
            }
        } catch (e: Exception) {
            log.error("Error calling departments list endpoint", e)
            return GetFieldValuesResponse(
                success = false,
                error = ErrorResponse(msg = e.message ?: "Unknown error"),
                responseCode = null
            )
        }
    }

    fun getExternalEnumValues(
        integration: JpaCompanyIntegration,
        externalFields: List<FieldData>?,
    ): List<JpaExternalPlatformValues>? {
        val cachedExternalValues = externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integration.id!!)
        val cacheEnumMapping = cachedExternalValues.associateBy { it.fieldId }
        val enumExternalFields =
            externalFields?.filter { it.fieldId != null && (it.dataType == "ENUM" || it.fieldId.contains("department")) }

        val fetchEnumValues = enumExternalFields?.filter {
            !cacheEnumMapping.containsKey(it.fieldId) || (cacheEnumMapping.containsKey(it.fieldId) && Duration.between(
                cacheEnumMapping[it.fieldId]?.updatedOn,
                LocalDateTime.now()
            ).toDays() >= 1)
        }?.map {
            Pair(it.fieldId!!, it.mappedKey)
        }
        if (fetchEnumValues.isNullOrEmpty()) {
            return cachedExternalValues
        }
        // Update caching for enum values
        val resultMap = runBlocking {
            fetchEnumValues.map { (fieldId, mappedKey) ->
                async {
                    // First try the regular field.values endpoint
                    var resp = knitAdapter.getFieldValues(
                        integration.companyId,
                        integration.platform.id!!,
                        mapPlatformIdToKnitAppId(integration.platform.name),
                        fieldId
                    )
                    
                    // If it's a department field and the response is empty/null, try the departments.list endpoint
                    if (fieldId.contains("department") && (resp.data?.fields.isNullOrEmpty())) {
                        val deptResp = getDepartmentFieldValues(integration, fieldId)
                        // Only use the new response if it's successful and has data
                        if (deptResp.success && !deptResp.data?.fields.isNullOrEmpty()) {
                            resp = deptResp
                        } else {
                            log.info("Falling back to original field values response for department field $fieldId")
                        }
                    }
                    
                    if (resp.success) fieldId to (resp to mappedKey) else null
                }
            }.awaitAll()
                .filterNotNull()
                .toMap()
        }
        if (resultMap.isEmpty()) {
            return cachedExternalValues
        }
        val updatedExternalPlatformValues = resultMap.map { (fieldId, result) ->
            val updatedValues = result.first.data?.fields?.map { objectMapper.writeValueAsString(it) }
            if (cacheEnumMapping.containsKey(fieldId)) {
                cacheEnumMapping[fieldId]?.let {
                    it.values = updatedValues
                    it.updatedOn = LocalDateTime.now()
                    it
                }
            } else {
                JpaExternalPlatformValues(
                    fieldId = fieldId,
                    integrationId = integration.id!!,
                    mappedKey = result.second,
                    values = updatedValues
                )
            }
        }
        externalPlatformValuesRepository.saveAll(updatedExternalPlatformValues)
        return externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integration.id!!)
    }

    fun getFixedEnumValues(
        externalPlatformValues: List<JpaExternalPlatformValues>?,
        integrationId: Long,
        thirdPartyFields: List<UnmappedField>,
    ): List<JpaExternalPlatformValues> {
        val fixedEnumMappings = mapOf(
            "profile.gender" to enumValues<Gender>(),
            "profile.maritalStatus" to enumValues<MaritalStatus>()
        )
        val dynamicEnumKeys = externalPlatformValues?.map { it.mappedKey }?.toSet() ?: emptySet()
        val mappedKeyToFieldId = thirdPartyFields.filter { !it.key.isNullOrBlank() }.associate { it.key to it.fieldId }

        val updatedEnumValues = fixedEnumMappings
            .filterKeys { it !in dynamicEnumKeys }
            .map { (key, enumValues) ->
                JpaExternalPlatformValues(
                    mappedKey = key,
                    integrationId = integrationId,
                    fieldId = mappedKeyToFieldId[key] ?: key,
                    values = enumValues.map { v ->
                        objectMapper.writeValueAsString(
                            mapOf(
                                "id" to v.name,
                                "label" to v.name
                            )
                        )
                    }
                )
            }

        return (externalPlatformValues ?: emptyList()) + updatedEnumValues
    }
    @Transactional
    fun handleFieldMappingsOnDisconnection(integrationId: Long) {
        log.info("Unmapped entity mappings and deleting field mappings by integrationId: $integrationId")

        try {
            updateAndSaveEntityMappingsOnDisconnection(integrationId)
            updateAndSaveEnumValuesOnDisconnection(integrationId)
        } catch (e: Exception) {
            log.error("[HandleFieldMappingsOnDisconnection] Throw exception: ${e.message} ", e)
        }
    }

    private fun updateAndSaveEntityMappingsOnDisconnection(integrationId: Long) {
        val entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        val updatedEntityMappings = entityMappings.map {
            it.apply {
                isEnabled = false
                status = LegalMappingStatus.UNMAPPED
            }
        }

        if (updatedEntityMappings.isNotEmpty()) {
            legalEntityMappingRepository.saveAll(updatedEntityMappings)
        }
    }

    private fun updateAndSaveEnumValuesOnDisconnection(integrationId: Long) {
        val externalPlatformValues = externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId)
        val updatedExternalPlatformValues = externalPlatformValues.map {
            it.apply {
                isDeleted = true
            }
        }

        if (updatedExternalPlatformValues.isNotEmpty()) {
            externalPlatformValuesRepository.saveAll(updatedExternalPlatformValues)
        }
    }

    private fun restoreArchivedReceivedEventsForEntityRetry(receivedEvents: List<JpaReceivedEventArchive>): List<JpaReceivedEvent> {
        return receivedEvents.map { event ->
            JpaReceivedEvent(
                eventId = event.eventId,
                syncId = event.syncId,
                integrationId = event.integrationId,
                eventType = event.eventType,
                syncDataType = event.syncDataType,
                errors = event.errors,
                identifiervalue = event.identifiervalue,
                receivedTime = event.receivedTime,
                data = event.data,
                confirmedByUser = event.confirmedByUser,
                processed = false,
                isEntityEnabled = true,
                entityId = event.entityId,
                entityCountry = event.entityCountry
            )
        }
    }

    fun getKnitFieldsV2(jpaCompanyIntegration: JpaCompanyIntegration): List<FieldMappingV2> {
        val knitFields = getKnitFields(
            jpaCompanyIntegration.id!!,
            jpaCompanyIntegration.companyId,
            jpaCompanyIntegration
        )
        val externalPlatformValues = getExternalEnumValues(jpaCompanyIntegration, knitFields)
        val thirdPartyFields = formatKnitFields(knitFields)
        val updatedExternalPlatformValues =
            getFixedEnumValues(externalPlatformValues, jpaCompanyIntegration.id!!, thirdPartyFields)
        val unmappedFieldsOutput = formatUnmappedFields(
            updatedExternalPlatformValues,
            thirdPartyFields,
            jpaCompanyIntegration
        )
        
        val baseFields = unmappedFieldsOutput.map { FieldMappingV2(
            it.key,
            it.label,
            false,
            if (it.children != null) it.children.map { child ->
                FieldMappingV2(
                    child.key,
                    child.label,
                    false,
                    emptyList()
                )
            } else emptyList<FieldMappingV2>()
        ) }
        
        // Add Hibob bank account fields if platform is Hibob
        return if (jpaCompanyIntegration.platform.name.equals("Hibob", ignoreCase = true)) {
            baseFields + getHibobBankAccountFields()
        } else {
            baseFields
        }
    }

    private fun getHibobBankAccountFields(): List<FieldMappingV2> {
        return listOf(
            FieldMappingV2("bankAccountsTable.bankAccountType", "Bank Table - Bank Account Type", false, listOf(
                FieldMappingV2("Checking", "Checking", false, emptyList()),
                FieldMappingV2("Savings", "Savings", false, emptyList())
            )),
            FieldMappingV2("bankAccountsTable.routingNumber", "Bank Table - Routing Number", false, emptyList()),
            FieldMappingV2("bankAccountsTable.accountNickname", "Bank Table - Account Nickname", false, emptyList()),
            FieldMappingV2("bankAccountsTable.accountNumber", "Bank Table - Account Number", false, emptyList()),
            FieldMappingV2("bankAccountsTable.bankName", "Bank Table - Bank Name", false, emptyList()),
            FieldMappingV2("bankAccountsTable.branchAddress", "Bank Table - Branch Address", false, emptyList()),
            FieldMappingV2("bankAccountsTable.bicOrSwift", "Bank Table - BIC/SWIFT", false, emptyList()),
            FieldMappingV2("bankAccountsTable.iban", "Bank Table - IBAN", false, emptyList()),
            FieldMappingV2("bankAccountsTable.allocation", "Bank Table - Allocation", false, listOf(
                FieldMappingV2("percent", "Percent", false, emptyList()),
                FieldMappingV2("amount", "Amount", false, emptyList()),
                FieldMappingV2("remaining", "Remaining", false, emptyList())
            )),
            FieldMappingV2("bankAccountsTable.amount", "Bank Table - Amount", false, emptyList()),
            FieldMappingV2("bankAccountsTable.useForBonus", "Bank Table - Use For Bonus", false, emptyList()),
            FieldMappingV2("bankAccountsTable.id", "Bank Table - ID", false, emptyList()),
            FieldMappingV2("bankAccountsTable.changedBy", "Bank Table - Changed By", false, emptyList())
        )
    }

    fun getIntegrationFieldsMappingProfile(
        entityId: Long,
        integrationId: Long,
    ): IntegrationFieldsMappingOutputV2 {
        // Fetch integration and validate
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
        val companyId = integration.companyId

        // Fetch legal entity and validate
        log.info("Fetching legal entity and company integration")
        val matchedLegalEntity = newCompanyServiceAdapter.getLegalEntities(companyId)
            .find { it.id == entityId }
            ?: throw EntityNotFoundException("Not found legal entity with entityId=$entityId")

        return try {
            log.info("Attempting to fetch field mapping profile from field-mapping-service")

            // Get or create profile
            val profile = getOrCreateProfile(companyId, entityId, integrationId, matchedLegalEntity, integration)
            val mappingStatus = determineMappingStatus(profile)

            val sourceFields = getKnitFieldsV2(integration)

            val targetFields = getOnboardDataSpecs(matchedLegalEntity, companyId)
                .map { dataSpec ->
                    val builder = FieldMappingV2.newBuilder()
                        .key(dataSpec.key)
                        .label(dataSpec.label)
                        .isRequired(dataSpec.required)
                    val children = dataSpec.valuesList.map { value ->
                        FieldMappingV2.newBuilder()
                            .key(value)
                            .label(value)
                            .build()
                    }
                    builder.children(children).build()
                }
            // Build and return response
            dataMapper.map(
                profileId = UUID.fromString(profile.id),
                integrationId = integrationId,
                matchedLegalEntity = matchedLegalEntity,
                mappingStatus = mappingStatus.name,
                sourceFields = sourceFields,
                targetFields = targetFields,
                companyId = companyId
            )
        } catch (e: Exception) {
            log.error("Error fetching field mapping profile from field-mapping-service: ${e.message}", e)
            IntegrationFieldsMappingOutputV2.newBuilder().build()
        }
    }

    fun getIntegrationFieldsMappingContractorProfile(
        integrationId: Long,
    ): IntegrationFieldsMappingContractorOutput {
        // Fetch integration and validate
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
        val companyId = integration.companyId

        return try {
            log.info("Attempting to fetch field mapping profile from field-mapping-service")

            // Get or create profile
            val profile = getOrCreateProfile(companyId, null, integrationId,  null, integration)
            val mappingStatus = determineMappingStatus(profile)

            val sourceFields = getKnitFieldsV2(integration)

            val targetFields = getOnboardDataSpecs(null, companyId)
                .map { dataSpec ->
                    val builder = FieldMappingV2.newBuilder()
                        .key(dataSpec.key)
                        .label(dataSpec.label)
                        .isRequired(dataSpec.required)
                    val children = dataSpec.valuesList.map { value ->
                        FieldMappingV2.newBuilder()
                            .key(value)
                            .label(value)
                            .build()
                    }
                    builder.children(children).build()
                }
            // Build and return response
            dataMapper.map(
                profileId = UUID.fromString(profile.id),
                integrationId = integrationId,
                mappingStatus = mappingStatus.name,
                sourceFields = sourceFields,
                targetFields = targetFields,
                companyId = companyId
            )
        } catch (e: Exception) {
            log.error("Error fetching field mapping profile from field-mapping-service: ${e.message}", e)
            IntegrationFieldsMappingContractorOutput.newBuilder().build()
        }
    }

    /**
     * Gets an existing profile or creates a new one if none exists
     */
    private fun getOrCreateProfile(
        companyId: Long,
        entityId: Long?,
        integrationId: Long,
        legalEntity: CompanyOuterClass.LegalEntity?,
        integration: JpaCompanyIntegration
    ):  Profile{
        // Get all profiles for the company
        val profiles = fieldMappingServiceAdapter.listProfiles(companyId)

        if(entityId == null && legalEntity == null){
            log.info("Fetching profile for contractor field mapping for companyId=$companyId")
            return profiles.profilesList.firstOrNull { profile ->
                // Check if the profile has config data for this contractType and integration
                profile.configMap.fieldsMap["contractType"]?.stringValue == Contract.ContractType.CONTRACT_TYPE_FREELANCER.toString() &&
                        profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
            } ?: createNewProfile(companyId, entityId, integrationId, legalEntity, integration)
        }

        // Find the profile for this entity and integration
        val profile = profiles.profilesList.firstOrNull { profile ->
            // Check if the profile has config data for this entity and integration
            profile.configMap.fieldsMap["entityId"]?.stringValue == entityId?.toString() &&
                    profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
        } ?: createNewProfile(companyId, entityId, integrationId, legalEntity, integration)

        //update the status of the mapping only if entityId is not null
        if (entityId != null) {
            val legalEntityMapping =
                    legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId).orElseThrow {
                        EntityNotFoundException("Not found legal entity mapping for entityId $entityId")
                    }
                val status = determineMappingStatus(profile)
                log.info("Legal entity mapping status need to be updated from ${legalEntityMapping.status} to $status")
                if (legalEntityMapping.status != status) {
                    legalEntityMapping.status = status
                    legalEntityMappingRepository.save(legalEntityMapping)
                }
        }

        return profile
    }

    /**
     * Creates a new profile for the entity and integration
     */
    private fun createNewProfile(
        companyId: Long,
        entityId: Long?,
        integrationId: Long,
        legalEntity: CompanyOuterClass.LegalEntity?,
        integration: JpaCompanyIntegration
    ): Profile {
        log.info("No field mapping profile found for entityId=$entityId and integrationId=$integrationId, creating new profile")

        if(entityId == null && legalEntity == null){
            log.info("Creating new profile for contractor field mapping for companyId=$companyId")
            val configMap = Struct.newBuilder().putAllFields(
                mapOf(
                    "contractType" to Value.newBuilder().setStringValue(Contract.ContractType.CONTRACT_TYPE_FREELANCER.toString()).build(),
                    "integrationId" to Value.newBuilder().setStringValue(integrationId.toString()).build(),
                )
            ).build()

            val profileRequest = Profile.newBuilder()
                .setName("Contractor - ${integration.platform.name}")
                .setDescription("Profile for Contractor - ${integration.platform.name}")
                .setCompanyId(companyId)
                .setIsActive(true)
                .setConfigMap(configMap).addAllRules(emptyList()).build()

            return fieldMappingServiceAdapter.createProfile(profileRequest).profile
        }

        val configMap = Struct.newBuilder().putAllFields(
            mapOf(
                "entityId" to Value.newBuilder().setStringValue(entityId?.toString() ?: "").build(),
                "integrationId" to Value.newBuilder().setStringValue(integrationId.toString()).build(),
            )
        ).build()

        val profileRequest = Profile.newBuilder()
            .setName("${legalEntity?.legalName} - ${integration.platform.name}")
            .setDescription("Profile for ${legalEntity?.legalName} - ${integration.platform.name}")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setConfigMap(configMap).addAllRules(emptyList()).build()

        return fieldMappingServiceAdapter.createProfile(profileRequest).profile
    }

    /**
     * Determines the mapping status based on the profile and its rules
     */
    private fun determineMappingStatus(
        profile: Profile
    ): LegalMappingStatus {
        if (!profile.isActive || profile.rulesList.isEmpty()) {
            return LegalMappingStatus.UNMAPPED
        }

        // Check if any required fields are unmapped
        val hasUnmappedRequiredFields = profile.rulesList.any { rule ->
            rule.isRequired && rule.sourceField.isNullOrEmpty()
        }

        return if (hasUnmappedRequiredFields) {
            LegalMappingStatus.PARTIALLY_MAPPED
        } else {
            LegalMappingStatus.FULLY_MAPPED
        }
    }
}