package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.LeaveTypeMappingRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.TimeoffEventRepository
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.repository.model.JpaTimeoffEvent
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.sync.model.EmployeeLeaveRequest
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.utils.toTimeoffDateFormattedString
import com.multiplier.timeoff.schema.GrpcBulkRevokeTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffInput
import com.multiplier.timeoff.schema.GrpcBulkTimeOffRequest
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class TimeoffService(
    private val timeoffEventRepository: TimeoffEventRepository,
    private val leaveTypeMappingRepository: LeaveTypeMappingRepository,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val timeoffServiceAdapter: TimeoffServiceAdapter,
    private val timeoffDataRepository: PlatformTimeoffIntegrationRepository,
    private val platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository,
) {

    private val log = KotlinLogging.logger {}

    fun processPendingTimeoffEvents() {
        log.info ("Processing approved timeoff events...")
        val pendingTimeoffEvents = timeoffEventRepository.findAllByShouldProcess(true)

        pendingTimeoffEvents.forEach { event ->
            try {
                processEvent(event)

                event.processed = true
                event.shouldProcess = false
                timeoffEventRepository.save(event)

                log.info { "Processed timeoff event with externalId: ${event.externalId}" }
            } catch (e: Exception) {
                log.error(e) { "Failed to process timeoff event with externalId: ${event.externalId}" }
            }
        }
    }

    fun revokeTimeoffEvent(event: JpaTimeoffEvent, companyId: Long) {
        val request = GrpcBulkRevokeTimeOffRequest.newBuilder()
            .setCompanyId(companyId)
            .addExternalTimeOffIds(event.externalId)
            .build()
        val resp = timeoffServiceAdapter.bulkRevokeTimeoffs(request)
        return
    }

    private fun processEvent(event: JpaTimeoffEvent) {
        // Fetch the mappings, do the mappings, call the appropriate GRPC :
        // https://www.notion.so/usemultiplier/Tech-Spec-Integrations-support-Timeoff-37bdf48e3e504dfe8fea86ce26dcba54#f433fab1def94b77a71800b8cdeda8bf
        //dont forget to add the internal_id in case of adding a new timeoff
        log.info { "Started processing timeoff event: ${event.toString()}" }

        try {
            val objectMapper = jacksonObjectMapper()
            val leaveRequest = objectMapper.readValue(event.timeoffData, EmployeeLeaveRequest::class.java)
            if (leaveRequest.status != Status.APPROVED) {
                log.info { "Ignore timeoff event ${event.id} since its status is not APPROVED" }
                return
            }


            val jpaCompanyIntegration = companyIntegrationRepository.findById(event.integrationId!!.toLong()).get()
            if (platformTimeoffDataRepository.existsByExternalTimeoffIdAndIntegrationId(event.externalId, jpaCompanyIntegration.id!!)) {
                log.warn { "Ignore timeoff event ${event.id} since it is already processed" }
                return
            }

            val contract = platformContractIntegrationRepository.findFirstByPlatformEmployeeIdAndPlatformIdAndProviderIdAndIntegrationId(
                event.employeeId,
                jpaCompanyIntegration.platform.id!!,
                jpaCompanyIntegration.provider.id!!,
                jpaCompanyIntegration.id!!
            )
            if (contract == null) {
                log.warn { "Ignore timeoff event ${event.id} since the linked contract is not found" }
                return
            }
            val leaveTypeMapping = leaveTypeMappingRepository.findByIntegrationIdAndExternalTypeId(
                event.integrationId,
                leaveRequest.leaveType?.id!!
            )

            if (leaveTypeMapping.isNullOrEmpty() || leaveTypeMapping.size > 1) {
                throw IntegrationInternalServerException("There is no matching leave types mapping or more than 2 mappings from external to internal type")
            }

            val inputLeaveTypeKey = leaveTypeMapping.first().internalTypeName

            //TODO: account for cases where unit is not DAYS but HOURS or anything else
            val timeOffInput = GrpcBulkTimeOffInput.newBuilder()
                .setExternalTimeOffId(event.externalId)
                .setEmployeeId(event.employeeId)
                .setType(inputLeaveTypeKey)
                .setStartDate(leaveRequest.startDate.toTimeoffDateFormattedString())
                .setEndDate(leaveRequest.endDate.toTimeoffDateFormattedString())
                .setNoOfDays(leaveRequest.amount.toString())
                .setDescription(leaveRequest.note ?: "")
                .build()

            val request = GrpcBulkTimeOffRequest.newBuilder()
                .setCompanyId(jpaCompanyIntegration.companyId)
                .addAllInputs(mutableListOf(timeOffInput))
                .build()
            val response = timeoffServiceAdapter.bulkUpsertTimeOffs(request)
            if (response.success) {
                event.internalId = response.getItems(0).timeOffId.toString()
                timeoffEventRepository.save(event)
                addTimeoffDataToCache(
                    event.integrationId,
                    contract.contractId,
                    event.employeeId,
                    response.getItems(0).timeOffId,
                    event.externalId
                )
            } else {
                log.error { "Error processing timeoff event ${event.id} with response ${response.getItems(0).errorsList.joinToString(", ")}" }
            }
        } catch (e: Exception) {
            log.error("Exception while processing timeoff event ${event.id}", e)
        }
    }

    fun addTimeoffDataToCache(
        integrationId: Long,
        contractId: Long,
        employeeId: String,
        internalTimeoffId: Long,
        externalTimeoffId: String
    ): JpaPlatformTimeoffIntegration {
        val timeoffData = JpaPlatformTimeoffIntegration(
            integrationId = integrationId,
            contractId = contractId,
            employeeId = employeeId,
            internalTimeoffId = internalTimeoffId,
            externalTimeoffId = externalTimeoffId
        )
        return timeoffDataRepository.save(timeoffData)
    }
}