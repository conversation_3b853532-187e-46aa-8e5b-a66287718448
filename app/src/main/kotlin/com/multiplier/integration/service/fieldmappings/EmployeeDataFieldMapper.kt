package com.multiplier.integration.service.fieldmappings

import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import mu.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class EmployeeDataFieldMapper(
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val featureFlagService: FeatureFlagService,
    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter,
) {
    fun mapEmployeeData(
        eventData: Map<String, Any>,
        legalEntityId: Long,
        onboardingType: OnboardingType,
        platformId: Long,
        integrationId: Long,
        companyId: Long,
    ): Map<String, String> {
        val ffAttributes = mapOf(
            "company" to companyId,
            "platform" to platformId
        )

        checkLegalEntityMapping(legalEntityId, integrationId)
        val mappings =
            if (featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, ffAttributes)) {
                mapEmployeeDataUsingFieldMappingService(legalEntityId, eventData, companyId, integrationId)
          } else {
              log.warn { "Field mapping feature not enabled for companyId=$companyId and platformId=$platformId" }
              return emptyMap()
          }


        val overrides = getOverrides(mappings, onboardingType, legalEntityId)

        return mappings + overrides
    }

    private fun mapEmployeeDataUsingFieldMappingService(
        legalEntityId: Long,
        eventData: Map<String, Any>,
        companyId: Long,
        integrationId: Long
    ): Map<String, String> {
        log.info("Using field mapping service for companyId=$companyId, integrationId=$integrationId")

        try {
            // Get field mapping profiles for the company
            val profiles = fieldMappingServiceAdapter.listProfiles(companyId)

            // Find the profile for this integration
            val profile = profiles.profilesList.firstOrNull { profile ->
                profile.configMap.fieldsMap["entityId"]?.stringValue == legalEntityId.toString() &&
                        profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
            } ?: throw IllegalStateException("No field mapping profile found for companyId=$companyId")

            // Execute the field mapping
            val result = fieldMappingServiceAdapter.executeMapping(profile.id, eventData)

            return result.transformedData.fieldsMap.mapValues {
                it.value.allFields.values.firstOrNull()?.toString().orEmpty()
            }
        } catch (e: Exception) {
            log.error(e) { "Error while mapping data using field mapping service: ${e.message}" }
            throw IllegalStateException(e.message)
        }
    }

    private fun checkLegalEntityMapping(
        legalEntityId: Long,
        integrationId: Long
    ) {
        val legalEntityMapping = legalEntityMappingRepository.findByIntegrationIdAndEntityId(
            integrationId, legalEntityId
        ).orElseThrow { RuntimeException("Not found legal entity mapping") }

        if (!legalEntityMapping.isEnabled) {
            throw RuntimeException("Legal entity field mapping for entity $legalEntityId not enabled for GP sync")
        }
    }

    private fun getOverrides(eventData: Map<String, String>, onboardingType: OnboardingType, legalEntityId: Long): Map<String, String> {
        val replaceEmailWithWorkEmail = if (eventData["email"].isNullOrEmpty() && !eventData["workEmail"].isNullOrEmpty())
            eventData + mapOf(
                "email" to (eventData["workEmail"] ?: ""),
                "workEmail" to "",
            )
        else emptyMap()

        val legalEntityIdForHris = if (onboardingType == OnboardingType.HRIS_PROFILE_DATA)
            mapOf("legalEntityId" to legalEntityId.toString())
        else emptyMap()

        return replaceEmailWithWorkEmail + legalEntityIdForHris
    }
}