package com.multiplier.integration.utils

import com.google.protobuf.Timestamp
import com.multiplier.integration.adapter.model.knit.Compensation
import com.multiplier.integration.adapter.model.knit.CompensationData
import com.multiplier.integration.adapter.model.knit.CompensationItem
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.model.EmploymentStatus
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import dev.merge.client.hris.models.CategoryEnum
import dev.merge.client.hris.models.GenderEnum
import dev.merge.client.hris.models.MaritalStatusEnum
import org.apache.commons.validator.GenericValidator
import org.apache.commons.validator.routines.EmailValidator
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME
import java.util.*
import com.neovisionaries.i18n.CountryCode as IsoCountryCode

fun Member.MaritalStatus.toMergeDevType(): MaritalStatusEnum? =
    when (this) {
        Member.MaritalStatus.SINGLE -> MaritalStatusEnum.SINGLE
        Member.MaritalStatus.MARRIED -> MaritalStatusEnum.MARRIED_FILING_JOINTLY
        else -> null
    }

fun MaritalStatusEnum?.toMultiplierType(): Member.MaritalStatus =
    when (this) {
        MaritalStatusEnum.SINGLE -> Member.MaritalStatus.SINGLE
        MaritalStatusEnum.MARRIED_FILING_JOINTLY -> Member.MaritalStatus.MARRIED
        MaritalStatusEnum.MARRIED_FILING_SEPARATELY -> Member.MaritalStatus.MARRIED
        else -> Member.MaritalStatus.NULL
    }

fun CategoryEnum?.toMultiplierType(): PlatformCategory? =
    when (this) {
        CategoryEnum.HRIS -> PlatformCategory.HRIS
        CategoryEnum.ACCOUNTING -> PlatformCategory.ACCOUNTING
        CategoryEnum.ATS -> PlatformCategory.ATS
        CategoryEnum.CRM -> PlatformCategory.CRM
        CategoryEnum.TICKETING -> PlatformCategory.TICKETING
        else -> null
    }

fun Timestamp.toLocalDateTime(): LocalDateTime =
    LocalDateTime.ofInstant(
        Instant.ofEpochSecond(this.seconds, this.nanos.toLong()), ZoneId.systemDefault())

fun Timestamp.toLocalDate(): LocalDate =
    LocalDate.ofInstant(
        Instant.ofEpochSecond(this.seconds, this.nanos.toLong()), ZoneId.systemDefault())

fun LocalDateTime.toOffsetDateTime(): OffsetDateTime =
    this.atZone(ZoneId.systemDefault()).toOffsetDateTime()

fun Gender.toMergeDevType(): GenderEnum =
    when (this) {
        Gender.MALE -> GenderEnum.MALE
        Gender.FEMALE -> GenderEnum.FEMALE
        Gender.UNSPECIFIED -> GenderEnum.PREFER_NOT_TO_DISCLOSE
        else -> GenderEnum.MERGE_NONSTANDARD_VALUE
    }

fun GenderEnum?.toMultiplierType(): Gender =
    when (this) {
        GenderEnum.MALE -> Gender.MALE
        GenderEnum.FEMALE -> Gender.FEMALE
        GenderEnum.PREFER_NOT_TO_DISCLOSE -> Gender.UNSPECIFIED
        else -> Gender.NULL_GENDER
    }

fun String.toCapitalize(): String =
    this.replaceFirstChar {
        if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString()
    }

fun <K, V> Map<K, V?>.filterValuesNotNull(): Map<K, V> =
    mapNotNull { (key, value) -> value?.let { key to it } }.toMap()

fun CountryCode.getFullName(): String? {
    val alpha3ToAlpha2 =
        Locale.getISOCountries().associateBy({ Locale("", it).isO3Country }, { it })
    return alpha3ToAlpha2[this.name]?.let { Locale("", it).displayCountry }
}

fun String.emailOrNull(): String? {
    if (!EmailValidator.getInstance().isValid(this)) return null
    return this
}

fun String?.toLocalDateTime(): LocalDateTime? {
    if (this.isNullOrBlank()) return null

    if (GenericValidator.isDate(this, "yyyy-MM-dd", true)) {
        return LocalDate.parse(this).atStartOfDay()
    }

    return LocalDateTime.parse(this, ISO_LOCAL_DATE_TIME)
}
fun mapCountryNameToMultiplierCountryCode(countryCode: String?): String {
    if (countryCode == null) {
        return ""
    }
    val country = IsoCountryCode.getByAlpha2Code(countryCode)
    return if (country == null) {
            return ""
    } else {
        country.alpha3
    }
}

inline fun <reified T> Any?.asListOfType(): List<T>? {
    if (this is List<*> && all { it is T }) @Suppress("UNCHECKED_CAST") return this as List<T>
    else return null
}

inline fun <reified K, reified V> Any?.asMapOfType(): Map<K, V>? {
    if (this is Map<*, *> && all { it.key is K } && all { it.value is V })
        @Suppress("UNCHECKED_CAST") return this as Map<K, V>
    else return null
}

fun Date?.toGrpcTimestamp(): Timestamp {
    if (this != null) {
        val milliseconds = this.time
        val seconds = milliseconds / 1000
        val nanos = ((milliseconds % 1000) * 1000000).toInt()

        return Timestamp.newBuilder().setSeconds(seconds).setNanos(nanos).build()
    } else {
        return Timestamp.getDefaultInstance()
    }
}

fun EmploymentStatus.toMemberStatus(): Member.MemberStatus {
    return when (this) {
        EmploymentStatus.ACTIVE -> Member.MemberStatus.ACTIVE
        EmploymentStatus.INACTIVE -> Member.MemberStatus.DELETED
        else -> Member.MemberStatus.UNRECOGNIZED
    }
}

fun Date.convertDateToLocalDate(): LocalDate {
    val instant = this.toInstant()
    val zonedDateTime = instant.atZone(ZoneId.systemDefault())
    return zonedDateTime.toLocalDate()
}

fun LocalDate?.toGrpcTimestamp(): Timestamp =
    if (this != null) {
        Timestamp.newBuilder()
            .setSeconds(this.atStartOfDay(ZoneOffset.UTC).toEpochSecond())
            .setNanos(this.atStartOfDay(ZoneOffset.UTC).nano)
            .build()
    } else {
        Timestamp.getDefaultInstance()
    }

fun convertToLocalDateTimeViaInstant(dateToConvert: Date): LocalDateTime {
    return dateToConvert.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDateTime()
}

fun LocalDate.mapToGoogleDate(): com.google.type.Date {
    val year = this.year
    val month = this.monthValue
    val day = this.dayOfMonth

    return com.google.type.Date.newBuilder()
        .setYear(year)
        .setMonth(month)
        .setDay(day)
        .build()
}

fun String?.toLocalDate(): LocalDate? {
    if (this == null) {
        return null
    }
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
    return LocalDate.parse(this, formatter)
}

fun String?.toDate(): Date? {
    if (this == null) {
        return null
    }
    val formatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
    return formatter.parse(this)
}

fun Date?.toFormattedString(): String {
    if (this == null) {
        return ""
    }
    val formatter = SimpleDateFormat("yyyy-MM-dd")
    return formatter.format(this)
}

fun Date?.toTimeoffDateFormattedString(): String {
    if (this == null) {
        return ""
    }
    val formatter = SimpleDateFormat("dd/MM/yyyy")
    return formatter.format(this)
}

fun Compensation.toEmployeeCompensationData(): CompensationData {
    return CompensationData(
        fixed = this.fixed?.map {
            CompensationItem(it.type, it.amount.toString(), it.currency, it.payPeriod, it.frequency.toString(), it.startDate, it.endDate, it.percentage.toString(), it.planId)
        },
        variable = this.variable?.map {
            CompensationItem(it.type, it.amount.toString(), it.currency, it.payPeriod, it.frequency.toString(), it.startDate, it.endDate, it.percentage.toString(), it.planId)
        }
    )
}

fun String.toEmployeeCompensationData(): CompensationData {
    return DataMapper.objectMapper.readValue(this, CompensationData::class.java)
}

fun mapPlatformIdToKnitAppId(input: String): String {
    // DB ( customer_integration.platform ) values are "Workday" and "Hibob", but Knit values are "workday" and
    // "hibob"
    return when (input) {
        "Workday" -> "workday"
        "Hibob" -> "hibob"
        "BambooHR" -> "bamboohr"
        "Personio" -> "personio"
        "SuccessFactors" -> "successfactors"
        "Paycor" -> "paycor"
        "Oracle HCM" -> "oracle-hcm"
        "Darwinbox" -> "darwinbox"
        "Paylocity" -> "paylocity"
        "Expensify" -> "expensify"
        "Zoho People" -> "zoho-people"
        "GreytHR" -> "greythr"
        "Freshteam" -> "freshteam"
        "Namely" -> "namely"
        "Humaans" -> "humaans"
        "Sage" -> "sage-hris"
        "Paychex" -> "paychex"
        "Keka HR" -> "keka"
        else -> ""
    }
}

fun mapPlatformIdToMergeDevAppId(input: String): String {
    // DB ( customer_integration.platform ) values are "Quickbooks" and "Xero", but mergeDev values are "quickbooks-online" and
    // "xero"
    return when (input) {
        "Quickbooks Online" -> "quickbooks-online"
        "Xero" -> "xero"
        "NetSuite" -> "netsuite"
        else -> "quickbooks-online"
    }
}