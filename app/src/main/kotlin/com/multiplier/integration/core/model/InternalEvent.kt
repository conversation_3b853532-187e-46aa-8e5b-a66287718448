package com.multiplier.integration.core.model

interface ContractEvent {
    val eventLogId: String
}

data class ContractStatusUpdateEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractDocumentUpdateEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractOnboardingStatusUpdateEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractCompensationUpdateEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractSalaryDocumentUpdateEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractStartedEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractJoiningDateChangedEvent(
    override val eventLogId: String
) : ContractEvent

data class ContractWorkEmailChangedEvent(
    override val eventLogId: String
) : ContractEvent

interface MemberEvent {
    val eventLogId: String
}

data class MemberBasicDetailUpdateEvent(
    override val eventLogId: String
) : MemberEvent

data class MemberContactDetailUpdateEvent(
    override val eventLogId: String
) : MemberEvent

data class MemberBankDetailUpdateEvent(
    override val eventLogId: String
) : MemberEvent

interface PayrollEvent {
    val eventLogId: String
}

data class PayrollPayslipDocumentUploadedEvent(
    override val eventLogId: String
) : PayrollEvent

data class PayrollPayslipDocumentPublishedEvent(
    override val eventLogId: String
) : PayrollEvent

data class ContractOffboardingStatusUpdateEvent(
    override val eventLogId: String
) : ContractEvent