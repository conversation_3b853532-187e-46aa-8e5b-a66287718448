package com.multiplier.platform.utility

import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.service.BulkModule
import com.multiplier.integration.service.IntegrationInput
import com.multiplier.integration.service.IntegrationOrchestrator
import com.multiplier.platform.updater.DatabaseScript
import com.multiplier.platform.updater.DatabaseUpdater
import org.springframework.stereotype.Service

data class SFTPIntegrationInput(
    val uri: String = "",
    val uriType: URIType,
    val companyId: Long,
    val module: BulkModule,
    val entityId: Long,
)

@DatabaseScript(
    action = "integration.sftp_file_processing",
    owner = "customer-integration",
    description = "SFTP Integration for file processing",
)
@Service
class SFTPIntegrationUpdater(
    private val integrationOrchestrator: IntegrationOrchestrator
): DatabaseUpdater<SFTPIntegrationInput>() {

    override fun validate(request: SFTPIntegrationInput): Boolean {
        require(request.uri.isNotBlank()) { "Path cannot be empty or blank" }
        require(request.companyId > 0) { "companyId must be greater than 0" }
        require(request.entityId > 0) { "entityId must be greater than 0"}
        return true
    }

    override fun execute(request: SFTPIntegrationInput) {
        validate(request)
        integrationOrchestrator.handleDataIngestion(
            input = IntegrationInput(
                type = URIType.SFTP,
                uri = request.uri,
                companyId = request.companyId,
                entityId = request.entityId,
                module = request.module,
            )
        )
    }
}