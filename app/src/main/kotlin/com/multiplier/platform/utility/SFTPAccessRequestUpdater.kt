package com.multiplier.platform.utility

import com.multiplier.integration.repository.SFTPAccessRequestRepository
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import com.multiplier.integration.utils.FTPDirectoryUtil
import com.multiplier.platform.updater.DatabaseScript
import com.multiplier.platform.updater.DatabaseUpdater
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * Input data for updating SFTP access request status and directory.
 *
 * @property requestId The ID of the SFTP access request to update
 * @property status The new status to set for the request
 * @property mainSftpDirectory The main SFTP directory to set for the request (required for APPROVED status)
 */
data class SFTPAccessRequestUpdateInput(
    val requestId: Long,
    val status: SftpAccessRequestStatus,
    val mainSftpDirectory: String? = null
)

/**
 * Database script for updating SFTP access request status and directory.
 * This script allows operations to update the status of SFTP access requests
 * and set the main SFTP directory when approving a request.
 */
@DatabaseScript(
    action = "integration.sftp_access_request_update",
    owner = "customer-integration",
    description = "Update SFTP access request status and directory"
)
@Service
class SFTPAccessRequestUpdater(
    private val sftpAccessRequestRepository: SFTPAccessRequestRepository
) : DatabaseUpdater<SFTPAccessRequestUpdateInput>() {

    override fun validate(request: SFTPAccessRequestUpdateInput): Boolean {
        // Validate request ID
        require(request.requestId > 0) { "Request ID must be greater than 0" }

        // Validate directory for APPROVED status
        if (request.status == SftpAccessRequestStatus.APPROVED) {
            validateMainSftpDirectory(request)
        }

        // Validate that the request exists
        val requestExists = sftpAccessRequestRepository.findById(request.requestId).isPresent
        require(requestExists) { "SFTP access request not found with ID: ${request.requestId}" }

        return true
    }

    /**
     * Validates the main SFTP directory for an SFTP access request.
     * This method checks that the directory is not null or blank and that it's not already in use by another request.
     *
     * @param request The SFTP access request update input to validate
     * @throws IllegalArgumentException if the directory is null, blank, or already in use
     */
    private fun validateMainSftpDirectory(request: SFTPAccessRequestUpdateInput) {
        // Check that the directory is not null or blank
        require(!request.mainSftpDirectory.isNullOrBlank()) {
            "Main SFTP directory is required when status is APPROVED"
        }

        // Normalize the directory path to ensure consistent format with trailing slash
        val normalizedDirectory = FTPDirectoryUtil.normalizeDirectoryPath(request.mainSftpDirectory!!)

        val existingRequest = sftpAccessRequestRepository.findByMainSFTPDirectory(normalizedDirectory)
        // If a request with this directory exists and it's not the current request being updated,
        // then the directory is already in use
        require(existingRequest == null || existingRequest.id == request.requestId) {
            "Main SFTP directory '$normalizedDirectory' is already in use by another request"
        }
    }

    @Transactional
    override fun execute(request: SFTPAccessRequestUpdateInput) {
        // Validate input
        validate(request)

        val sftpAccessRequest = sftpAccessRequestRepository.findById(request.requestId).get()
        sftpAccessRequest.status = request.status

        // Only update directory for APPROVED status when a new directory is explicitly provided
        if (request.status == SftpAccessRequestStatus.APPROVED && request.mainSftpDirectory != null) {
            sftpAccessRequest.mainSFTPDirectory = FTPDirectoryUtil.normalizeDirectoryPath(request.mainSftpDirectory)
        }

        // Save the updated request
        sftpAccessRequestRepository.save(sftpAccessRequest)
    }
}
